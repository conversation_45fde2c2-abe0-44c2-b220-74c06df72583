#pragma once

#include <string>
#include "iniparser.hpp"
#include <boost/preprocessor.hpp>
#include <experimental/filesystem>

namespace fs = std::experimental::filesystem;

#define HELPER1(...) ((__VA_ARGS__)) HELPER2
#define HELPER2(...) ((__VA_ARGS__)) HELPER1
#define HELPER1_END
#define HELPER2_END
#define ADD_PARENTHESES_FOR_EACH_TUPLE_IN_SEQ(sequence) BOOST_PP_CAT(HELPER1 sequence,_END)

#define INI_VARIABLE(type, asType, name, section, value, help) \
type name(){ \
    auto sec = iniFile.FindSection(section); \
    if (sec) { \
        return sec->GetValue(#name, value).As##asType(); \
    } \
    else { \
        return value; \
    } \
} \

#define INI_PROP_int(name, section, val, txt) INI_VARIABLE(int, Int, name, section, val, txt)
#define INI_PROP_bool(name, section, val, txt) INI_VARIABLE(bool, Bool, name, section, val, txt)
#define INI_PROP_string(name, section, val, txt) INI_VARIABLE(std::string, String, name, section, val, txt)
#define INI_PROP_double(name, section, val, txt) INI_VARIABLE(double, Double, name, section, val, txt)

#define SINGLETON_DEFINE(name) \
static name* instance() { \
    static name cfg; \
    return &cfg; \
} \

#define INIT_PROP_TYPE(TYPE) BOOST_PP_CAT(INI_PROP_, TYPE)

#define INI_PROPERTY_API(propType, propName, group, defaultVal, description) \
    INIT_PROP_TYPE(propType)(propName, BOOST_PP_STRINGIZE(group), defaultVal, description) \

#define GENERATE_INI_PROPERTY_API(r, appName, elements) \
    INI_PROPERTY_API(BOOST_PP_TUPLE_ELEM(0, elements), BOOST_PP_TUPLE_ELEM(1, elements), BOOST_PP_TUPLE_ELEM(2, elements), BOOST_PP_TUPLE_ELEM(3, elements), BOOST_PP_TUPLE_ELEM(4, elements)) \

#define INI_SET_DEFAULT_VALUE(propName, group, defaultVal, description) \
    iniFile.SetValue(BOOST_PP_STRINGIZE(group:propName), defaultVal, description); \

#define GENERATE_INI_SET_DEFAULT(r, appName, elements) \
        INI_SET_DEFAULT_VALUE(BOOST_PP_TUPLE_ELEM(1, elements), BOOST_PP_TUPLE_ELEM(2, elements), BOOST_PP_TUPLE_ELEM(3, elements), BOOST_PP_TUPLE_ELEM(4, elements)) \

#define DEFINE_INI_CONFIG(appName, iniPath, iniElements) \
namespace appName { \
class Config { \
public: \
BOOST_PP_SEQ_FOR_EACH( \
                GENERATE_INI_PROPERTY_API, \
                appName, \
                ADD_PARENTHESES_FOR_EACH_TUPLE_IN_SEQ(iniElements) \
            ) \
SINGLETON_DEFINE(Config) \
private: \
    INI::File iniFile; \
    Config(){ \
        std::string filePath = iniPath;  \
        auto pos = filePath.find_last_of("/");  \
        if(pos != std::string::npos)  \
        {  \
            auto dir = filePath.substr(0, pos);  \
			if (!fs::is_directory(dir) || !fs::exists(dir)) \
				fs::create_directory(dir);	\
        } \
        iniFile.Load(iniPath); \
        bool updateINI = iniFile.GetTopLevelSections().empty(); \
        if (updateINI) { \
        BOOST_PP_SEQ_FOR_EACH( GENERATE_INI_SET_DEFAULT, appName, \
                ADD_PARENTHESES_FOR_EACH_TUPLE_IN_SEQ(iniElements)) \
        iniFile.Save(iniPath); \
      } \
    } \
  }; \
}
