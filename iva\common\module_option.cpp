#include "module_option.h"
#include <shared_mutex>
#include <iostream>
#include <vector>
#include <map>
#include <algorithm>

/*
* 设置模块选项 （如 日志控制等）
*
*  可以通过控制台 输入对应的命令进行切换
*							如输入 firesmoke 切换烟火日志
*/
namespace opt
{
    std::shared_mutex  mutexOption;
    /**
     * 获取选项是否开启
     * @param option 选项
     * @param defaultValue 默认值
     */
    bool getOptionEnabled(std::string_view option, bool defaultValue)
    {
        bool enabled = defaultValue;
        std::shared_lock<std::shared_mutex> lock(mutexOption);
        if(options.end() != options.find(option))
            enabled = options[option];

        return enabled;
    }

    /**
     * 设置选项
     * @param option 选项
     * @param value 设置值
     */
    void setOptionEnabled(std::string_view option, bool value)
    {
        std::lock_guard<std::shared_mutex> lock(mutexOption);
        options[option] = value;
    }

    /**
     * 解析控制台输入（打印开关控制）
     * @param input 控制台输入
     */
    void parseOption(std::string_view input)
    {
        for (auto&[key, option] : options)
        {
            if (input == key)
            {
                bool enabled = getOptionEnabled(key);
                setOptionEnabled(key, !enabled);
                std::cout << "\n\n[option] " << input << ": " << (!enabled ? "enabled": "disabled") << "\n\n"<< std::endl;
            }
        }
    }
}