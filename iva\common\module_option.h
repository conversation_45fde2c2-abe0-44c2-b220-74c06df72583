#pragma once
#include <string>
#include "module_option_table.h"

/**
 * 设置模块选项 （如 日志控制等）
 *
 *  可以通过控制台 输入对应的命令进行切换
 *							如输入 firesmoke 切换烟火日志
 */
namespace opt
{

    /**
     * 获取选项是否开启
     * @param option 选项
     * @param defaultValue 默认值
     */
    bool getOptionEnabled(std::string_view option, bool defaultValue = false);

    /**
     * 设置选项是否开启
     * @param option 选项
     * @param value 设置值
     */
    void setOptionEnabled(std::string_view option, bool value);

    /**
     * 解析控制台输入（打印开关控制）
     * @param input 控制台输入
     */
    void parseOption(std::string_view input);

}