#pragma once
#include <iostream>
#include <map>

namespace opt
{
    constexpr std::string_view SENDER_MSG_OPT = "sendermsg";         //!< 消息发送模块日志选项开关
    constexpr std::string_view FIRE_SMOKE_OPT = "firesmoke";         //!< 烟火检测模块日志选项开关
    constexpr std::string_view ROAD_BLOCK_OPT = "roadblock";         //!< 路障检测模块日志选项开关
    constexpr std::string_view THROW_AWAY_OPT = "throwaway";         //!< 抛洒物检测模块日志选项开关
    constexpr std::string_view CAMERA_OFFSET_OPT = "cameraoffset";   //!< 偏移检测模块日志选项开关
    constexpr std::string_view VIDEO_RECORD_OPT = "videorecord";     //!< 视频录制日志记录开关

    inline std::map<std::string_view, bool> options = {{SENDER_MSG_OPT,false},
                                                       {FIRE_SMOKE_OPT,false},
                                                       {ROAD_BLOCK_OPT,false},
                                                       {THROW_AWAY_OPT,false},
                                                       {CAMERA_OFFSET_OPT,false},
                                                       {VIDEO_RECORD_OPT,false}
                                                        };
}
