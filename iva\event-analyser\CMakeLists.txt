cmake_minimum_required (VERSION 3.5.2)

## 目标生成
set(TARGET_LIBRARY "event_analyser")
# 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/out/lib)

add_compile_options(-g -std=c++17 -fPIC -fstack-protector-all -Wno-unknown-pragmas -Wno-unused-variable -Werror=return-type -Wall)

# boost
set(BOOST_HOME "/opt/boost")


# 头文件
include_directories(
    ${BOOST_HOME}/include/
    ${OpenCV_INCLUDE_DIRS}
    ${PROJECT_SOURCE_DIR}/iva-log
    ${PROJECT_SOURCE_DIR}/common
	./
    include
)

# 库路径
link_directories(
    ${LIBRARY_OUTPUT_PATH}
)

# cpp
FILE(GLOB area "area/*.cpp")
FILE(GLOB channel "channel/*.cpp")
FILE(GLOB config "config/*.cpp")
FILE(GLOB detector "detector/*.cpp")
FILE(GLOB element "element/*.cpp")
FILE(GLOB map "map/*.cpp")
FILE(GLOB output "stream/output/*.cpp")
FILE(GLOB util "util/*.cpp")
FILE(GLOB src "*.cpp")

#file(GLOB_RECURSE sources CONFIGURE_DEPENDS "*.cpp")
SET(ALL_SRC ${include} ${area} ${channel} ${config} ${detector} ${element} ${map} ${output} ${util} 
    event_analyser.cpp )

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})
target_link_libraries(${TARGET_LIBRARY} ${OpenCV_LIBS} ivalog stdc++fs)

