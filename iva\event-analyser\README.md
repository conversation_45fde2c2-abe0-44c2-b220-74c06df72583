event-analyser

AI事件分析模块代码

debug测试数据：\\192.168.77.68\ai\2021AI事件分析模块设计\track_data


示例代码:
```c++
#include <iostream>
#include "event_analyser.h"

using namespace evt;
int main()
{
	// 准备 通道配置信息
	ROIInfoList roiInfoList;
	ROIInfo roiInfo;
	roiInfo.direction = { 100,0 };
	roiInfo.eventMask = 0xffffff;
	roiInfo.targetMask = 0xffffff;
	roiInfoList.emplace_back(roiInfo);

	// 初始化通道 1
	auto channel = initChannel(0, roiInfoList);
	// 设置画面尺寸
	channel->setFrameSize(1280, 720);
	// 设置事件回调
	channel->setNewEvtCallback([](EventInfo evt) {
		std::cout << "新事件: " << evt.id << " 类型: " << evt.id << std::endl;
	});

	// 准备 track数据
	std::vector<TrackList> allTracks;
	// 循环输入分析数据
	int frameIndex = 1;
	for (auto& tracks : allTracks)
	{
		channel->update(tracks, frameIndex++);
		TargetInfoList outputTargets;
		channel->getCurrentTargets(outputTargets);
		for (auto& target : outputTargets)
		{
			// 绘制数据
		}
	}
}
```