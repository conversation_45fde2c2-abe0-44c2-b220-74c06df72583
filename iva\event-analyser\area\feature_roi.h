/**
 * Project AI事件分析模块
 */

#pragma once
#include "area.h"
#include "area/feature_roi_info.h"

/**
 * 特征感兴趣区
 */
namespace evt
{

    class FeatureRoi : public Area {
    public:
        /**
         * @param info
         * @param roiID
         */
        FeatureRoi(FeatureRoiInfo info, int roiID);

        /**
         * 设置检测尺寸
         */
        void setFrameSize(int w, int h) override;

        /**
         * @brief       判断目标所在区域，当前时间是否可进行特征提取
         * @param rect  目标框
         */
        bool isEnableRetrieveFeature(Rect rect);

        /**
         * @brief       获取特征检测区底部边值
         */
        inline float getBottom(){return polygon.boundingBox().getBottom();};

    private:
        /**
         * @brief       判断目标中心点是否在特征检测区域内
         * @param rect  目标框
         */
        inline bool isContainPoint(Rect rect){return polygon.containPoint(rect.getCenterPosition());}

        /**
         * @brief       判断当前时间段是否可进行特征提取
         */
        bool isInTimePeriod();

    private:
        FeatureRoiInfo featureRoiInfo; //!< 特征感兴趣区信息
    };
}
