/**
 * Project AI事件分析模块
 */

#include "region.h"

/**
 * Region implementation
 * 
 * 子区域
 * 可以基于配置事件类型和目标类型进行灵活区域检测
 * 属于ROI对象管理
 */
namespace evt
{
	Region::Region()
	{

	}

	/**
	 * @param info 配置信息
	 */
	Region::Region(RegionInfo info, int roiID) {
		this->id = info.id;
		this->eventMask = info.eventMask;
		this->targetMask = info.targetMask;
		this->polygon = info.polygon;
		this->config = info.config;
		this->roiID = roiID;
		this->areaType = AreaType_Region;
		this->ignored = (this->eventMask.empty());
		onConfigUpdated();
	}
}