/**
 * Project AI事件分析模块
 */
#include "roi.h"
#include "util/scene_utility.h"
#include "opencv2/imgproc.hpp"

/**
 * ROI implementation
 * 
 * 感兴趣区
 */
namespace evt
{
	ROI::ROI():frameRate(DEFAULT_FRAME_RATE)
	{
		this->areaType = AreaType_ROI;
	}

	ROI::~ROI()
	{
		for (auto& region: regionList)
		{
			delete region;
		}
		for (auto& lane : laneList)
		{
			delete lane;
		}
	}

	/**
	 * 初始化ROI
	 * @param info  ROI信息
	 * @param evtMap 事件地图
	 */
	ROI::ROI(ROIInfo info): ROI(){
		this->id = info.id;
		this->roiID = info.id;
		this->eventMask = info.eventMask;
		this->targetMask = info.targetMask;
		this->polygon = info.polygon;
		this->config = info.config;
		this->onConfigUpdated();
		this->logicID = info.logicID;
		this->direction = info.direction;
		this->countLine = info.countLine;
		this->initTransformMatrix(info.referenceRect, info.referenceLine, info.referenceDistance);

		bool detectDriveAway = false;
		for (auto& regionInfo: info.childRegions)
		{
			auto region = new Region(regionInfo, this->id);
			region->direction = this->direction;
			regionList.push_back(region);
			if(!detectDriveAway)
				detectDriveAway = region->hasEventType(EventType_DriveAway);
		}
		for (auto& laneInfo : info.laneRegions)
		{
			auto lane = new Lane(laneInfo, this->id);
			if(!lane->direction.isValid())
				lane->direction = this->direction;

			laneList.push_back(lane);
			if (!detectDriveAway)
				detectDriveAway = lane->hasEventType(EventType_DriveAway);
		}

        featureRoi = std::make_shared<FeatureRoi>(info.featureRoiInfo, this->id);

		std::vector<EventType> globalTypes;
		if (detectDriveAway)
			globalTypes.push_back(EventType_DriveAway);

		initDetectors(globalTypes);
	}

	/**
	 * 初始化检测器
	 */
	void ROI::initDetectors(std::vector<EventType>& globalTypes)
	{
		for (auto& region : regionList)
		{
			region->initDetectors(globalTypes);
			region->updateROIEventMask(this->roiEventMask);
		}
		for (auto& lane : laneList)
		{
			lane->initDetectors(globalTypes);
			lane->updateROIEventMask(this->roiEventMask);
		}
		Area::initDetectors(globalTypes);
	}

	/**
	 * 清除目标
	 */
	void ROI::clearTargets() {
		Area::clearTargets();
		for (auto& region: regionList)
		{
			region->clearTargets();
		}
		for (auto& lane : laneList)
		{
			lane->clearTargets();
		}
	}

	/**
	 * 匹配区域
	 */
	Area* ROI::matchArea(TargetPtr target)
	{
		//不在roi里面，直接返回
		if (!matchTarget(target))
			return NULL;
		
		Area* matchedArea = NULL;

		bool regionMatched = false;
		bool laneMatched = false;

		//判断是否在免检region中。在，则目标不在其他子区域进行事件判断
		for (auto& region : regionList)
		{
			if (region->isIgnoreArea())
			{
				if (region->matchTarget(target))
				{
					target->updateArea(region, true, region->addTarget(target));
					regionMatched = true;
					matchedArea = region;
					break;
				}
			}
		}

		//判断是否在region
		if (!regionMatched)
		{
			for (auto& region : regionList)
			{
				if (region->isIgnoreArea() || region->isImageDetectAreaOnly())  //!< 当前区域是免检区 或者 图像全局子检测区，目标不添加到该区域中
					continue;

				if (region->matchTarget(target))
				{
					bool targetAdded = false;
					target->updateArea(region, true, targetAdded = region->addTarget(target));
					regionMatched = true;
					matchedArea = region;

					if (targetAdded)// 添加ROI事件目标列表
					{
						for (auto evtType : roiEventTypes)
						{
							if (region->hasEventType(evtType))
							{
								target->addRelatedEventType(evtType);
								roiEventTargets[evtType].push_back(target);
							}
						}
					}
					break;
				}
			}
		}

		//判断是否在lane
		for (auto& lane : laneList)
		{
			if (lane->matchTarget(target))
			{
				bool addLane = !regionMatched;
				bool targetAdded = false;
				target->updateArea(lane, addLane, addLane && (targetAdded = lane->addTarget(target)) );

				if (addLane)
				{
					matchedArea = lane;
					if (targetAdded)
					{
						for (auto evtType : roiEventTypes)// 添加ROI事件目标列表
						{
							if (lane->hasEventType(evtType))
							{
								// 应急车道由于web默认勾选拥堵了 暂时不由属性决定
								if (evtType == EventType_Jam && lane->getLaneType() == LaneType_Emergency)
									continue;

								target->addRelatedEventType(evtType);
								roiEventTargets[evtType].push_back(target);
							}
						}
					}
				}

				laneMatched = true;
				break;
			}
		}
		if (!regionMatched)
			target->updateRegion(0);
		if (!laneMatched)
			target->updateLane(0);

		bool addRoi = !regionMatched && !laneMatched;
		bool targetAdded = false;

		target->updateArea(this, addRoi, addRoi && (targetAdded = addTarget(target)));
		if (addRoi)
		{
			if (targetAdded)// 添加ROI事件目标列表
			{
				for (auto evtType : roiEventTypes)
				{
					if (this->hasEventType(evtType))
					{
						target->addRelatedEventType(evtType);
						roiEventTargets[evtType].push_back(target);
					}
				}
			}
			matchedArea = this;
		}
		return matchedArea;
	}

	/**
	 * 清除事件物体(抛洒物等)
	 */
	void ROI::clearEventObjects()
	{
		Area::clearEventObjects();
		for (auto& lane : laneList)
		{
			lane->clearEventObjects();
		}
		for (auto& region : regionList)
		{
			region->clearEventObjects();
		}
	}

	/**
	 * 匹配事件物体(抛洒物等)
	 */
	bool ROI::matchEventObjects(EventObject & object)
	{
		bool matched = false;

		//判断是否在免检region中。在，则目标不在其他子区域进行事件判断
		for (auto& region : regionList)
		{
			if (region->isIgnoreArea()) 
			{
				if (region->framePolygon.containPoint(object.rect.getCenterPosition()))
				{
					matched = true;
					break;
				}
			}
		}
		if (!matched)
		{
			for (auto& region : regionList)
			{
				if (region->isIgnoreArea())  //!< 上面已经判断过，这里不再判断
					continue;
				
				if (region->addEventObject(object))
				{
					object.matched = true;
					matched = true;
					break;
				}
			}
		}

		if (!matched)
		{
			for (auto& lane : laneList)
			{
				if (lane->addEventObject(object))
				{
					object.matched = true;
					matched = true;
					break;
				}
			}
		}

		if (Area::addEventObject(object))
		{
			matched = true;
		}
		return matched;
	}

	/**
	 * 更新目标计数线位置状态
	 */
	void ROI::updateCountLineState(TargetPtr target)
	{
		bool bottom = leftOfLine(target->getLatestPos(), countLine.begin, countLine.end, frameWidth, frameHeight);
		if (target->getCountLineState() != TargetCountLineState_Finished)
		{
			target->updateCountLineState(bottom ? TargetCountLineState_Bottom : TargetCountLineState_Top);
		}
	}

	/**
	 * 更新目标速度
	 * @param target
	 */
	int ROI::updateSpeed(TargetPtr target) {
		if (transformMatrix.empty()) return 0;

		// 处理抽帧情况 
		// 原理 是找到两个目标框的真正起始帧，进行车速的准确计算
		// 计算0-6，2-8，4-10三个索引帧之间车速，再平均
		int checkCount = 0;
		int totalTrackCount = target->getTrackCount();

		static const int CALC_COUNT = 3;
		int actCount = 0;
		double tmpSpeed[CALC_COUNT] = { 0 };
		int curIndex1 = 0;
		for (int i = 0; i < CALC_COUNT; i++)
		{
			// 从track尾部向前查找 第一个起始帧
			checkCount = 0;
			int index1 = curIndex1;
			int checkMax = TARGET_SPEED_TRACK_MIN / 2;
			while (checkCount <= checkMax && TARGET_SPEED_TRACK_MIN * 2 > (index1 + 1))
			{
				checkCount++;
				if (target->getLatestPos(index1) == target->getLatestPos(index1 + 1))
				{
					index1++;
				}
				else
					break;
			}
			curIndex1 = index1 + 1;
			int index2 = curIndex1 + TARGET_SPEED_TRACK_MIN;
			if (index2 >= (totalTrackCount-1) )
				break;
			checkCount = 0;
			bool index2Found = false;

			// 从track前部 向前查找 第二个起始帧 （考虑到 track长度问题，如果长度足够优先向前查找）
			while (index2 < totalTrackCount && index2 >(index1 + 1))
			{
				if (target->getLatestPos(index2) == target->getLatestPos(index2 + 1))
				{
					index2++;
				}
				else
				{
					index2Found = true;
					break;
				}
			}
			// 第二个起始帧 尝试向后查找
			if (!index2Found)
			{
				index2 = TARGET_SPEED_TRACK_MIN;
				while (checkCount <= checkMax && index2 > (index1 + 1))
				{
					checkCount++;
					if (target->getLatestPos(index2) == target->getLatestPos(index2 - 1))
						index2--;
					else
					{
						index2--;
						break;
					}
				}
			}

			auto p1 = target->getLatestPos(index1);
			auto p2 = target->getLatestPos(index2);

			p1.x /= frameWidth;
			p1.y /= frameHeight;
			p2.x /= frameWidth;
			p2.y /= frameHeight;

			double tranY0 = (p1.x * transformMatrix.at<double>(1, 0) + p1.y * transformMatrix.at<double>(1, 1) + transformMatrix.at<double>(1, 2))
				/ (p1.x * transformMatrix.at<double>(2, 0) + p1.y * transformMatrix.at<double>(2, 1) + transformMatrix.at<double>(2, 2));
			double tranY1 = (p2.x * transformMatrix.at<double>(1, 0) + p2.y * transformMatrix.at<double>(1, 1) + transformMatrix.at<double>(1, 2))
				/ (p2.x * transformMatrix.at<double>(2, 0) + p2.y * transformMatrix.at<double>(2, 1) + transformMatrix.at<double>(2, 2));
			double timePerFrame = 1.0 / (double)frameRate;

			tmpSpeed[i] = std::abs(((tranY0 - tranY1) * distanceScale) / ((double)(index2 - index1) * timePerFrame)) * MS_2_KMH;
			actCount++;
		}
		if (actCount == 0)
			return 0;
		double dTotal = 0;
		for (int i = 0; i < actCount; i++)
		{
			dTotal += tmpSpeed[i];
		}
		int speed = static_cast<int>(dTotal / actCount);
		target->updateSpeed(speed);
		return speed;
	}

	/**
	 * 初始化转换矩阵
	 *
	 * @param referenceRect  参考矩阵
	 * @param referenceLine  参考线
	 * @param referenceDistance  参考距离
	 */
	void ROI::initTransformMatrix(Polygon& referenceRect, Line& referenceLine, int referenceDistance)
	{
		if (referenceRect.isValid() && referenceLine.isValid())
		{
			cv::Point2f src_points[] = {
				cv::Point2f(referenceRect.getPoint(0).x, referenceRect.getPoint(0).y),
				cv::Point2f(referenceRect.getPoint(1).x, referenceRect.getPoint(1).y),
				cv::Point2f(referenceRect.getPoint(2).x, referenceRect.getPoint(2).y),
				cv::Point2f(referenceRect.getPoint(3).x, referenceRect.getPoint(3).y) };

			cv::Point2f dst_points[] = {
				cv::Point2f(referenceRect.getPoint(0).x, referenceRect.getPoint(0).y),
				cv::Point2f(referenceRect.getPoint(0).x + 100, referenceRect.getPoint(0).y),
				cv::Point2f(referenceRect.getPoint(0).x + 100, referenceRect.getPoint(0).y + 100),
				cv::Point2f(referenceRect.getPoint(0).x, referenceRect.getPoint(0).y + 100) };

			transformMatrix = cv::getPerspectiveTransform(src_points, dst_points);
			double tran_y_0 = (referenceLine.begin.x * transformMatrix.at<double>(1, 0) + referenceLine.begin.y*transformMatrix.at<double>(1, 1) + transformMatrix.at<double>(1, 2))
				/ (referenceLine.begin.x * transformMatrix.at<double>(2, 0) + referenceLine.begin.y*transformMatrix.at<double>(2, 1) + transformMatrix.at<double>(2, 2));

			//double tran_x_0 = (referenceLine.begin.x * transformMatrix.at<double>(0, 0) + referenceLine.begin.y*transformMatrix.at<double>(0, 1) + transformMatrix.at<double>(0, 2))
			//	/ (referenceLine.begin.x * transformMatrix.at<double>(2, 0) + referenceLine.begin.y*transformMatrix.at<double>(2, 1) + transformMatrix.at<double>(2, 2));

			double tran_y_1 = (referenceLine.end.x * transformMatrix.at<double>(1, 0) + referenceLine.end.y*transformMatrix.at<double>(1, 1) + transformMatrix.at<double>(1, 2))
				/ (referenceLine.end.x * transformMatrix.at<double>(2, 0) + referenceLine.end.y*transformMatrix.at<double>(2, 1) + transformMatrix.at<double>(2, 2));

			//double tran_x_1 = (referenceLine.end.x * transformMatrix.at<double>(0, 0) + referenceLine.end.y*transformMatrix.at<double>(0, 1) + transformMatrix.at<double>(0, 2))
			//	/ (referenceLine.end.x * transformMatrix.at<double>(2, 0) + referenceLine.end.y*transformMatrix.at<double>(2, 1) + transformMatrix.at<double>(2, 2));

			distanceScale = std::abs(referenceDistance / (tran_y_1 - tran_y_0));
		}
		else
		{
			distanceScale = -1;
		}
	}

	/**
	 * 设置事件地图
	 * @param map
	 */
	void ROI::setEventMap(EventMap * map)
	{
		Area::setEventMap(map);
		for (auto& lane : laneList)
		{
			lane->setEventMap(map);
		}
		for (auto& region : regionList)
		{
			region->setEventMap(map);
		}
	}

	/**
	 * 设置目标地图
	 * @param map
	 */
	void ROI::setTargetMap(TargetMap * map)
	{
		Area::setTargetMap(map);
		for (auto& lane : laneList)
		{
			lane->setTargetMap(map);
		}
		for (auto& region : regionList)
		{
			region->setTargetMap(map);
		}
	}

	/**
	 * 设置检测尺寸
	 */
	void ROI::setFrameSize(int w, int h)
	{
		Area::setFrameSize(w, h);
		for (auto& lane : laneList)
		{
			lane->setFrameSize(w, h);
		}
		for (auto& region : regionList)
		{
			region->setFrameSize(w, h);
		}

        if (featureRoi)
            featureRoi->setFrameSize(w, h);
	}

	/**
	 * 设置开始检测时间
	 */
	void ROI::setStartDetectTime(steady_clock::time_point time)
	{
		Area::setStartDetectTime(time);
		for (auto& lane : laneList)
		{
			lane->setStartDetectTime(time);
		}
		for (auto& region : regionList)
		{
			region->setStartDetectTime(time);
		}
	}

	/**
	 * 设置检测帧率
	 */
	void ROI::setFrameRate(int rate)
	{
		this->frameRate = rate;
	}

	/**
	 * 检测事件
	 */
	void ROI::detectEvents() {

		// 预处理
		for (auto region : regionList)
		{
			region->preprocessEvents();
		}
		for (auto lane : laneList)
		{
			lane->preprocessEvents();
		}
		Area::preprocessEvents();

		// 检测
		for (auto region : regionList)
		{
			region->detectEvents();
		}
		for (auto lane : laneList)
		{
			lane->detectEvents();
		}
		Area::detectEvents();
	}

	/**
	 * 获取逻辑ID
	 * @return int
	 */
	int ROI::getLogicID() {
		return logicID;
	}

	/**
	 * 获取距离映射比率
	 * @return double
	 */
	double ROI::getDistanceScale() {
		return distanceScale;
	}

	/**
	 * 获取转换矩阵
	 * @return cv:Mat
	 */
	cv::Mat ROI::getTransformMatrix() {
		return transformMatrix;
	}

	/**
	 * 设置拥堵状态
	 */
	void ROI::setJamState(bool injam)
	{
		jamState = injam;
		for (auto& region : regionList)
		{
			region->jamState = injam;
		}
		for (auto& lane : laneList)
		{
			lane->jamState = injam;
		}
	}
}
