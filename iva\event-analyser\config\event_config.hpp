﻿#ifndef _EVENT_CONFIG_H
#define _EVENT_CONFIG_H

#include "ini/inibase.h"

/*
 * 事件模块配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(evt,					"config/event_analyser.ini",
(bool, ignoreAllWhenShifted,			option, true, u8"交通流偏移 过滤所有事件")
(bool, checkEventObjectIOU,				option, true, u8"是否检测图像目标与车辆等重叠情况")
(bool, ignoreEmergencyStopWhenJam,		option, true, u8"拥堵时是否忽略应急车道停车")
(bool, checkDriveInAreaPass,			option, false, u8"是否检查驶入区域跨越动作")
(int, cacheMatchIOU,					cache, 10, u8"缓存匹配 最低IOU %")
(int, cacheMatchDistance,				cache, 100, u8"缓存匹配 最高像素距离")
(int, checkDelay,						check, 5, u8"事件检测延时(秒) 基于流恢复")
(int, camOffsetWithdrawTime,			check, 10, u8"相机偏移撤回时长(秒)")
(int, constructionStopMinSpeed,			construction, 20, u8"施工车辆判断最小速度 框移动百分比/每帧")
(int, constructionCheckFrameCount,		construction, 50, u8"施工车辆判断最小帧数")
(int, constructionVehicleMinSize,		construction, 60, u8"施工车辆最小尺寸")
(int, constructionVehicleDistance,		construction, 500, u8"施工车辆 路障距离(像素) 配置0 则此条件不生效")
(int, constructionStopMinIOU,			construction, 90, u8"施工车辆判断最小IOU %")
(int, constructionPassRatio,			construction, 10, u8"施工检测满足率 %")
(bool, accidentNoRoadblock,				accident, true, u8"事故点不能包含路障")
(int, accidentCheckDelay,				accident, 10, u8"通道恢复后事故 检测延时 (秒)")
(int, accidentStopMinSpeed,				accident, 20, u8"事故最小速度 框移动百分比/每帧")
(int, accidentPassRatio,				accident, 10, u8"事故 候选 通过比率 %")
(bool,postSafetyVestPersonEvent,        option, true, u8"是否上报反光衣的行人事件")
(bool,constructionRequireSafetyVest,    option, false, u8"施工事件是否需反光衣")
(int, minPassedBorderDistance,         search, 10, u8"过线车辆距离边界最小像素距离")
(int, maxPassedBorderDistance,         search, 70, u8"过线车辆距离边界最大像素距离")
)

#define EVENT_CFG evt::Config::instance()
#endif //_EVENT_CONFIG_H
