/**
 * Project AI事件分析模块
 */

#include "config_key.h"
#include "config/global_region_config.h"
#include <iostream>

/**
 * GlobalRegionConfig implementation
 * 
 * 全局配置
 */

namespace evt
{
	GlobalRegionConfig::GlobalRegionConfig()
	{
		
	}

	/**
	 * 获取（单例）实例对象
	 * @return GlobalRegionConfig*
	 */
	GlobalRegionConfig* GlobalRegionConfig::getInstance() {
		static GlobalRegionConfig cfg;
		return &cfg;
	}

	/**
	 * 初始化配置
	 * @param level 灵敏度等级
	 * @param cfg 参数配置
	 */
	void GlobalRegionConfig::initConfig(int level, map<string, string>& cfg) {

		configs[level] = cfg;
	}

	/**
	 * 获取参数值
	 * @param val 配置值
	 * @param key 配置项
	 * @param level 灵敏度等级
	 * @return bool 是否包含配置
	 */
	template<> 
	bool GlobalRegionConfig::getValue<int>(int& val, string key, int level) {
		auto& levMap = configs[level];
		auto& strVal = levMap[key];
		try {
			val = std::stoi(strVal);
			return true;
		}
		catch (std::invalid_argument&) {
			std::cerr << "Global config has invalid argument: " << key << " level: " << level << std::endl;
		}
		catch (std::out_of_range&) {
			std::cerr << "Global config out of range: " << key << " level: " << level << std::endl;
		}
		catch (...) {
			std::cerr << "Global config parse error: " << key << " level: " << level << std::endl;
		}
		return false;
	}

	/**
	 * 获取参数值
	 * @param val 配置值
	 * @param key 配置项
	 * @param level 灵敏度等级
	 * @return bool 是否包含配置
	 */
	template<>
	bool GlobalRegionConfig::getValue<string>(string& val, string key, int level) {

		auto& levMap = configs[level];
		auto& strVal = levMap[key];
		if (!strVal.empty())
		{
			val = strVal;
			return true;
		}
		else
		{
			return false;
		}
	}

	/**
	 * 获取参数值
	 * @param val 配置值
	 * @param key 配置项
	 * @param level 灵敏度等级
	 * @return bool 是否包含配置
	 */
	template<>
	bool GlobalRegionConfig::getValue<float>(float& val, string key, int level) {
		auto& levMap = configs[level];
		auto& strVal = levMap[key];

		try {
			val = std::stof(strVal);
			return true;
		}
		catch (std::invalid_argument&) {
			std::cerr << "Global config has invalid argument: " << key << " level: " << level << std::endl;
		}
		catch (std::out_of_range&) {
			std::cerr << "Global config out of range: " << key << " level: " << level << std::endl;
		}
		catch (...) {
			std::cerr << "Global config parse error: " << key << " level: " << level << std::endl;
		}
		return false;
	}

	/**
	 * 获取参数值
	 * @param val 配置值
	 * @param key 配置项
	 * @param level 灵敏度等级
	 * @return bool 是否包含配置
	 */
	template<>
	bool GlobalRegionConfig::getValue<double>(double& val, string key, int level) {
		auto& levMap = configs[level];
		auto& strVal = levMap[key];

		try {
			val = std::stod(strVal);
			return true;
		}
		catch (std::invalid_argument&) {
			std::cerr << "Global config has invalid argument: " << key << " level: " << level << std::endl;
		}
		catch (std::out_of_range&) {
			std::cerr << "Global config out of range: " << key << " level: " << level << std::endl;
		}
		catch (...) {
			std::cerr << "Global config parse error: " << key << " level: " << level << std::endl;
		}
		return false;
	}
}