/**
 * Project AI事件分析模块
 */

#include "accident_event_detector.h"
#include "config/event_config.hpp"

/**
 * AccidentEventDetector implementation
 * 
 * 交通事故事件检测
 */
namespace evt
{
	// 交通事故检测  路障检测频率 间隔 （秒）
	#define ACCIDENT_CHECK_ROADBLOCK_INTERVAL 30

	AccidentEventDetector::AccidentEventDetector(Area* area): AreaEventDetector(area)
	{
		lastRoadblockFoundTime = std::chrono::steady_clock::time_point();
	}

	/**
	 * @param type
	 * @return bool
	 */
	bool AccidentEventDetector::checkTargetType(TargetType type) {
		return type == TargetType::TargetType_Car
			|| type == TargetType::TargetType_Bus
			|| type == TargetType::TargetType_Truck
			|| type == TargetType::TargetType_Pedstrain;
	}

	void AccidentEventDetector::onUpdateRegionStart() 
	{
		// 区域检测开始， 重置标记
		checkInAccidentPassed = false;
		curBlockCount = 0;
	}

	/**
	 * 事故检测逻辑
	 *    （东莞PK测试简单版） 原理： 车道检测到行人，按范围匹配周边是否有静止车辆，数量满足则为事故。
	 *　　　　
	 *　　TODO待完善真实逻辑：　１.［候选］行驶异常车辆：速度异常、轨迹异常、IOU异常
	 *　　						２.［阶段确认］单车道出现点位拥堵，前方蠕动绕行情况
	 *　　						３.［阶段确认］拥堵点位出现行人
	 */
	EventPtr AccidentEventDetector::process(TargetPtr target) 
	{
		// 事故条件已满足
		if (checkInAccidentPassed)
			return nullptr;

		// 是否是行人
		if (target->getType() != TargetType::TargetType_Pedstrain)
			return nullptr;

		// 得到周围目标
		int stopCount = 0;
		auto adjacentTargets = parentArea->getAdjacentTargets(target->getLatestPos(), checkDistance);
		for (auto& t: adjacentTargets)
		{
			if (t->getType() == TargetType::TargetType_Pedstrain)
				continue;

			if (!t->hasRelatedEventType(eventType))
				continue;

			// 计算像素速度（按车框比率）
			auto velocity = t->getVelocity();
			auto curRect = t->getLatestRect();

			float speed = velocity.length();
			float width = curRect.width;
			float speedRatio = speed / width;

			// 慢行统计
			if (speedRatio* 100.f < EVENT_CFG->accidentStopMinSpeed() / 100.f)
			{
				stopCount++;
			}
		}

		if (stopCount >= checkStopCount)
		{
			checkInAccidentPassed = true;

			auto pos = target->getLatestPos();
			Rect rect{ pos.x - checkDistance, pos.y - checkDistance, 2.f * checkDistance, 2.f * checkDistance };
			curPossibleAccidentRect = rect;
		}

		return nullptr;
	}

	void AccidentEventDetector::process(EventObject object)
	{
		if (object.type == EventType_RoadBlock)
		{
			curBlockCount++;
		}
	}

	/**
	 * 区域处理结束
	 */
	EventPtr AccidentEventDetector::onUpdateRegionEnd()
	{
		AreaEventDetector::onUpdateRegionEnd();
		if (curBlockCount > 0)
		{
			lastRoadblockFoundTime = std::chrono::steady_clock::now();
		}

		if (checkInAccidentPassed && holdEvents.empty())
		{
			bool roadblockChecked = true;
			if (EVENT_CFG->accidentNoRoadblock())
			{
				if (curBlockCount > 0)
				{
					roadblockChecked = false;
				}
				else
				{
					auto& startTime = parentArea->getStartDetectTime();
					auto passedSeconds = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::steady_clock::now() - startTime).count();
					if (passedSeconds < EVENT_CFG->accidentCheckDelay())
					{
						roadblockChecked = false;
					}
					else
					{
						passedSeconds = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::steady_clock::now() - lastRoadblockFoundTime).count();
						if (passedSeconds < ACCIDENT_CHECK_ROADBLOCK_INTERVAL)
						{
							roadblockChecked = false;
						}
					}
				}
			}

			if (roadblockChecked)
			{
				auto newEvent = proposeNew(EventState_Proposal);
				accidentOccurRect = curPossibleAccidentRect;
				return newEvent;
			}
		}

		return nullptr;
	}

	/**
	 * 事件候选
	 * @param evt
	 */
	void AccidentEventDetector::onEventProposal(EventPtr evt) {

		if (EVENT_CFG->accidentNoRoadblock())	// 发现锥筒，撤销事件  
		{
			if (curBlockCount > 0)
			{
				evt->setState(EventState_None);
				evt->setStateLife(0.0f);
				return;
			}
		}

		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		if (checkInAccidentPassed)
		{
			evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);
		}

		if (evt->getPeriodCheckCount() > frameRate)
		{
			evt->getPeriodPassCount() > (int)((EVENT_CFG->accidentPassRatio() / 100.f) * (float)frameRate) ? evt->addStateLife(1) : evt->addStateLife(-1);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);
		}

		if (evt->getStateLife() > checkTime)
		{
			evt->setState(EventState_Confirming);
		}
		else if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_None);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件维持
	 * @param evt
	 */
	void AccidentEventDetector::onEventMaintaining(EventPtr evt) {
		float delta = 1.0f / (float)frameRate;

		float curLife = evt->getStateLife();
		bool released = checkAccidentRemoved();
		evt->addStateLife(released ? -delta : +delta);

		if (evt->getStateLife() > 1.0f) // checkTime
		{
			evt->setStateLife(1.0f);
		}
		if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_Released);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件解除
	 * @param evt
	 */
	void AccidentEventDetector::onEventRelease(EventPtr evt) {

		float delta = 1.0f / (float)frameRate;

		if (checkInAccidentPassed)   	       ///< 重新检测到事故状态，则重置
		{
			if (evt->getStateLife() > 0.0f)
				evt->setStateLife(0.0f);
		}
		else
		{
			bool released = checkAccidentRemoved();
			evt->addStateLife(released ? +delta : -delta);
		}

		if (evt->getStateLife() > removeTime)
		{
			evt->setState(EventState_Removed);
			evt->setStateLife(0.0f);
		}
		else if (evt->getStateLife() + checkTime < 0)
		{
			evt->setState(EventState_Maintaining);
			evt->setStateLife(0.0f);
		}

	}

	/**
	 * 检测解除状态
	 */
	bool AccidentEventDetector::checkAccidentRemoved()
	{
		bool released = false;

		int stopVehicleCount = 0;
		int pedstrainCount = 0;
		auto adjacentTargets = parentArea->getAdjacentTargets(accidentOccurRect.getPosition(), checkDistance);
		for (auto& t : adjacentTargets)
		{
			if (t->getType() == TargetType::TargetType_Pedstrain)
			{
				pedstrainCount++;
				continue;
			}

			// 计算像素速度（按车框比率）
			auto velocity = t->getVelocity();
			auto curRect = t->getLatestRect();

			float speed = velocity.length();
			float width = curRect.width;
			float speedRatio = speed / width;

			// 慢行统计
			if (speedRatio* 100.f < EVENT_CFG->accidentStopMinSpeed() / 100.f)
			{
				stopVehicleCount++;
			}
		}

		if (stopVehicleCount > 0 || pedstrainCount > 0)
		{
			released = true;
		}
		return released;
	}

	/**
	 * 区域配置更新
	 */
	void AccidentEventDetector::onUpdateRegionConfig()
	{
		checkTime = 2.f;

		regionConfig.getValue<float>(checkTime, ACCIDENT_CHECK_TIME);
		regionConfig.getValue<int>(checkStopCount, ACCIDENT_STOP_COUNT);
		regionConfig.getValue<int>(checkDistance, ACCIDENT_CHECK_DISTANCE);
		regionConfig.getValue<float>(removeTime, ACCIDENT_REMOVE_TIME);
	}
}
