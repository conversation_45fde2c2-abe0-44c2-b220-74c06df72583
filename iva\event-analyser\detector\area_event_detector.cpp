/**
 * Project AI事件分析模块
 */
#include "area_event_detector.h"

/**
 * AreaEventDetector implementation
 * 
 * 区域事件检测(基类)
 * 
 * 主要统计区域内的整体状态情况，如拥堵等
 */

namespace evt
{
	AreaEventDetector::AreaEventDetector(Area* area)
	{
		detectorType = DetectorType::DetectorType_Area;
		parentArea = area;
	}

	void AreaEventDetector::onEventProposal(EventPtr evt) {

	}
	void AreaEventDetector::onEventMaintaining(EventPtr evt) {

	}
	void AreaEventDetector::onEventRelease(EventPtr evt) {

	}
	void AreaEventDetector::onUpdateRegionStart() {

	}

	EventPtr AreaEventDetector::onUpdateRegionEnd() {

		for (auto& event : holdEvents)
		{
			switch (event->getState())
			{
				case  EventState_Proposal:
					onEventProposal(event);
					break;
				case  EventState_Maintaining:
					onEventMaintaining(event);
					break;
				case  EventState_Released:
					onEventRelease(event);
					break;
				default:break;
			}
		}
		return NULL;
	}

}