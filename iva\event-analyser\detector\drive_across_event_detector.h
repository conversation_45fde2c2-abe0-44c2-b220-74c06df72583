/**
 * Project AI事件分析模块
 */

#ifndef _DRIVEACROSSEVENTDETECTOR_H
#define _DRIVEACROSSEVENTDETECTOR_H

#include "pass_event_detector.h"

/**
*
* 变道检测器
*/
namespace evt
{
	class DriveAcrossEventDetector : public PassEventDetector {
	private:

		EventPtr process(TargetPtr target) override;

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

		// 变道检测过线参考距离（像素）
		int acrossDistance = 50;

		// 变道事件相似检查距离间隔（像素）
		int eventSpace = 50;
	};
}
#endif //_DRIVEACROSSEVENTDETECTOR_H