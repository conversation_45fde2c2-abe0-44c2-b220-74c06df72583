/**
 * Project AI事件分析模块
 */

#include "drive_away_event_detector.h"
#include "area/area.h"

/**
 * DriveAwayEventDetector implementation
 * 
 * 驶离检测器
 */
namespace evt
{
	EventPtr DriveAwayEventDetector::process(TargetPtr target)
	{
		CHECK_TARGET_TYPE(target);
		if (!passedRegion(target)) return NULL;

		// 获取上次跨越信息
		AreaPassInfo lastPassInfo;
		if (!target->getLatestArea(lastPassInfo, 1))
			return NULL;

		// 上一个区域 包含驶离事件
		if (!lastPassInfo.area->hasEventType(eventType))
			return NULL;

		// 获取本次跨越信息
		AreaPassInfo passInfo;
		target->getLatestArea(passInfo);

		// 检查跨越后的帧数
		if (target->getTrackIndex() - passInfo.trackIndex < checkFrameCount)
			return NULL;

		auto evt = proposeNew(target, EventState_Confirming, eventSpace);
		evt->updateOccurArea(lastPassInfo.area->getFramePolygon());
		return evt;
	}

	/**
	 * 区域配置更新
	 */
	void DriveAwayEventDetector::onUpdateRegionConfig()
	{
		regionConfig.getValue<float>(checkTime, DRIVE_AWAY_CHECK_TIME);
		regionConfig.getValue<float>(removeTime, DRIVE_AWAY_REMOVE_TIME);
		regionConfig.getValue<int>(eventSpace, DRIVE_AWAY_EVENT_SPACE);

		checkFrameCount = (int)(checkTime * (float)frameRate);
	}
}
