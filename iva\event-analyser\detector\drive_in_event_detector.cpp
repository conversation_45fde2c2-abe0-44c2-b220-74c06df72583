/**
 * Project AI事件分析模块
 */


#include "drive_in_event_detector.h"
#include "area/area.h"
#include "config/event_config.hpp"
#include "util/scene_utility.h"

/**
 * DriveInEventDetector implementation
 * 
 * 驶入检测器
 */

namespace evt
{

	EventPtr DriveInEventDetector::process(TargetPtr target)
	{
		CHECK_TARGET_TYPE(target);

		// 是否需要跨越区域
		if (EVENT_CFG->checkDriveInAreaPass())
		{
			if (!passedRegion(target)) 
				return NULL;
		}

		// 获取本次跨越信息
		AreaPassInfo passInfo;
		target->getLatestArea(passInfo);
		if (!passInfo.area->hasEventType(eventType))
			return NULL;

		// 检查跨越后的帧数
		if (target->getTrackIndex() - passInfo.trackIndex < checkFrameCount)
			return NULL;

		// 检查 新事件 是否为最近解除事件
		auto trackPos = target->getLatestPos();
		int eventDistance = 0xffffff;
		EventPtr latestEvt = NULL;
		for (auto& evt : holdEvents)
		{
			int dis = calculateDistanceSquare(trackPos, evt->getOccurRect().getCenterPosition());
			if (latestEvt == NULL || dis < eventDistance)
			{
				eventDistance = dis;
				latestEvt = evt;
			}
		}

		// 最近解除事件 改为维持状态
		bool isNew = true;
		if (latestEvt != NULL)
		{
			isNew = false;
			if (latestEvt->getState() == EventState_Released)
			{
				latestEvt->setState(EventState_Maintaining);
				target->updateEvent(latestEvt.get());
			}
		}
		return	isNew ? proposeNew(target, EventState_Confirming, eventSpace) : NULL;
	}

	/**
	 * 事件范围匹配标记检查
	 * @param target
	 */
	bool DriveInEventDetector::eventMatchSpace(TargetPtr target)
	{
		for (auto& evt : holdEvents)
		{
			if (evt->getState() == EventState_Released)
				return false;
		}
		return checkTargetType(target->getType());
	}

	/**
	 * 区域配置更新
	 */
	void DriveInEventDetector::onUpdateRegionConfig()
	{
		regionConfig.getValue<float>(checkTime, DRIVE_IN_CHECK_TIME);
		regionConfig.getValue<float>(removeTime, DRIVE_IN_REMOVE_TIME);
		regionConfig.getValue<int>(eventSpace, DRIVE_IN_EVENT_SPACE);

		checkFrameCount = (int)(checkTime * (float)frameRate);
	}
}
