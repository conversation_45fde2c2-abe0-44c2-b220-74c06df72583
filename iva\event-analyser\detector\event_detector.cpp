/**
 * Project AI事件分析模块
 */


#include "event_detector.h"
#include "util/scene_utility.h"

/**
 * EventDetector implementation
 * 
 * 事件检测器（基类）
 */
namespace evt
{
	EventDetector::EventDetector():frameRate(DEFAULT_FRAME_RATE)
	{

	}

	EventDetector::~EventDetector()
	{

	}

	/**
	 * 初始化
	 * @param type
	 */
	void EventDetector::init(EventType type) {
		this->eventType = type;
	}

	/**
	 * 设置区域参数
	 * @param cfg
	 */
	void EventDetector::setRegionConfig(RegionConfig& cfg) {
		//TODO
		regionConfig = cfg;
		onUpdateRegionConfig();
	}

	/**
	 * 区域配置更新
	 */
	void EventDetector::onUpdateRegionConfig()
	{

	}

	/**
	 * 区域检测开始
	 */
	void EventDetector::onUpdateRegionStart() {

	}

	/**
	 * 区域检测结束
	 */
	EventPtr EventDetector::onUpdateRegionEnd() {
		return NULL;
	}

	/**
	 * 候选新事件
	 * @param target 事件目标
	 * @param state 事件状态
	 * @param pad 事件检查间距(像素)
	 */
	EventPtr EventDetector::proposeNew(TargetPtr target, EventState state, int pad)
	{
		auto evt = std::make_shared<Event>(generateUUID(), this->eventType, state, target->getLatestRect());
		evt->detector = this;
		evt->setTarget(target);
		evt->setPadding(static_cast<float>(pad));
		holdEvents.push_back(evt);
		return evt;
	}

	/**
	 * 新事件
	 * @param state 事件状态
	 */
	EventPtr EventDetector::proposeNew(EventState state, Rect rect, int pad)
	{
		auto evt = std::make_shared<Event>(generateUUID(), this->eventType, state, rect);
		evt->detector = this;
		evt->setPadding(static_cast<float>(pad));
		holdEvents.push_back(evt);
		return evt;
	}

	/**
	 * 删除持有事件
	 * @param evt
	 */
	bool EventDetector::removeHoldEvent(EventPtr evt)
	{
		holdEvents.remove(evt);
		return true;
	}

}
