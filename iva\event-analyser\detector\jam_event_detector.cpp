/**
 * Project AI事件分析模块
 */

#include "jam_event_detector.h"

/**
 * JamEventDetector implementation
 * 
 * 拥堵事件检测
 */
namespace evt
{
	JamEventDetector::JamEventDetector(Area* area): AreaEventDetector(area)
	{
	}

	/**
	 * @param type
	 * @return bool
	 */
	bool JamEventDetector::checkTargetType(TargetType type) {
		return type == TargetType::TargetType_Car
			|| type == TargetType::TargetType_Bus
			|| type == TargetType::TargetType_Truck;
	}

	void JamEventDetector::onUpdateRegionStart() {
		curSlowCount = 0;
		curNormalCount = 0;
		curAllCount = 0;
		curMeanSpeed = 0.0f;
		curSpeedAreas = 0.0f;
		heatValue = parentArea->getHeatValue();
		occupiedValue = parentArea->getOccupiedValue();
		jamTestPassed = false;
	}

	EventPtr JamEventDetector::process(TargetPtr target) {

		CHECK_TARGET_TYPE(target);

		// 检查参与拥堵计算的车辆最小像素
		auto rect = target->getLatestRect();
		if (rect.width < slowVehicleMinSize)
			return NULL;
		if (rect.height < slowVehicleMinSize)
			return NULL;

		// 统计所有参与数量
		curAllCount++;

		// 计算像素速度（按车框比率）
		auto velocity = target->getVelocity();
		auto curRect = target->getLatestRect();

		float speed = velocity.length();
		float width = curRect.width;
		float speedRatio = speed / width;

		// 统计累加基于面积的 区域平均速度
		float area = curRect.width * curRect.height;
		curMeanSpeed += speedRatio * area;
		curSpeedAreas += area;

		// 慢行统计
		if (speedRatio* 100.f < JAM_SLOW_MIN_SPEED)
			curSlowCount++;

		// 正常统计
		if (speedRatio* 100.f > JAM_NORMAL_MIN_SPEED)
			curNormalCount++;

		// 结构化页面 调试显示
		//if (rect.height > 50)
		//{
		//	std::string numSpeed = std::to_string(speedRatio * 100.f);
		//	std::string roundSpeed = numSpeed.substr(0, numSpeed.find(".") + 2);
		//	auto info = " v: " + roundSpeed;

		//	if (target->getID() % 4 == 0)
		//	{
		//		std::string numHeatValue = std::to_string(heatValue);
		//		std::string roundHeat = numHeatValue.substr(0, numHeatValue.find(".") + 4);

		//		info += "\n o: " + roundHeat;

		//		std::string numOccupiedValue = std::to_string(occupiedValue);
		//		std::string roundOccupied = numOccupiedValue.substr(0, numOccupiedValue.find(".") + 4);

		//		info += " c: " + roundOccupied;
		//	}

		//	target->updateExtraData(info);
		//}

		return NULL;
	}

	/**
	 * 区域处理结束
	 */
	EventPtr JamEventDetector::onUpdateRegionEnd()
	{
		jamTestPassed = false;

		// 计算 区域平均速度
		float areaMeanSpeed = 0.f;
		if (curSpeedAreas > 0.f)
			areaMeanSpeed = VEHICLE_SPEED_TRANS_COEFF * curMeanSpeed / curSpeedAreas;

		// IVA_LOG_INFO("areaMeanSpeed {}", areaMeanSpeed);
		// 拥堵计算
		if (curSlowCount >= startSlowCount								// 慢行数量
			&& occupiedValue >= startOccupiedRatio						// 区域占有率
			&& heatValue >= startOccupiedRatio * JAM_CHECK_HEAT_VALUE	// 区域热力值
			&& areaMeanSpeed < startMeanSpeed)							// 区域平均速度
		{
			jamTestPassed = true;
		}

		AreaEventDetector::onUpdateRegionEnd();

		if (jamTestPassed && holdEvents.empty())
		{
			return proposeNew(EventState_Proposal);
		}
		else
		{
			return NULL;
		}
	}

	/**
	 * 事件候选
	 * @param evt
	 */
	void JamEventDetector::onEventProposal(EventPtr evt) {

		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		if(jamTestPassed)
			evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);

		if (evt->getPeriodCheckCount() > frameRate)
		{
			evt->getPeriodPassCount() > (int)(JAM_CHECK_PASS_RATIO * (float)frameRate) ? evt->addStateLife(1) : evt->addStateLife(-1);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);
		}

		if (evt->getStateLife() > checkTime)
		{
			evt->setState(EventState_Confirming);
		}
		else if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_None);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件维持
	 * @param evt
	 */
	void JamEventDetector::onEventMaintaining(EventPtr evt) {
		float delta = 1.0f / (float)frameRate;

		float curLife = evt->getStateLife();
		bool released = checkJamRemoved();
		evt->addStateLife(released ? -delta : +delta);

		if (evt->getStateLife() > 1.0f) // checkTime
		{
			evt->setStateLife(1.0f);
		}
		if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_Released);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件解除
	 * @param evt
	 */
	void JamEventDetector::onEventRelease(EventPtr evt) {

		float delta = 1.0f / (float)frameRate;

		if (jamTestPassed)   	       ///< 重新达到拥堵状态，则重置 拥堵解除状态持续时间
		{
			if (evt->getStateLife() > 0.0f)
				evt->setStateLife(0.0f);
		}
		else								  
		{
			bool released = checkJamRemoved();
			evt->addStateLife(released ? +delta : -delta);
		}
	
		if (evt->getStateLife() > removeTime)
		{
			evt->setState(EventState_Removed);
			evt->setStateLife(0.0f);
		}
		else if (evt->getStateLife() + checkTime < 0)
		{
			evt->setState(EventState_Maintaining);
			evt->setStateLife(0.0f);
		}

	}

	/**
	 * 检测解除状态
	 */
	bool JamEventDetector::checkJamRemoved()
	{
		bool released = false;

		if (occupiedValue < removeOccupiedRatio) // 区域占有率 小于设定值
		{
			//IVA_LOG_INFO("occupiedValue {}", occupiedValue);
			released = true;
		}
		else
		{
			// 正常行驶数量 满足解除设定值
			float normalRatio = curAllCount > 0 ? (float)curNormalCount / curAllCount : 1.0f;
			//IVA_LOG_INFO("normalRatio {} all {}", normalRatio, curAllCount);
			if (normalRatio > removeNormalRatio)
				released = true;
		}
		return released;
	}


	/**
	 * 区域配置更新
	 */
	void JamEventDetector::onUpdateRegionConfig()
	{
		checkTime = 3.f;
		regionConfig.getValue<float>(checkTime, JAM_CHECK_TIME);
		regionConfig.getValue<int>(startSlowCount, JAM_START_SLOW_COUNT);
		regionConfig.getValue<float>(startOccupiedRatio, JAM_START_OCCUPIED_RATIO);
		regionConfig.getValue<int>(startMeanSpeed, JAM_MEAN_SPEED);
		regionConfig.getValue<float>(removeOccupiedRatio, JAM_REMOVE_OCCUPIED_RATIO);
		regionConfig.getValue<float>(removeNormalRatio, JAM_REMOVE_NORMAL_RATIO);
		regionConfig.getValue<float>(removeTime, JAM_REMOVE_TIME);
		regionConfig.getValue<int>(slowVehicleMinSize, SLOW_VEHICLE_MIN_SIZE);
	}
}