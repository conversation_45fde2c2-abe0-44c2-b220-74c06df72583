/**
 * Project AI事件分析模块
 */


#ifndef _OPPOSITEEVENTDETECTOR_H
#define _OPPOSITEEVENTDETECTOR_H

#include "target_event_detector.h"
#include "element/vector2d.h"

 /**
  * 逆行检测器
  */
namespace evt
{
	#define OPPOSITE_MIN_CHECK_FRAME_COUNT 20 			// 逆行检测最少帧数要求
	#define OPPOSITE_MIN_TURN_FRAME_COUNT 18 			// 逆行拐点检测最少帧数要求
	#define OPPOSITE_TURN_CHECK_ANGLE 120 				// 逆行检测拐点检查角度

	class OppositeEventDetector : public TargetEventDetector {
	public:

		/**
		 * @param direction
		 */
		OppositeEventDetector(Vector2D direction);

	private:

		/**
		 * 事件范围匹配标记检查
		 * @param target
		 */
		bool eventMatchSpace(TargetPtr target) override;

		/**
		 * @param type
		 */
		bool checkTargetType(TargetType type);

		/**
		 * 事件分析核心逻辑
		 */
		EventPtr process(TargetPtr target) override;

		/**
		* 事件维持
		* @param evt
		*/
		void onEventMaintaining(EventPtr evt, TargetPtr target ) override;
		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

		/**
		 * 当前方向
		 */
		Vector2D direction;

		//----------------配置参数---------------//
		// 逆行检测角度
		int checkAngle = 150;

		// 逆行检测参考距离(像素)
		float checkDistance = 100.f;

		// 逆行事件相似检查距离间隔（像素）
		int eventSpace = 10;

		// 逆行事件通过比率
		float checkRate = 0.1f;

		// 逆行车辆最小尺寸
		int vehicleMinSize = 40;
		//-----------------------------------------//
	};
}
#endif //_OPPOSITEEVENTDETECTOR_H
