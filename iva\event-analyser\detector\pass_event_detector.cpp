/**
 * Project AI事件分析模块
 */


#include "pass_event_detector.h"

/**
 * PassEventDetector implementation
 * 
 * 目标跨越区域相关事件检测（基类）
 * 
 * 主要关心目标是否跨越区域，如变道，驶入，驶离等
 */
namespace evt
{
	/**
	 * 检查目标类型
	 * @param type
	 */
	bool PassEventDetector::checkTargetType(TargetType type)
	{
		return type == TargetType::TargetType_Car
			|| type == TargetType::TargetType_Bus
			|| type == TargetType::TargetType_Truck;
	}

	/**
	 * 是否跨越
	 * @param target
	 * @return bool
	 */
	bool PassEventDetector::passedRegion(const TargetPtr& target) {

		AreaPassInfo passInfo;
		auto isPassed = target->getLatestArea(passInfo, 1);
		return isPassed;
	}
}