/**
 * Project AI事件分析模块
 */

#include "road_construction_event_detector.h"
#include "config/event_config.hpp"
#include "util/scene_utility.h"

/**
 * RoadConstructionEventDetector implementation
 * 
 * 施工事件检测
 */
namespace evt
{			

	RoadConstructionEventDetector::RoadConstructionEventDetector(Area* area): AreaEventDetector(area)
	{
		this->checkFrameCount = EVENT_CFG->constructionCheckFrameCount();
	}

	/**
	 * @param type
	 * @return bool
	 */
	bool RoadConstructionEventDetector::checkTargetType(TargetType type) {
		return type == TargetType::TargetType_Car
			|| type == TargetType::TargetType_Bus
			|| type == TargetType::TargetType_Truck;
	}

    /**
     * 检查目标分类类型
     * @param target
     */
    bool RoadConstructionEventDetector::checkTargetClassType(TargetPtr target)
    {
        if (!target)
            return false;

        if (EVENT_CFG->constructionRequireSafetyVest()) //!< 施工事件中的行人目标必须是穿了放光衣的
            return target->hasClassType(PERSON_REF_LABEL);

        return true;
    }

	void RoadConstructionEventDetector::onUpdateRegionStart() {
		curBlockCount = 0;
		curPedCount = 0;
		curStopCount = 0;
		constructTestPassed = false;
		relatedRects.clear();
		roadblocks.clear();
	}

	EventPtr RoadConstructionEventDetector::process(TargetPtr target) 
	{
		TargetType targetType = target->getType();
		if (checkTargetType( targetType ))		//有停车，加1
		{
			//std::string info = "vehicle track: " + std::to_string(target->getTrackCount());
			//target->updateExtraData(info);
			CHECK_TARGET_FRAME_COUNT(target);

			// 检查车辆最小像素
			auto rect = target->getLatestRect();
			if (rect.width < EVENT_CFG->constructionVehicleMinSize())
				return NULL;
			if (rect.height < EVENT_CFG->constructionVehicleMinSize())
				return NULL;


			// 计算像素速度（按车框比率）
			auto velocity = target->getVelocity();
			auto curRect = target->getLatestRect();

			float speed = velocity.length();
			float width = curRect.width;
			float speedRatio = speed / width;

			//info += "\n: speed" + std::to_string(speedRatio* 100.f);
			//target->updateExtraData(info);

			// 静止统计
			if (speedRatio* 100.f < (EVENT_CFG->constructionStopMinSpeed()/100.f))
			{
				auto lastRect = target->getLatestRect(checkFrameCount-1);
				auto iou = calculateIOU(curRect, lastRect);

				//info += "\n: iou: " + std::to_string(iou);
				//target->updateExtraData(info);

				if (iou * 100.f > EVENT_CFG->constructionStopMinIOU() && checkIfInConstructionArea(curRect))
				{
					//info += "\n: inArea";
					//target->updateExtraData(info);
					curStopCount++;
					relatedRects.emplace_back(target->bound());
				}
			}
		}
		else if ( targetType == TargetType::TargetType_Pedstrain && checkTargetClassType(target))//行人加1
		{
			//std::string info = "pedstrain: " + std::to_string(target->getTrackCount());
			//target->updateExtraData(info);

			auto curRect = target->getLatestRect();
			if (checkIfInConstructionArea(curRect))
			{
				//info += "\n: inArea: ";
				//target->updateExtraData(info);

				curPedCount++;
				relatedRects.emplace_back(curRect);
			}
		}
		return NULL;
	}

	void RoadConstructionEventDetector::process(EventObject object)
	{
		if (object.type == EventType_RoadBlock)
		{
			curBlockCount++;
			roadblocks.push_back(object.rect);
			relatedRects.push_back(object.rect);
		}
	}

	/**
	 * 区域处理结束
	 */
	EventPtr RoadConstructionEventDetector::onUpdateRegionEnd()
	{
		constructTestPassed = false;

		// 施工计算
		if (curBlockCount >= constructBlockCount					// 路障数量
			&& curPedCount >= constructPedCount						// 行人数量
			&& curStopCount >= constructStopCount )					// 停车数量
		{
			constructTestPassed = true;
		}

		AreaEventDetector::onUpdateRegionEnd();

		if (constructTestPassed && holdEvents.empty() && relatedRects.size() > 0 )
		{
			EventPtr ptr = proposeNew(EventState_Proposal);
			if (ptr)
			{
				ptr->updateExtraRect(relatedRects);
			}
			return ptr;
		}
		return nullptr;
	}

	/**
	 * 事件候选
	 * @param evt
	 */
	void RoadConstructionEventDetector::onEventProposal(EventPtr evt) {

		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		if (constructTestPassed)
		{
			evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);
		}

		if (evt->getPeriodCheckCount() > frameRate)
		{
			evt->getPeriodPassCount() > (int)(EVENT_CFG->constructionPassRatio() * (float)frameRate / 100.f) ? evt->addStateLife(1) : evt->addStateLife(-1);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);
		}

		if (evt->getStateLife() > checkTime)
		{
			evt->setState(EventState_Confirming);
		}
		else if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_None);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件维持
	 * @param evt
	 */
	void RoadConstructionEventDetector::onEventMaintaining(EventPtr evt) {

		float delta = 1.0f / (float)frameRate;

		float curLife = evt->getStateLife();
		bool released = checkConstructRemoved();
		evt->addStateLife(released ? -delta : +delta);

		if (evt->getStateLife() > 1.0f) // checkTime
		{
			evt->setStateLife(1.0f);
		}
		if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_Released);
			evt->setStateLife(0.0f);
		}

	}

	/**
	 * 事件解除
	 * @param evt
	 */
	void RoadConstructionEventDetector::onEventRelease(EventPtr evt) {

		float delta = 1.0f / (float)frameRate;
		bool released = false;
		if (constructTestPassed)   	       ///< 重新达到施工状态，则重置 施工解除状态持续时间
		{
			if (evt->getStateLife() > 0.0f)
				evt->setStateLife(0.0f);
		}
		else								  
		{
			released = checkConstructRemoved();
			evt->addStateLife(released ? +delta : -delta);
		}
	
		if (evt->getStateLife() > removeTime && released )
		{
			evt->setState(EventState_Removed);
			evt->setStateLife(0.0f);
		}
		else if (evt->getStateLife() + checkTime < 0)
		{
			evt->setState(EventState_Maintaining);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 检测解除状态
	 */
	bool RoadConstructionEventDetector::checkConstructRemoved()
	{
		bool released = false;
		if ( curBlockCount < constructBlockCount					// 路障数量
		//	|| curPedCount < constructPedCount						// 行人数量
			|| curStopCount < constructStopCount )
		{
			released = true;
		}
		return released;
	}

	/**
	 * 检测目标是否在施工区域
	 * @param targetRect 目标矩形框
	 */
	bool RoadConstructionEventDetector::checkIfInConstructionArea(Rect& targetRect)
	{
		bool inConstructionArea = true;
		if (EVENT_CFG->constructionVehicleDistance() > 0) // 如果配置了施工锥筒距离限制
		{
			if (roadblocks.size() > 0)
			{
				int distanceFromRoadblock = INT_MAX;
				int checkcount = 0;
				for (auto& r : roadblocks)
				{
					if (checkcount++ > 10)
						break;

					distanceFromRoadblock = std::min(distanceFromRoadblock, (int)targetRect.distanceFrom(r));
				}

				if (distanceFromRoadblock > EVENT_CFG->constructionVehicleDistance())
					inConstructionArea = false;
			}
		}
		return inConstructionArea;
	}

	/**
	 * 区域配置更新
	 */
	void RoadConstructionEventDetector::onUpdateRegionConfig()
	{
		checkTime = 3.f;
		regionConfig.getValue<float>(checkTime, ROAD_CONSTRUCTION_CHECK_TIME); 
		regionConfig.getValue<float>(removeTime, ROAD_CONSTRUCTION_REMOVE_TIME);
		regionConfig.getValue<int>(constructPedCount, ROAD_CONSTRUCTION_PED_NUM);
		regionConfig.getValue<int>(constructBlockCount, ROAD_CONSTRUCTION_BLOCK_NUM);
		regionConfig.getValue<int>(constructStopCount, ROAD_CONSTRUCTION_STOP_NUM);
	}
}
