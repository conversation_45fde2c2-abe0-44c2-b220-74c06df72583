/**
 * Project AI事件分析模块
 */


#ifndef _ROADCONSTRUCTIONEVENTDETECTOR_H
#define _ROADCONSTRUCTIONEVENTDETECTOR_H

#include "area_event_detector.h"

 /**
  *
  * 拥堵事件检测
  */
namespace evt
{

	class RoadConstructionEventDetector : public AreaEventDetector {

	public:
		RoadConstructionEventDetector(Area* area);
		/**
		 * @param type
		 */
		bool checkTargetType(TargetType type);

        /**
         * 检查目标分类类型
         * @param target
         */
        bool checkTargetClassType(TargetPtr target);

	private:
		/**
		 * 事件候选
		 * @param evt
		 */
		void onEventProposal(EventPtr evt) override;

		/**
		 * 事件维持
		 * @param evt
		 */
		void onEventMaintaining(EventPtr evt) override;

		/**
		 * 事件解除
		 * @param evt
		 */
		void onEventRelease(EventPtr evt) override;

		/**
		 * 区域处理开始
		 */
		void onUpdateRegionStart() override;

		EventPtr process(TargetPtr target) override;

		void process(EventObject object) override;
		/**
		 * 区域处理结束
		 */
		EventPtr onUpdateRegionEnd() override;

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

		/**
		 * 检测解除状态
		 */
		bool checkConstructRemoved();

		/**
		 * 检测目标是否在施工区域
		 * @param targetRect 目标矩形框
		 */
		bool checkIfInConstructionArea(Rect& targetRect);

	private:
		// 当前区域路障数量
		int curBlockCount;

		// 当前区域行人数量
		int curPedCount;

		// 当前区域停车数量
		int curStopCount;

		// 施工条件满足
		bool constructTestPassed;

		std::vector<Rect> relatedRects;
		//----------------配置参数---------------//
		// 施工生效行人数量
		int constructPedCount = 1;

		// 施工生效路障数量
		int constructBlockCount = 2;

		// 施工生效停车数量
		int constructStopCount = 1;

		//-----------------------------------------//
		// 路障框列表
		std::vector<Rect> roadblocks;
	};
}
#endif //_ROADCONSTRUCTIONEVENTDETECTOR_H