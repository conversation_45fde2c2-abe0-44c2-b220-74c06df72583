/**
 * Project AI事件分析模块
 */


#include "stop_event_detector.h"
#include "util/scene_utility.h"

/**
 * StopEventDetector implementation
 * 
 * 停车检测器
 */
namespace evt
{
	/**
	 * 检查目标类型
	 * @param type
	 */
	bool StopEventDetector::checkTargetType(TargetType type)
	{
		return type == TargetType::TargetType_Car
			|| type == TargetType::TargetType_Bus
			|| type == TargetType::TargetType_Truck;
	}

	/**
	 * 事件分析核心逻辑
	 */
	EventPtr StopEventDetector::process(TargetPtr target)
	{
		CHECK_TARGET_TYPE(target);
		CHECK_TARGET_FRAME_COUNT(target);
		
		auto trackEnd = target->getLatestRect();
		auto trackStart = target->getLatestRect(checkFrameCount);

		auto iou = calculateIOU(trackEnd, trackStart);
		auto length = target->getAccuVelocity().length();

		// 1. IOU满足
		// 2. 累计平均速度不超过200 像素/ 20 帧 (TODO)
		if (iou > checkIOU && length < STOP_ACCUMULATED_VELOCITY_MAX) 
			return proposeNew(target, EventState_Proposal);
		else
			return NULL;
	}

	/**
	 * 事件候选
	 * @param evt
	 * @param target
	 */
	void StopEventDetector::onEventProposal(EventPtr evt, TargetPtr target)
	{
		// 候选 通过计数
		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		bool matched = matchCheck(evt, target);
		if (matched)
			evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);

		if (evt->getPeriodCheckCount() > frameRate)
		{
			evt->getPeriodPassCount() > (int)(checkRate * (float)frameRate) ? evt->addStateLife(1): evt->addStateLife(-1);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);
		}

		// 候选 时间判断
		if (evt->getStateLife() > checkTime - proposeTime)
		{
			if (target != NULL && matched)
			{
				evt->updateOccurRect(target->getLatestRect());
				evt->setState(EventState_Confirming);
				evt->setPadding(eventSpace);
				evt->setStateLife(0.0f);
			}
		}
		else if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_None);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件维持
	 * @param evt
	 * @param target
	 */
	void StopEventDetector::onEventMaintaining(EventPtr evt, TargetPtr target)
	{
		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		if (matchCheck(evt, target))
			evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);

		if (evt->getPeriodCheckCount() > frameRate)
		{
			evt->getPeriodPassCount() > (int)(STOP_MAINTAINING_CHECK_RATE * (float)frameRate) ? evt->addStateLife(1) : evt->addStateLife(-1);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);
		}
		if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_Released);
			evt->setStateLife(0.0f);
		}
		else
		{
			if (evt->getStateLife() > checkTime)
			{
				evt->setStateLife(checkTime);
			}
		}
	}

	/**
	 * 事件解除
	 * @param evt
	 * @param target
	 */
	void StopEventDetector::onEventRelease(EventPtr evt, TargetPtr target)
	{
		evt->setPeriodCheckCount(evt->getPeriodCheckCount() + 1);
		if (matchCheck(evt, target))
			evt->setPeriodPassCount(evt->getPeriodPassCount() + 1);

		if (evt->getPeriodCheckCount() > frameRate)
		{
			bool periodCheckPassed = evt->getPeriodPassCount() > (int)(checkRate * (float)frameRate);
			periodCheckPassed ? evt->addStateLife(-1) : evt->addStateLife(1);
			evt->setPeriodCheckCount(0);
			evt->setPeriodPassCount(0);

			// 如果 区间计数满足 重置解除时间
			if (periodCheckPassed && evt->getStateLife() > 0.0f)
				evt->setStateLife(0.0f);
		}
		if (evt->getStateLife() > removeTime)
		{
			evt->setState(EventState_Removed);
			evt->setStateLife(0.0f);
		}
		else if (evt->getStateLife() + checkTime < 0.f )
		{
			evt->setState(EventState_Maintaining);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * 事件目标匹配检查
	 * @param evt
	 * @param target
	 */
	bool StopEventDetector::matchCheck(EventPtr evt, TargetPtr target)
	{
		if (target == NULL)
			return false;

		if (!checkTargetType(target->getType())) return false;

		auto iou = calculateIOU(evt->getOccurRect(), target->getLatestRect());
		return iou > checkIOU;
	}


	/**
	 * 区域配置更新
	 */
	void StopEventDetector::onUpdateRegionConfig()
	{
		removeTime = 60.f;

		regionConfig.getValue<float>(checkIOU, STOP_CHECK_IOU);
		regionConfig.getValue<float>(proposeTime, STOP_PROPOSE_TIME);
		regionConfig.getValue<float>(checkTime, STOP_CHECK_TIME);
		regionConfig.getValue<float>(removeTime, STOP_REMOVE_TIME);
		regionConfig.getValue<float>(checkRate, STOP_CHECK_RATE);
		regionConfig.getValue<float>(eventSpace, STOP_EVENT_SPACE);

		checkFrameCount = (int)(proposeTime * (float)frameRate);
	}
}
