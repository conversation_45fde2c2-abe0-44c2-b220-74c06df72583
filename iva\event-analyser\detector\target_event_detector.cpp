/**
 * Project AI事件分析模块
 */


#include "target_event_detector.h"

 /**
  * TargetEventDetector implementation
  *
  * 目标事件检测器（基类）
  *
  * 按单个目标分类的事件类型检测器，如停车，逆行等
  */
namespace evt
{
	TargetEventDetector::TargetEventDetector()
	{
		detectorType = DetectorType_Target;
	}

	/**
	 * 事件范围匹配标记检查
	 * @param target
	 */
	bool TargetEventDetector::eventMatchSpace(TargetPtr target)
	{
		return checkTargetType(target->getType());
	}

	/**
	 * 检查目标类型
	 * @param type
	 */
	bool TargetEventDetector::checkTargetType(TargetType type)
	{
		return false;
	}

	/**
	 * @param target
	 */
	EventPtr TargetEventDetector::process(TargetPtr target) {
		return NULL;
	}
	/**
	 * @param object
	 */
	void TargetEventDetector::process(EventObject object)
	{
	}

	/**
	 * 事件状态检查
	 * @param event
	 * @param target
	 */
	void TargetEventDetector::onUpdateEvent(EventPtr event, TargetPtr target)
	{
		if (event != NULL)
		{
			switch (event->getState())
			{
				case  EventState_Proposal:
					onEventProposal(event, target);
					break;
				case  EventState_Maintaining:
					onEventMaintaining(event, target);
					break;
				case  EventState_Released:
					onEventRelease(event, target);
					break;
				default:break;
			}
		}
	}

	/**
	 * @param evt
	 * @param target
	 */
	void TargetEventDetector::onEventProposal(EventPtr evt, TargetPtr target) {

	}

	/**
	 * @param evt
	 * @param target
	 */
	void TargetEventDetector::onEventMaintaining(EventPtr evt, TargetPtr target) {

		float delta = 1.0f / (float)frameRate;

		float curLife = evt->getStateLife();
		evt->addStateLife(target != NULL ? +delta : -delta);

		if (evt->getStateLife() > 1.0f) // checkTime
		{
			evt->setStateLife(1.0f);
		}
		if (evt->getStateLife() < 0.0f)
		{
			evt->setState(EventState_Released);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * @param evt
	 * @param target
	 */
	void TargetEventDetector::onEventRelease(EventPtr evt, TargetPtr target) {

		float delta = 1.0f / (float)frameRate;

		float curLife = evt->getStateLife();
		evt->addStateLife(target != NULL ? -delta : +delta);

		if (evt->getStateLife() > removeTime)
		{
			evt->setState(EventState_Removed);
			evt->setStateLife(0.0f);
		}
		else if (evt->getStateLife() + checkTime < 0)
		{
			evt->setState(EventState_Maintaining);
			evt->setStateLife(0.0f);
		}
	}

	/**
	 * @param evt
	 * @param target
	 */
	bool TargetEventDetector::matchCheck(EventPtr evt, TargetPtr target) {
		if (evt->getTargetID() == target->getID())
		{
			return true;
		}
		else
		{
			return false;
		}
	}
}