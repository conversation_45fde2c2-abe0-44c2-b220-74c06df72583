/**
 * Project AI事件分析模块
 */


#include "two_wheels_event_detector.h"

/**
 * TwoWheelsEventDetector implementation
 * 
 * 摩托车检测器
 */
namespace evt
{

	/**
	 * 检查目标类型
	 * @param type
	 */
	bool TwoWheelsEventDetector::checkTargetType(TargetType type) {
		return type == TargetType::TargetType_TwoWheels;
	}

	/**
	 * 核心判断逻辑
	 * @param target
	 */
	EventPtr TwoWheelsEventDetector::process(TargetPtr target)
	{
		CHECK_TARGET_TYPE(target);
		CHECK_TARGET_FRAME_COUNT(target);

		auto velocity = target->getVelocity();
		auto curRect = target->getLatestRect();

		float speedRatio = target->getAverageSpeedRatio();

		if (speedRatio * frameRate * TWOWHEELS_SPEED_TRANS_COEFF >= minSpeed)
		{
			// 摩托车抽帧 偶现框子滞留
			// 适当按行车方向 扩充截图框
			float xPadding = 4.f * velocity.x; 
			float yPadding = 4.f * velocity.y;

			xPadding = std::max(-20.f, std::min(20.f, xPadding));
			yPadding = std::max(-20.f, std::min(20.f, yPadding));

			Point tl1(curRect.getLeft(), curRect.getTop());
			Point br1(curRect.getRight(), curRect.getBottom());

			Point tl2(curRect.getLeft() + xPadding, curRect.getTop() + yPadding);
			Point br2(curRect.getRight() + xPadding, curRect.getBottom() + yPadding);

			float l = std::min({ tl1.x , tl2.x });
			float r = std::max({ br1.x , br2.x});
			float t = std::min({ tl1.y , tl2.y });
			float b = std::max({ br1.y , br2.y });

			l = std::max(0.0f, l);
			t = std::max(0.0f, t);
			auto evtRect = Rect(l,t,r-l,b-t);

			auto evt= proposeNew(target, EventState_Confirming, eventSpace);

			if(evtRect.isValid())
				evt->updateOccurRect(evtRect);
			return evt;
		}
		else
		{
			return NULL;
		}
	}

	/**
	 * 区域配置更新
	 */
	void TwoWheelsEventDetector::onUpdateRegionConfig()
	{
		removeTime = 60.f;

		regionConfig.getValue<float>(checkTime, TWOWHEELS_CHECK_TIME);
		regionConfig.getValue<float>(removeTime, TWOWHEELS_REMOVE_TIME);
		regionConfig.getValue<float>(minSpeed, TWOWHEELS_MIN_SPEED);
		regionConfig.getValue<int>(eventSpace, TWOWHEELS_EVENT_SPACE);

		checkFrameCount = (int)(checkTime * (float)frameRate);
	}

}