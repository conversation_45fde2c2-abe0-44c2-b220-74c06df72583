/**
 * Project AI事件分析模块
 */

#include "element/polygon.h"
#include <math.h>
#include <algorithm>
#include "util/scene_utility.h"

 /**
  * Polygon implementation
  *
  * 多边形
  */
namespace evt
{
	/**
	 * 是否包含点
	 * @param p
	 * @return bool
	 */
	Polygon::Polygon()
	{
	}

	Polygon::Polygon(std::vector<Point>& pointList)
	{
		init(pointList);
	}

	void Polygon::init(std::vector<Point>& pointList)
	{
		this->points = pointList;
		updateBoundingBox();
	}

	/**
	 * 更新外接矩形
	 */
	void Polygon::updateBoundingBox()
	{
		if (points.size() > 2)
		{
			Point lt = this->points[0];
			Point br = this->points[1];

			for (auto& p : this->points)
			{
				lt.x = std::min(p.x, lt.x);
				lt.y = std::min(p.y, lt.y);

				br.x = std::max(p.x, br.x);
				br.y = std::max(p.y, br.y);
			}

			boundingRect = Rect(lt, br);
		}
	}

	/**
	 * 按实际尺寸缩放
	 * @param w
	 * @param h
	 */
	void Polygon::scale(int w, int h)
	{
		for (auto& p : points)
		{
			p.x *= static_cast<float>(w);
			p.y *= static_cast<float>(h);
		}
		updateBoundingBox();
	}

	/**
	 * 是否包含点
	 * @param p
	 */
	bool Polygon::containPoint(Point p) {

		int count = static_cast<int>(points.size());
		if (count < 3)
		{
			return false;
		}

		// 外接矩形 判断
		if (!boundingRect.contains(p))
			return false;

		int px = p.x,
			py = p.y;
		double sum = 0;

		for (int i = 0, l = count, j = l - 1; i < l; j = i, i++)
		{
			int sx = points[i].x,
				sy = points[i].y,
				tx = points[j].x,
				ty = points[j].y;

			if ((sx - px) * (px - tx) >= 0 && (sy - py) * (py - ty) >= 0 && (px - sx) * (ty - sy) == (py - sy) * (tx - sx))
			{
				return true;
			}

			double angle = atan2(sy - py, sx - px) - atan2(ty - py, tx - px);
			if (angle >= PI)
			{
				angle = angle - PI * 2;
			}
			else if (angle <= -PI)
			{
				angle = angle + PI * 2;
			}

			sum += angle;
		}

		return round(sum / PI) != 0;
	}

	/**
	 * 多边形数据是否正确
	 */
	bool Polygon::isValid() {
		if (points.size() < 4)
			return false;

		int zerocount = 0;
		for (auto& p : points)
		{
			if (p == Point(0.0f, 0.0f))
			{
				zerocount++;
			}
		}
		return zerocount < points.size();
	}

	/**
	 * 外接矩形
	 * @return Rect
	 */
	Rect Polygon::boundingBox() {
		return boundingRect;
	}

	/**
	 * 获取坐标点
	 * @param index 坐标点序号
	 */
	Point Polygon::getPoint(int index)
	{
		if (index >=0 && index < pointCount())
		{
			return points[index];
		}
		return Point();
	}

	/**
	 * 多边形点数量
	 */
	int Polygon::pointCount()
	{
		return static_cast<int>(points.size());
	}
}
