/**
 * Project AI事件分析模块
 */


#ifndef _TARGET_H
#define _TARGET_H

#include <vector>
#include <list>
#include <memory>
#include <deque>
#include <map>
#include "map/scene_object.h"
#include "util/mask_value.h"
#include "element/target_info.h"
#include "element/vector2d.h"
#include "element/target_class.h"

//#define CyclicArray std::vector

/**
* 目标对象
*/
namespace evt
{
	#define TARGET_MAX_LIFE 1					// 目标缓存最大生命
	#define TARGET_SPEED_TRACK_MIN 5			// 目标实际速度计算最少帧数
	#define TARGET_VELOCITY_MEAN_INDEX 20		// 目标向量速度平均统计帧数

	#define TARGET_TRACK_MAX_LENGTH 1000		// 目标轨迹最大长度
	#define TARGET_TRACK_ADJUST_LENGTH 600		// 目标轨迹调整长度
	#define TARGET_ABNORMAL_SPEEDRATIO_COEFF 20 // 目标帧 像素比例速度的突变倍数
	#define TARGET_ACCUMULATED_DISPL_MIN 20	    // 计数满足的最短轨迹长度

	#define TARGET_OPPOSITE_TRACK_SLIDE 150		// 目标逆行判断滑动帧数
	#define TARGET_TURN_TRACK_MAX 400			// 目标拐点判断最大帧数

	#define TARGET_CHECK_ACC_SPEED 20.f			// 目标速度检查起始
	#define TARGET_MAX_ACC_SPEED_RATIO 30		// 目标最大加速倍数
	
	class Area;
	/*
	 * 目标跨越区域信息
	 */
	struct AreaPassInfo
	{
		int trackIndex;
		Area* area;
		Rect rect;
	};

	class Lane;
	class Event;
	class Region;
	class Target : public SceneObject {

		friend class Channel;
		friend class Scene;
		friend class SceneState;
		friend class Area;
	public:
		/**
		 * @param id
		 * @param type
		 * @param classTypes 目标分类类型 key: classifierType value: label
		 */
		Target(int id, TargetType type, const std::map<std::string, std::string>& classTypes);

		/**
		 * 更新轨迹帧
		 * @param track
		 */
		bool updateTrack(Track& track);

		/**
		 * 更新区域信息
		 * @param region
		 * @param roiID
		 */
		void updateArea(Area* region, int roiID);

		/**
		 * 更新区域信息
		 * @param area 当前区域
		 * @param bSetArea
		 * @param bAddArea 是否加入到当前区域中
		 */
		void updateArea(Area* area, bool bSetCurArea, bool bAddArea);

		/**
		 * 获得当前所在区域
		 */
		Area* getCurrentArea();

		/**
		 * 更新车道信息
		 * @param lane
		 */
		void updateLane(Lane* lane);

		/**
		 * 更新区域信息
		 * @param Region
		 */
		void updateRegion(Region* region);

		/**
		 * 更新关联事件信息
		 * @param evt
		 */
		void updateEvent(Event* evt);

		/**
		 * 更新过线状态
		 * @param state
		 */
		void updateCountLineState(TargetCountLineState state);

		/**
		 * 更新速度(Km/h)
		 */
		void updateSpeed(int val);

		/**
		 * 更新折点（拐点）
		 */
		void updateTurnIndex(Vector2D direction, float turnAngle);

		/**
		 * 更新额外信息
		 */
		void updateExtraData(std::string data);

		/**
		 * 设置相关事件类型（ROI范围事件）
		 * @param evtType
		 */
		void addRelatedEventType(EventType evtType);

		/**
		 * 是否和事件类型关联（ROI范围事件）
		 * 即，是否参与 范围事件（拥堵、事故、施工等） 的计算
		 * @param evtType
		 */
		bool hasRelatedEventType(EventType evtType);

		/**
		 * 清理关联事件类型（ROI范围事件）
		 */
		void clearRelatedEventTypes();

		/**
		 * 目标ID
		 */
		int getID();

		/**
		 * 目标类型
		 */
		TargetType getType();

		/**
		 * 关联事件ID
		 */
		std::string getEventID();

		/**
		 * 关联事件状态
		 */
		EventState getEventState();

		/**
		 * 关联事件类型
		 */
		EventType getEventType();

		/**
		 * 轨迹长度
		 */
		int getTrackCount();

		/**
		 * 获取速度 km/h
		 */
		int getSpeed();

		/**
		 * ROI ID
		 */
		int getROIID();

		/**
		 * 车道类型
		 */
		LaneType getLaneType();

		/**
		 * 区域ID
		 */
		int getRegionID();

		/**
		 * 车道ID
		 */
		int getLaneID();

		/**
		 * 获取最近坐标框
		 */
		Rect getLatestRect(int index=0);

		/**
		 * 获取最近坐标点
		 */
		Point getLatestPos(int index = 0, bool useCenter = false);

		/**
		 * 获取最近经过区域
		 */
		bool getLatestArea(AreaPassInfo& passInfo, int index = 0);

		/**
		 * 基于帧序获取坐标框
		 */
		Rect getIndexRect(int index);

		/**
		 * 基于帧序获取坐标点
		 */
		Point getIndexPos(int index, bool useCenter = false);

		/**
		 * 获取计数线状态
		*/
		TargetCountLineState getCountLineState();

		/**
		 * 获取当前外接矩形框 (通过 SceneObject 继承)
		 */
		Rect bound() override;

		/**
		 * 平均方向向量(像素)
		 */
		Vector2D getVelocity();

		/**
		 * 累计平均方向向量(像素)
		 */
		Vector2D getAccuVelocity();

		/**
		 * 拐点时刻平均方向向量(像素)
		 */
		Vector2D getTurnVelocity(int latestIndex= 0);

		/**
		 * 是否显示
		 */
		bool getShowable();

		/**
		 * 逆行数量统计
		*/
		int getOppositeCount();

		/**
		 * 获取track序号
		*/
		int getTrackIndex();

		/**
		 * 是否为机动车
		 */
		bool isVehicleType();

		/**
		 * 获取折点（拐点）序号
		 */
		int getTurnIndex();

		/**
		 * 是否穿越ROI
		 */
		bool IsPassedROI();

		/**
		 * 更新像素比例平均速度 （像素位移/框对角线）
		 */
		void updateSpeedRatio(float len);

		/**
		* 获取目标框移动的平均速度 （像素位移/框对角线）
		*/
		float getAverageSpeedRatio();

		/**
		 * 是否需要计算速度
		 */
		bool needCountSpeed();

		/** 
		 * 开始计算速度
		 */
		void startCountSpeed();

        /**
         * 更新目标的分类信息
         * @param[in] classTypes key: classifierType value: label
         */
        inline void updateClassType(const std::map<std::string, std::string>& classTypes){info.classTypes.updateClassType(classTypes);};

        /**
         * 获取目标分类信息
         * @return key: classifierType value: label
         */
        inline std::map<std::string, std::string> getClassTypes(){return info.classTypes.getClassTypes();};

        /**
         * 从分类类型信息集中查找是否有某类标签类别
         * @param[in] label 判断是否有该标签类型
         */
        bool hasClassType(std::string_view label);

	private:
		/**
		 * 目标信息
		 */
		TargetInfo info;

		/**
		 * 当前平均方向向量(像素)
		 */
		Vector2D velocity;

		/**
		 * 当前累计方向向量(像素)
		 */
		Vector2D accumulatedVelocity;

		/**
		 * track序号
		*/
		int trackIndex;

		/**
		 * 上一折点
		 */
		int turnIndex;

		/**
		 * 上一逆行点
		 */
		int oppositeIndex;
		std::list<int> oppositeIndexs;

		/**
		 * 拐点时刻平均方向向量(像素)
		 */
		std::vector<Vector2D> turnVelocities;

		/**
		 * 生命值
		*/
		int life;

		/**
		 * 是否匹配（已有事件）
		*/
		MaskValue matched;

		/**
		 * 关联的需要检测的事件类型 （主要为区域类型事件 按范围查找时检查用）
		*/
		MaskValue relatedEventTypes;

		/**
		 * 是否被移除
		*/
		bool removed;

		/**
		 * 是否穿越ROI
		*/
		bool passedROI;

		/**
		 * 是否已检查交通流
		*/
		bool sceneflowChecked;

		/**
		 * 计数线状态
		*/
		TargetCountLineState countLineState;

		/**
		 * 经过区域
		 */
		std::vector<AreaPassInfo> passedAreas;

		Area* currentArea; //当前停留的区域

		/**
		 * 正常帧的瞬时速度（像素位移/框对角线）
		 */
		std::deque<float> normalInstantSpeeds;

		/**
		* 平均帧速度（像素位移/框对角线）
		*/
		float averageSpeedRatio;

		/**
		* 轨迹是否已跟踪
		*/
		bool tracked;

		/** 
		 * 是否开始计算速度
		 */
		bool countSpeed;

        /**
         * 过线车辆是否被截取到
         */
        bool vehicleCaptured = false;
	};

	typedef std::shared_ptr<Target> TargetPtr;

	typedef std::list<TargetPtr> TargetList;
}
#endif //_TARGET_H
