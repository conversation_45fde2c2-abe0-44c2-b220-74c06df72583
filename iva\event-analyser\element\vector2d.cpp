/**
 * Project AI事件分析模块
 */

#include "element/vector2d.h"
#include "util/scene_utility.h"
#include <math.h>
#include <iostream>

/**
 * Vector2D implementation
 * 
 * 二唯向量
 */
namespace evt
{
	Vector2D::Vector2D()
	{
		this->x = 0.0f;
		this->y = 0.0f;
	}

	Vector2D::Vector2D(float x, float y)
	{
		this->x = x;
		this->y = y;
	}

	Vector2D::Vector2D(Point p1, Point p2)
	{
		this->x = p2.x - p1.x;
		this->y = p2.y - p1.y;
	}

	Vector2D Vector2D::operator+(float f)
	{
		return Vector2D(this->x + f, this->y + f);
	}

	Vector2D Vector2D::operator+(Vector2D v)
	{
		return Vector2D(this->x + v.x, this->y + v.y);
	}

	Vector2D& Vector2D::operator+=(Vector2D& v)
	{
		this->x += v.x;
		this->y += v.y;
		return *this;
	}

	Vector2D Vector2D::operator-(float f)
	{
		return Vector2D(this->x - f, this->y - f);
	}

	Vector2D Vector2D::operator-(Vector2D f)
	{
		return Vector2D(this->x - f.x, this->y - f.y);
	}

	Vector2D& Vector2D::operator-=(Vector2D& v)
	{
		this->x -= v.x;
		this->y -= v.y;
		return *this;
	}

	Vector2D Vector2D::operator/(float f)
	{
		if (fabsf(f) < 0.0f)
		{
			std::cerr << "Over flow!\n";
			return *this;
		}

		return Vector2D(x / f, y / f);
	}

	Vector2D Vector2D::operator*(float f)
	{
		return Vector2D(x*f, y*f);
	}

	bool Vector2D::operator==(const Vector2D &p)
	{
		return p.x == x && p.y == y;
	}

	bool Vector2D::operator!=(const Vector2D &p)
	{
		return !operator==(p);
	}

	float Vector2D::dot(const Vector2D &v)
	{

		return x * v.x + y * v.y;
	}

	double Vector2D::cross(const Vector2D &v)
	{
		return x * v.y - y * v.x;
	}

	float Vector2D::length()
	{
		return sqrtf(dot(*this));
	}

	void Vector2D::normalize()
	{
		float len = length();
		if (len <= 0)
			len = 1.f;

		x /= len;
		y /= len;
	}

	// inner angle
	float Vector2D::angle(Vector2D& v)
	{
		if (!isValid() || !v.isValid()) return 0.f;
		float radian =  acos(dot(v) / (this->length() * v.length()));
		return radian * 180.0f / PI;
	}

	bool Vector2D::isValid()
	{
		return x != 0.0f || y != 0.0f;
	}
}
