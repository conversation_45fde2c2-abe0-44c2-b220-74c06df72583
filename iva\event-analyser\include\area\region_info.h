/**
 * Project AI事件分析模块
 */

#ifndef _REGIONINFO_H
#define _REGIONINFO_H

#include "element/polygon.h"
#include "config/region_config.h"

 /**
  * 子区域配置信息
  */
namespace evt
{
	class RegionInfo {
	public:
		/**
		 * 区域ID
		 */
		int id;

		/**
		 * 事件类型掩码
		 */
		int eventMask;

		/**
		 * 目标类型掩码
		 */
		int targetMask;

		/**
		 * 区域多边形
		 */
		Polygon polygon;

		/**
		 * 个性化区域参数配置
		 */
		RegionConfig config;
	};
}
#endif //_REGIONINFO_H
