/**
 * Project AI事件分析模块
 */

#ifndef _REGIONCONFIG_H
#define _REGIONCONFIG_H

#include "module_def.h"
#include <map>
#include <string>

/**
* 区域配置（个性化参数）
*/
namespace evt
{
	using std::map;
	using std::string;

	class EVT_EXPORT RegionConfig {
	public:

		RegionConfig();
		/**
		 * @param cfg 参数配置
		 * @param level 灵敏度等级
		 */
		RegionConfig(map<string, string>& cfg, int level =0);

		/**
		 * 获取值
		 * @param val 配置值
		 * @param key 配置项
		 * @param fallbackGlobal 使用全局项
		 */
		template<typename T>
		bool getValue(T& val, string key, bool fallbackGlobal= true);

	private:

		/**
		 * 配置内容
		 */
		map<string, string> configs;

		/**
		 * 配置灵敏度等级
		 */
		int cfgLevel;
	};
}
#endif //_REGIONCONFIG_H