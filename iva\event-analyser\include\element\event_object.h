/**
 * Project AI事件分析模块
 */

#ifndef _EVENT_OBJECT_H
#define _EVENT_OBJECT_H

#include <vector>
#include "element/rect.h"
#include "enum/event_type.h"

 /**
 *
 * 事件目标
 * 处理图像模块 检测到的场景物件 （抛洒物、烟火、路障等）
 *
 */
namespace evt
{
	struct EventObject {

		/**
		 * 目标ID
		 */
		int id;

		/**
		 * 关联的事件类型
		 */
		EventType type;

		/**
		 * 目标的坐标框
		 */
		Rect rect;

		/**
		 * 是否已匹配
		 */
		bool matched = false;

		EventObject(int id_, EventType type_, Rect rect_) 
		{
			id = id_;
			type = type_;
			rect = rect_;
		}
	};

	typedef std::vector<EventObject> EventObjectList;
}

#endif  //_EVENT_OBJECT_H
