/**
 * Project AI事件分析模块
 */

#ifndef _LINE_H
#define _LINE_H

#include "module_def.h"
#include "point.h"

/**
*
* 线段
*/
namespace evt
{
	 struct EVT_EXPORT Line {
		/**
		 * 起始点
		 */
		Point begin;

		/**
		 * 结束点
		 */
		Point end;

		Line() {};

		Line(Point& p1, Point& p2)
		{
			begin = p1;
			end = p2;
		}

		/**
		 * 数据是否正确
		 */
		bool isValid()
		{
			return (begin != end) && (begin != Point(0.0f, 0.0f) || end != Point(0.0f, 0.0f));
		}
	};
}
#endif //_LINE_H