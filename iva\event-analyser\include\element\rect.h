/**
 * Project AI事件分析模块
 */


#ifndef _RECT_H
#define _RECT_H

#include "module_def.h"
#include "point.h"

/**
* 矩形框
*/
namespace evt
{
	struct EVT_EXPORT Rect {

		float x;
		float y;
		float width;
		float height;

		Rect();

		/**
		 * @param lt 左上点
		 * @param br 右下点
		 */
		Rect(Point lt, Point br);

		/**
		 * @param x
		 * @param y
		 * @param w
		 * @param h
		 */
		Rect(float x, float y, float w, float h);

		/**
		 * 是否包含矩形框
		 */
		bool contains(const Rect &other);

		/**
		 * 是否包含点
		 */
		bool contains(const Point &p);

		/**
		 * 是否相交矩形框
		 */
		bool intersects(const Rect &other);

		/**
		 * 计算与其他矩形框 距离
		 */
		float distanceFrom(Rect& other);

		/**
		 * 获取矩形框左边值
		 */
		float getLeft();

		/**
		 * 获取矩形框顶部值
		 */
		float getTop();

		/**
		 * 获取矩形框右边值
		 */
		float getRight();

		/**
		 * 获取矩形框底边值
		 */
		float getBottom();

		/**
		 * 获取矩形框中心X
		 */
		float getCenterX();

		/**
		 * 获取矩形框中心Y
		 */
		float getCenterY();

		/**
		 * 获取矩形框注册点
		 * @param offsetX X偏移比例
		 * @param offsetY Y偏移比例
		 */
		Point getPosition(float offsetX= 0.5f, float offsetY= 0.9f);

		/**
		 * 获取矩形框中心点
		 */
		Point getCenterPosition();

		/**
		 * 获取矩形边长（平均边长）
		 */
		float getSideLength();

		/**
		 * 获取矩形边长（最大边长）
		 */
		float getMaxLength();

		/**
		 * 获取矩形 高宽比
		 */
		float getHWRatio();

		/**
		* 获取矩形框对角线长度
		*/
		float getDiagonalLen();

		/**
		 * 框子是否合法
		 */
		bool isValid();

	};
}
#endif //_RECT_H