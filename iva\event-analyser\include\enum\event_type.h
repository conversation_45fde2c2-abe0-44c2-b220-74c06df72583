/**
 * Project AI事件分析模块
 */


#ifndef _EVENTTYPE_H
#define _EVENTTYPE_H

#include <algorithm>

namespace evt
{
	/*
	 * 检测事件类型
	 */
	enum EventType
	{
		EventType_None = 0,
		// 停车
		EventType_Stop = 1, 
		// 逆行
		EventType_Opposite = 2, 
		// 拥堵
		EventType_Jam = 5,
		// 驶入
		EventType_DriveIn = 10,
		// 变道
		EventType_DriveAcross = 11,
		// 驶离
		EventType_DriveAway = 9,
		// 行人
		EventType_Pedstrain = 6,

		// 抛洒物
		EventType_Obstacle = 8,
		// 路障
		EventType_RoadBlock = 12,
		// 施工
		EventType_RoadConstruction = 13,
		// 烟火
		EventType_FireSmoke = 14,
		// 摩托车
		EventType_TwoWheels = 16,
		// 事故
		EventType_Accident = 17
	};

	// 所有事件类型列表
	static const auto allEventTypes = { 
		EventType_Stop,
		EventType_Opposite,
		EventType_Jam,
		EventType_DriveIn,
		EventType_DriveAcross,
		EventType_DriveAway,
		EventType_Pedstrain,
		EventType_TwoWheels,

		EventType_Obstacle,
		EventType_RoadBlock,
		EventType_RoadConstruction,
		EventType_FireSmoke,
		EventType_Accident
	};

	// 仅ROI检测的事件类型列表
	static const auto roiEventTypes = {
		EventType_Jam,
		EventType_RoadConstruction,
		EventType_Accident
	};

	// 交通流偏移时考虑过滤的事件类型列表
	static const auto ignoreEventTypes = {
		EventType_Stop,
		EventType_Opposite,
		EventType_DriveIn,
		EventType_DriveAcross,
		EventType_DriveAway,
		EventType_Pedstrain,
		EventType_TwoWheels,
	};

	/*
	*　是否为ROI专有事件　
	*
	*  ***** 仅由ROI上报，其他区域勾选类型，只决定其范围目标是否参与ROI事件逻辑
	*/
	inline bool isROIEventType(EventType evtType) { return std::find(roiEventTypes.begin(), roiEventTypes.end(), evtType) != roiEventTypes.end(); };

	/*
	*　是否为可忽略的事件（交通流偏移时）
	*/
	inline bool canIgnoreWhenShifted(EventType evtType) { return std::find(ignoreEventTypes.begin(), ignoreEventTypes.end(), evtType) != ignoreEventTypes.end(); };
}
#endif //_EVENTTYPE_H