/**
 * Project AI事件分析模块
 */

#ifndef _TARGETTYPE_H
#define _TARGETTYPE_H

#include <map>

namespace evt
{
	/**
	 * 目标类型
	 */
	enum TargetType
	{
		// 行人
		TargetType_Pedstrain = 0,
		// 小汽车
		TargetType_Car,
		// 客车
		TargetType_Bus,
		// 货车
		TargetType_Truck,
		// 摩托车
		TargetType_TwoWheels
	};

    constexpr std::string_view PERSON_REF_LABEL = "ref";

	/**
	 * 目标计数线位置状态
	 */
	enum TargetCountLineState
	{
		TargetCountLineState_None,
		// 目标在计数线上方
		TargetCountLineState_Top,
		// 目标在计数线下方
		TargetCountLineState_Bottom,
		// 目标 完成计数线经过
		TargetCountLineState_Finished


	};
}
#endif //_TARGETTYPE_H