/**
 * Project AI事件分析模块
 */

#include <map>
#include "event_map.h"
#include "detector/event_detector.h"
#include "util/scene_utility.h"
#include "config/event_config.hpp"
#include "config/global_region_config.h"

/**
 * EventMap implementation
 * 
 * 事件地图
 * 负责记录事件位置和维护事件状态
 */

#define CACHE_UPDATE_INTERVAL 25		// 全局缓存事件更新频率 （帧）

namespace evt
{
	EventMap::EventMap(int w, int h, int cacheTime) {
		cachedEventsTree = new QuadTree({0.0f, 0.0f, (float)w, (float)h});

		this->evtCacheTime = cacheTime;
	}

	EventMap::~EventMap()
	{
		delete cachedEventsTree;
	}

	/**
	 * 更新缓存事件
	 */
	void EventMap::update()
	{
		if (evtCacheTime == 0)
			return;

		if (updateIndex++ % CACHE_UPDATE_INTERVAL != 0)
			return;

		SceneObjectList cachedEvents;
		cachedEventsTree->getAllObjects(cachedEvents);

		auto systime = systemTimestamp();
		for (auto& obj : cachedEvents)
		{
			auto evt = std::static_pointer_cast<Event>(obj);
			auto evtCheckTime = evt->getRemoveTime();
			if (evtCheckTime > 0)
			{
				auto passed = systime - evtCheckTime;
				//IVA_LOG_WARN("cache event passed seconds {}", passed / 1000);
				if (evtCheckTime + evtCacheTime * 1000 < systime)
					cachedEventsTree->remove(evt);
			}
		}
		//IVA_LOG_WARN("cache event count {}", cachedEvents.size());
	}

	/**
	 * 新增事件
	 * @param evt
	 */
	void EventMap::addEvent(EventPtr evt) {
		typeEvents[evt->getType()].push_back(evt);
	}

	/**
	 * 删除事件
	 * @param evt
	 */
	void EventMap::delEvent(EventPtr evt) {
		typeEvents[evt->getType()].remove(evt);
		if (evt->detector != NULL)
			evt->detector->removeHoldEvent(evt);

		if (evt->target != NULL && evt->target->getEventID() == evt->getID())
			evt->target->updateEvent(NULL);
	}

	/**
	 * 检查缓存事件
	 * @param evt
	 */
	void EventMap::checkCacheEvent(EventPtr evt)
	{
		if (evtCacheTime == 0)
			return;

		auto cacheEvt = matchCacheEvent(evt);
		if (cacheEvt != NULL)
		{
			evt->ignored = true;
			IVA_LOG_INFO("ROI:{} [event ignored] since global map cached! id {} type {}", evt->getROIID(), evt->getID(), evt->getType());
			cachedEventsTree->remove(cacheEvt);
		}
		cachedEventsTree->insert(evt);
	}

	/**
	 * 匹配缓存事件
	 * @param evt
	 */
	EventPtr EventMap::matchCacheEvent(EventPtr evt)
	{
		auto evtRect = evt->getOccurRect();
		EventList cachedEvts;
		getCacheEventsByRect(evtRect, evt->getType(), cachedEvts);

		for (auto& cache : cachedEvts)
		{
			auto cacheRect = cache->getOccurRect();
			//IVA_LOG_WARN("cache IOU {} DIS {}", calculateIOU(evtRect, cacheRect), calculateDistance(evtRect.getCenterPosition(), cacheRect.getCenterPosition()));
			if (evt->getID() != cache->getID() && calculateIOU(evtRect, cacheRect) > ((float)EVENT_CFG->cacheMatchIOU() / 100.f)
				&& !checkIfDistanceGreaterThan(evtRect.getCenterPosition(), cacheRect.getCenterPosition(), EVENT_CFG->cacheMatchDistance()))
			{
				return cache;
			}
		}
		return NULL;
	}

	/**
	 * 基于位置获取事件列表
	 * @param r
	 * @param evtType
	 * @param evtList
	 */
	void EventMap::getCacheEventsByRect(Rect r, EventType evtType, EventList& evtList) {
		auto objList = cachedEventsTree->getObjectsInBound(r);
		for (auto obj : objList)
		{
			auto evt = std::static_pointer_cast<Event>(obj);
			if (evt->getType() == evtType)
			{
				evtList.push_back(evt);
			}
		}
	}

	/**
	 * 基于类型获取事件列表
	 * @param type 事件类型
	 * @return EventList
	 */
	EventList EventMap::getEventsByType(EventType type) {
		return typeEvents[type];
	}

	/**
	 * 添加可超时撤回的事件
	 * @param eType
	 * @param passTime
	 */
	void EventMap::addWithdrawEvent(EventType eType, long long compareTime, EventPtr evt )
	{		
		clearOverTimeEvent(eType, compareTime);
		withdrawEvents[eType].emplace_back(evt->info);
	}

	/**
	 * 清除指定类型的超时撤回事件
	 * @param eType
	 * @param passTime
	 */
	void EventMap::clearOverTimeEvent( EventType eType, long long compareTime)
	{
		auto& oldList = withdrawEvents[eType];
		if (oldList.empty())
			return;
		std::list<EventInfo> newList;
		for (auto& ei : oldList)
		{
			if (ei.occurTime >= compareTime )
				newList.emplace_back(ei);
		}
		withdrawEvents[eType] = newList;
	}

	/**
	 * 获得要撤回的指定时间之内的事件列表
	 * @param tm
	 */
	std::list<EventInfo> EventMap::getWithdrawEvent( EventType eType, long long compareTime, int roiID )
	{
		std::list<EventInfo> returnList, newList;
		auto& oldList = withdrawEvents[eType];
		if (oldList.empty())
			return returnList;

		for (auto& ei : oldList)
		{
			if (ei.occurTime >= compareTime )
			{
				if (roiID == 0 || roiID == ei.roiID)
					returnList.emplace_back(ei);
				else
					newList.emplace_back(ei);
			}
		}

		withdrawEvents[eType] = newList;
		return returnList;
	}

	/**
	 * 查询事件
	 * @param info 事件信息
	 * @return EventPtr
	 */
	EventPtr EventMap::getEventByInfo(EventInfo& info) {
		auto type = info.type;
		for (auto& evtptr : typeEvents[type])
		{
			if (evtptr->getID() == info.id)
				return evtptr;
		}
		return NULL;
	}
}
