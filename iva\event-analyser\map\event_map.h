/**
 * Project AI事件分析模块
 */

#ifndef _EVENTMAP_H
#define _EVENTMAP_H

#include <map>
#include "quad_tree.h"
#include "element/event.h"
#include "element/target.h"

/**
* 事件地图
* 负责记录事件位置和维护事件状态
*/
namespace evt
{
	class EventMap {
		friend class Scene;
	public:

		EventMap(int w, int h, int cacheTime= 0);
		~EventMap();

		/**
		 * 更新缓存
		 */
		void update();

		/**
		 * 新增事件
		 * @param evt
		 */
		void addEvent(EventPtr evt);

		/**
		 * 删除事件
		 * @param evt
		 */
		void delEvent(EventPtr evt);

		/**
		 * 检查缓存事件
		 * @param evt
		 */
		void checkCacheEvent(EventPtr evt);

		/**
		 * 匹配缓存事件
		 * @param evt
		 */
		EventPtr matchCacheEvent(EventPtr evt);

		/**
		 * 基于位置获取缓存事件列表
		 * @param r
		 * @param evtType
		 * @param evtList
		 */
		void getCacheEventsByRect(Rect r, EventType evtType, EventList & evtList);

		/**
		 * 基于类型获取事件列表
		 * @param type 事件类型
		 */
		EventList getEventsByType(EventType type);

		/**
		 * 查询事件
		 * @param info 事件信息
		 * @return EventPtr
		 */
		EventPtr getEventByInfo(EventInfo& info);
		
		/**
		 * 清除过时的待撤回事件
		 * @param eType
		 * @param compareTime
		 */
		void clearOverTimeEvent(EventType eType, long long compareTime );

		/**
		 * 获得要撤回的指定时间之内的事件列表
		 * @param eType
		 * @param compareTime
		 * @param roiID
		 */
		std::list<EventInfo> getWithdrawEvent(EventType eType, long long compareTime, int roiID = 0);

		/**
		 * 添加可撤回事件
		 * @param eType
		 * @param compareTime
		 * @param info
		 */
		void addWithdrawEvent(EventType eType, long long compareTime, EventPtr evt );

	private:
		QuadTree* cachedEventsTree;

		// 更新序号
		int updateIndex = 0;
		// 事件缓存时间（秒）
		int evtCacheTime = 0;

		// 当前持有事件
		std::map<EventType, EventList> typeEvents;

		// 撤回事件集
		std::map<EventType, std::list<EventInfo> > withdrawEvents;
	};
}
#endif //_EVENTMAP_H
