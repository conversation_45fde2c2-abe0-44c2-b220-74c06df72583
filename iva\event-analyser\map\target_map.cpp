/**
 * Project AI事件分析模块
 */

#include <map>
#include <math.h>
#include "target_map.h"
#include "area/roi.h"

/**
 * TargetMap implementation
 *
 * 目标地图
 *
 * 负责记录目标位置 方便快速按范围进行目标匹配
 */

namespace evt
{
	TargetMap::TargetMap(int w, int h) 
	{
		targetTree = new QuadTree({ 0.0f, 0.0f, (float)w, (float)h });
		frameWidth = w;
		frameHeight = h;

		gridSizeW = frameWidth / HEATMAP_GRID_WIDTH;
		gridSizeH = frameHeight / HEATMAP_GRID_HEIGHT;
		gridWidth = HEATMAP_GRID_WIDTH;
		gridHeight = HEATMAP_GRID_HEIGHT;
	}

	TargetMap::~TargetMap()
	{
		delete targetTree;
	}

	/**
	 * 新帧开始
	 */
	void TargetMap::newFrameStart()
	{
		for (auto& p: heatmap)
		{
			int index = p.first;
			auto& heatinfo = p.second;
			heatinfo.occupied = false;
			heatinfo.heatValue--;
			if (heatinfo.heatValue < 0)
				heatinfo.heatValue = 0;
		}
	}

	/**
	 * 新增目标
	 * @param target
	 */
	void TargetMap::addTarget(TargetPtr target, ROI* roi) {
		targetTree->insert(target);
		updateHeatmapCount(target, roi);
	}

	/**
	 * 删除目标
	 * @param target
	 */
	void TargetMap::delTarget(TargetPtr target) {
		targetTree->remove(target);
	}

	/**
	 * 更新热力图
	 * @param target
	 */
	void TargetMap::updateHeatmapCount(TargetPtr target, ROI* roi)
	{
		if (!target->isVehicleType()) return;

		Rect rect = target->getLatestRect();
		int left = round(rect.getLeft() / gridSizeW);
		int top = round(rect.getTop() / gridSizeH);
		int right = floor(rect.getRight() / gridSizeW);
		int bottom = floor(rect.getBottom() / gridSizeH);

		for (int i = left; i <= right; i++)
		{
			for (int j = top; j <= bottom; j++)
			{
				int index = i + gridWidth * j;
				if (roi && roi->containHeatmapIndex(index))
				{
					auto& heatinfo = heatmap[index];
					heatinfo.occupied = true;
					heatinfo.heatValue++;
					if (heatinfo.heatValue > HEATMAP_MAX_FRAME)
						heatinfo.heatValue = HEATMAP_MAX_FRAME;
				}
			}
		}
	}

	/**
	 * 更新区域热力图索引
	 * @param polygon
	 * @param indexs
	 */
	void TargetMap::initAreaHeatMapIndexs(Polygon& polygon, std::vector<int>& indexs)
	{
		indexs.clear();
		auto rect = polygon.boundingBox();

		int left = round(rect.getLeft() / gridSizeW);
		int top = round(rect.getTop() / gridSizeH);
		int right = floor(rect.getRight() / gridSizeW);
		int bottom = floor(rect.getBottom() / gridSizeH);

		for (int i = left; i <= right; i++)
		{
			for (int j = top; j <= bottom; j++)
			{
				Point checkpoint((i+ 0.5f) * gridSizeW, (j+0.5f) * gridSizeH);
				if (polygon.containPoint(checkpoint))
				{
					int index = i + gridWidth * j;
					indexs.push_back(index);
				}
			}
		}
	}

	/**
	 * 获取区域热力图值
	 * @param indexs
	 * @param level
	 */
	float TargetMap::getHeatValue(std::vector<int>& indexs, int level)
	{
		int totalSize = indexs.size();
		if (totalSize == 0)
			return 0.f;

		int statisticCount = 0;
		for (auto& index : indexs)
		{
			if (heatmap.find(index) == heatmap.end())
				continue;

			auto& heatinfo = heatmap[index];
			if(heatinfo.heatValue >= level)
				statisticCount++;
		}
		float heatValue = (float)statisticCount / (float)totalSize;
		return heatValue;
	}

	/**
	 * 获取区域占比
	 * @param indexs
	 */
	float TargetMap::getOccupiedValue(std::vector<int>& indexs)
	{
		int totalSize = indexs.size();
		if (totalSize == 0)
			return 0.f;

		int statisticCount = 0;
		for (auto& index : indexs)
		{
			if (heatmap.find(index) == heatmap.end())
				continue;

			auto& heatinfo = heatmap[index];
			if (heatinfo.occupied)
				statisticCount++;
		}
		float heatValue = (float)statisticCount / (float)totalSize;
		return heatValue;
	}

	/**
	 * 更新目标
	 * @param target
	 */
	void TargetMap::updateTarget(TargetPtr target, ROI* roi)
	{
		targetTree->update(target);
		updateHeatmapCount(target, roi);
	}

	/**
	 * 基于范围获取目标集合
	 * @param r
	 */
	TargetList TargetMap::getTargetsByRect(Rect r, int roiID) {
		auto objList = targetTree->getObjectsInBound(r);
		TargetList targetList;
		for (auto obj : objList)
		{
			auto target = std::static_pointer_cast<Target>(obj);
			if(roiID ==0 || target->getROIID() == roiID)
				targetList.push_back(target);
		}
		return targetList;
	}
}
