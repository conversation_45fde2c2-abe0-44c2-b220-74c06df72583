/**
 * Project AI事件分析模块
 */


#include "mask_value.h"
#include <iostream>

 /**
  * MaskValue implementation
  *
  * 掩码
  */

namespace evt
{
	MaskValue::MaskValue():flags(0)
	{
	}

	MaskValue::MaskValue(int val)
	{
		this->flags = val;
	}

	/**
	 * 掩码是否包含位
	 * @param bit 位
	 */
	bool MaskValue::contains(short bit, int start)
	{
		if (bit > 32)
		{
			std::cerr << "Mask value bit too long: " << bit << std::endl;
			return false;
		}

		int move = bit - start;
		uint_fast32_t mask = 1;
		mask = mask << move;
		bool bEnable = (this->flags & mask) >> move;
		return bEnable;
	}

    /**
     * 掩码是否仅仅包含一些组合的位
     * @param bit 位
     */
    bool MaskValue::containsOnly(std::initializer_list<short> bits, int start)
    {
        uint_fast32_t mask = 0;
        for (const auto& bit : bits)
        {
            int move = bit - start;
            mask |= 1 << move;
        }
        return ((this->flags & mask) > 0 && (this->flags & ~mask) == 0);
    }

	/**
	 * 设置掩码
	 * @param bit 位
	 * @param val
	 * @param start
	 */
	bool MaskValue::set(short bit, bool val, int start)
	{
		if (bit > 32)
		{
			std::cerr << "Mask value bit too long: " << bit << std::endl;
			return false;
		}

		int move = bit - start;
		uint_fast32_t mask = 1;
		mask = mask << move;
		if (val)
			this->flags |= mask;
		else
			this->flags &= ~mask;

		return true;
	}

	/**
	 * 重置掩码
	 */
	void MaskValue::reset()
	{
		this->flags = 0;
	}

	bool MaskValue::empty()
	{
		return this->flags == 0;
	}
}
