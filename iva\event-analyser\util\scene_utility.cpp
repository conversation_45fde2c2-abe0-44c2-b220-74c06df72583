/**
 * Project AI事件分析模块
 */

#include "scene_utility.h"
#include <sys/timeb.h>
#include <math.h>
#include <sstream>
#include <random>

/**
 * 相关通用工具方法
 */
namespace evt
{

	/**
	 * IOU计算
	 * @param rectA
	 * @param rectB
	 */
	float calculateIOU(Rect rectA, Rect rectB) {

		if (rectA.getLeft() > rectB.getRight()) { return 0.0f; }
		if (rectA.getTop() > rectB.getBottom()) { return 0.0f; }
		if (rectA.getRight() < rectB.getLeft()) { return 0.0f; }
		if (rectA.getBottom() < rectB.getTop()) { return 0.0f; }
		float colInt = std::min(rectA.getRight(), rectB.getRight()) - std::max(rectA.getLeft(), rectB.getLeft());
		float rowInt = std::min(rectA.getBottom(), rectB.getBottom()) - std::max(rectA.getTop(), rectB.getTop());
		float intersection = colInt * rowInt;
		float areaA = rectA.width * rectA.height;
		float areaB = rectB.width * rectB.height;
		float unionArea = areaA + areaB - intersection;

		if (unionArea != 0.0f)
		{
			float intersectionPercent = intersection / unionArea;
			return intersectionPercent;
		}
		return 0.0f;
	}

	/**
	 * 距离计算
	 * @param p1
	 * @param p2
	 */
	float calculateDistance(Point p1, Point p2) {
		return sqrt(calculateDistanceSquare(p1, p2));
	}

	/**
	 * 距离计算（平方），避免开根号消耗
	 * @param p1
	 * @param p2
	 */
	float calculateDistanceSquare(Point p1, Point p2) {
		return (p1.x - p2.x)*(p1.x - p2.x) + (p1.y - p2.y) *(p1.y - p2.y);
	}

	/**
	 * 距离检查
	 * @param p1
	 * @param p2
	 * @param dis
	 */
	bool checkIfDistanceGreaterThan(Point p1, Point p2, float dis) {
		return calculateDistanceSquare(p1, p2) > (dis * dis);
	}
	
	/**
	 * 获取当前系统时间戳
	 */
	long long systemTimestamp() {
		struct timeb t1;
		ftime(&t1);
		return t1.time * 1000 + t1.millitm;
	}

	/**
	 * 判断点是否在直线左边
	 * @param p
	 * @param p1
	 * @param p2
	 */
	bool leftOfLine(Point p, Point p1, Point p2, int w, int h ) {
		if (p1.x == p2.x)
		{
			return p.x < p1.x * w;
		}

		if (p1.y == p2.y)
		{
			return p.y < p1.y * h;
		}

		if (p2.x < p1.x)
		{
			Point tmp = p2;
			p2 = p1;
			p1 = tmp;
		}
		float ret = (p2.y* h - p.y) * (p2.x* w - p1.x* w) - (p2.y* h - p1.y* h) * (p2.x* w - p.x);
		return ret < 0.0f;
	}

	unsigned int random_char() {
		std::random_device rd;
		std::mt19937 gen(rd());
		std::uniform_int_distribution<> dis(0, 255);
		return dis(gen);
	}

	/**
	 * 生成UUID  TODO 随机性有待验证 
	 * 对于事件ID 理论上应该足够
	 * @param len 字符长度
	 */
	std::string generateUUID(const unsigned int len)
	{
		std::stringstream ss;
		for (unsigned int i = 0; i < len; i++) {
			const auto rc = random_char();
			std::stringstream hexstream;
			hexstream << std::hex << rc;
			auto hex = hexstream.str();
			ss << (hex.length() < 2 ? '0' + hex : hex);
		}
		return ss.str();
	}
}