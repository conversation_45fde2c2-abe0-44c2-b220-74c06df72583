cmake_minimum_required (VERSION 3.5.2)

## 目标生成
set(TARGET_LIBRARY "image_analyser")
# 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/out/lib)

FIND_PACKAGE( OpenMP REQUIRED)
if(OPENMP_FOUND)
    message("OPENMP FOUND")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
endif()

add_compile_options(-g -std=c++17 -fPIC -fstack-protector-all -Wno-unknown-pragmas -Wno-unused-variable -Werror=return-type -Wall)

# boost
set(BOOST_HOME "/opt/boost")

# opencv
set(OPENCV_HOME "/usr/local")

list(APPEND OPENCV_LIB
        opencv_tracking
        opencv_core
        opencv_imgproc
        opencv_highgui
        opencv_video
        opencv_videoio
        opencv_flann
        opencv_calib3d
        opencv_imgcodecs
        opencv_dnn
        opencv_img_hash
        )

if(${PLATFORM} MATCHES "NVIDIA")
list(APPEND OPENCV_LIB
        opencv_features2d
        opencv_xfeatures2d
        opencv_bgsegm
        opencv_objdetect
        opencv_cudabgsegm
        opencv_cudafeatures2d
        opencv_cudaimgproc
        opencv_cudaoptflow
        opencv_cudawarping
        )
endif()

# TensorRT
set(TensorRT_HOME "/usr/local/TensorRT")
set(TensorRT_LIB
    nvinfer
    nvinfer_plugin
)

# CUDA
set(CUDA_HOME "/usr/local/cuda")
set(CUDA_LIB
    cudart
    cuda
    cublas
)

# 头文件
include_directories(
    ${OpenCV_INCLUDE_DIRS}
    ${BOOST_HOME}/include/
    ${TensorRT_HOME}/include/
    ${CUDA_HOME}/include/
    ${PROJECT_SOURCE_DIR}/iva-log
    ${PROJECT_SOURCE_DIR}/common
    ${PROJECT_SOURCE_DIR}/image-analyser
    ./include
)

# 库路径
link_directories(
    ${OPENCV_HOME}/lib/
    ${TensorRT_HOME}/lib/
    ${CUDA_HOME}/lib64/
    ${LIBRARY_OUTPUT_PATH}
)

# cpp
#FILE(GLOB algo "algo/*.cpp")
FILE(GLOB config "config/*.cpp")
FILE(GLOB roadblock_detect "detector/roadblock_detect/*.cpp")
FILE(GLOB util "util/*.cpp")

if(${PLATFORM} MATCHES "NVIDIA")
    FILE(GLOB detector "detector/*.cpp")
    FILE(GLOB roi_object_detect "detector/roi_object_detect/*.cpp")
    FILE(GLOB model "model_warehouse/*.cpp")
else()
    FILE(GLOB detector "detector/base_detector.cpp" "detector/firesmoke_detector.cpp" "detector/roadblock_detector.cpp" "detector/camera_offset_detector.cpp" "detector/throwaway_detector.cpp")
endif()

FILE(GLOB src "*.cpp")

SET(ALL_SRC ${include} ${detector} ${config} ${util} ${model} ${roi_object_detect} ${roadblock_detect} ${src} )

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})

list(APPEND TARGET_LINK_LIBS ${OPENCV_LIB} ivalog stdc++fs ivacommon)
if(${PLATFORM} MATCHES "NVIDIA")
    list(APPEND TARGET_LINK_LIBS ${TensorRT_LIB} ${CUDA_LIB} gpu_util)
endif()

target_link_libraries(${TARGET_LIBRARY} ${TARGET_LINK_LIBS})

#gpu-util工具库
if(${PLATFORM} MATCHES "NVIDIA")
    add_subdirectory(util/gpu-util)
endif()
