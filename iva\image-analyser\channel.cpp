/**
 * Project IVA (image analyser)
 */

#include "channel.h"
#include "config/detect_config.hpp"
#include "detector/camera_offset_detector.h"

#ifdef NVIDIA
#include "detector/roi_object_detector.h"
#endif
#ifdef HUAWEI
#include "detector/throwaway_detector.h"
#endif

#include "detector/roadblock_detector.h"
#include "detector/firesmoke_detector.h"

 /**
 * 图像检测通道
 */
namespace ia
{
	// 抛洒物 检测频率
	#define ROI_OBJECT_DETECT_INTERVAL 30

	Channel::Channel(int id, int deviceID): channelID(id), deviceId(deviceID)
	{
		detectors[DetectorType::Offset] = new CameraOffsetDetector();

		detectors[DetectorType::Firesmoke] = new FiresmokeDetector();
		detectors[DetectorType::Roadblock] = new RoadblockDetector();
#ifdef NVIDIA
		detectors[DetectorType::Roiobject] = new ROIObjectDetector(ROI_OBJECT_DETECT_INTERVAL, deviceId);
#endif
#ifdef HUAWEI
        detectors[DetectorType::Roiobject] = new ThrowawayDetector();
#endif

		detectorConfigStates[DetectorType::Firesmoke] = DETECT_CFG->detectFireSmoke();
		detectorConfigStates[DetectorType::Roadblock] = DETECT_CFG->detectRoadblock();
		detectorConfigStates[DetectorType::Roiobject] = DETECT_CFG->detectThrowaway();
		detectorConfigStates[DetectorType::Offset] = DETECT_CFG->detectCamOffset();

		omp_init_lock(&ompLock);
	}

	Channel::~Channel()
	{
		for (auto&[type, d] : detectors)
		{
			delete d;
		}
		detectors.clear();
		omp_destroy_lock(&ompLock);
	}

	/**
	* 核心检测流程接口
	* @param frameData 输入数据
	*/
	OutputData Channel::process(const FrameData& frameData)
	{
		OutputData output;
		bool flagReset = dirtyFlagReset;

		omp_set_num_threads(4);
#pragma omp parallel for
		for (uint i = 0; i < detectors.size(); i++)
		{
			auto it = detectors.begin();
			advance(it, i);

			DetectorType type = it->first;
			ia::BaseDetector* d = it->second;

			if (flagReset)
				d->reset();

			if (!getEnable(type))
				continue;

			d->updateFrameIndex(frameData.frameIndex);
			if (d->getCatogory() == DetectorCatogory::State)
			{
				auto stateDetector = dynamic_cast<StateDetector*>(d);
				bool state = stateDetector->detect(frameData);
				omp_set_lock(&ompLock);
				output.frameState[type] = state;
				omp_unset_lock(&ompLock);
			}
			else
			{
				auto objectDetector = dynamic_cast<ObjectDetector*>(d);
				auto objs = objectDetector->detect(frameData);
				omp_set_lock(&ompLock);
				output.outputObjects.try_emplace(type, std::move(objs));
				omp_unset_lock(&ompLock);
			}
		}


		if (flagReset)
			dirtyFlagReset = false;

		return output; // NRVO
	}

	/**
	* 重置通道
	*/
	void Channel::reset()
	{
		dirtyFlagReset = true;
	}

	/**
	* 设置检测器开关
	* @param type 检测器类型
	* @param enable 开关值
	*/
	void Channel::setEnable(DetectorType type, bool enable)
	{
		std::lock_guard<std::mutex> lk(this->mutexStates);
		if (enable && detectorConfigStates.find(type) != detectorConfigStates.end())
		{
			detectorStates[type] = detectorConfigStates[type];
		}
		else
		{
			detectorStates[type] = enable;
		}
	}

	/**
	* 获取检测器开关
	* @param type 检测器类型
	*/
	bool Channel::getEnable(DetectorType type)
	{
		std::lock_guard<std::mutex> lk(this->mutexStates);
		return detectorStates[type];
	}

	/**
	* 当前帧是否需要画面帧数据
	* （提供给上游任务 帧采样频率，降低设备侧数据搬运成本）
	*/
	bool Channel::needFrameMat()
	{
		for (auto& [type, d] : detectors)
		{
			if (d->needRawFrame())
				return true;
		}
		return false;
	}


	/**
	* 设置检测器 子区域掩码
	* @param pts 区域坐标集
	* @param detectType 检测器类型
	*/
	void Channel::setCheckAreaMask(const RegionPositions& pts, DetectorType detectType)
	{
		RegionPoints offsetPts;
		for (auto& datas : pts)
		{
			std::vector<cv::Point2d> points;
			for (int i = 0; i < static_cast<int>(datas.size()); i += 2)
			{
				points.emplace_back(datas[i], datas[i + 1]);
			}
			offsetPts.emplace_back(std::move(points));
		}

		auto detector = getDetector(detectType);
		if (detector != nullptr)
		{
			detector->setRegionMask(offsetPts);
		}
	}

	/**
	* 获取检测器
	* @param type 检测器类型
	*/
	BaseDetector* Channel::getDetector(DetectorType detectorType)
	{
		for (auto& [type, d] : detectors)
		{
			if (type == detectorType)
				return d;
		}
		return nullptr;
	}
}
