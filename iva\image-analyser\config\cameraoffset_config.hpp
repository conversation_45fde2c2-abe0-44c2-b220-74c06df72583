#pragma once
#include "ini/inibase.h"

/*
 * 偏移 检测配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::cameraoffset, "config/camera_offset.ini",
(double, scoresThres,	            model, 0.3,	u8"")
(double, validSegResThres,	        multiVoting, 0.2, u8"")
(int, validSegPointThres,      	    multiVoting, 20, u8"")
(int, framesNum,	                multiVoting, 2, u8"")
(double, matchRatioOffsetThres1,    multiVoting, 0.6, u8"")
(double, matchRatioOffsetThres2,	multiVoting, 0.8, u8"")
(double, matchRatioNormalThres1,	multiVoting, 0.99, u8"")
(double, matchRatioNormalThres2,	multiVoting, 0.9, u8"")
(int, countThres1,	                multiVoting, 1, u8"")
(int, countThres2,	                multiVoting, 2, u8"")
(int, dilationSize,	                filter, 3, u8"")
(int, erodeSize,	                filter, 3, u8"")
(int, validRoiWidthThres,           filter, 15, u8"")
(int, idChangeTime,	                filter, 20, u8"")
(bool, saveDebug,	                debug, false,	u8"")
(bool, isSaveSkeleton,	            debug, false,	u8"")
)

#define CAMERAOFFSET_CFG ia::cameraoffset::Config::instance()
