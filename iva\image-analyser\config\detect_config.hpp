#pragma once
#include "ini/inibase.h"

/*
 * 图像模块配置 ( INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia,		"config/image_analyser.ini",
//(bool, detectFlicker,		option, false,	u8"开启花屏检测")
//(bool, detectFrameState,	option, false, u8"开启视频质量检测")
//(bool, detectFrameFoggy,	option, false, u8"开启大雾检测")
//(bool, detectFrameNight,	option, false, u8"开启昼夜检测")
(bool, detectCamOffset,		option, false, u8"开启偏移检测")
//(bool, detectCamReset,		option, true, u8"开启偏移恢复检测")
(bool, detectThrowaway,		option, true, u8"开启抛洒物检测")
(bool, detectRoadblock,		option, true, u8"开启路障检测")
(bool, detectFireSmoke,		option, true, u8"开启烟火检测")
//(int, globalNightCheckInterval, global, 60, u8"设置昼夜检测间隔(分钟)")
(string, modelPath,			model, "/data/opt/models", u8"模型目录")
)

#define DETECT_CFG ia::Config::instance()
