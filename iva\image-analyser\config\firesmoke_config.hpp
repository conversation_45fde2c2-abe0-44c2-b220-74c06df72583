#pragma once
#include "ini/inibase.h"

/*
 * 烟火 检测配置 (INI)
 *
 *    (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::firesmoke, "config/fire_smoke.ini",
(int, preprocessFrame, filter, 20, u8"预存帧数")
(int, frameAtLeast, filter, 2, u8"检出框个数阈值")
(int, distanceAtLeast, filter, 10, u8"检出框中心点阈值")
(bool, enablePostprocess, filter, true, u8"是否开启后处理,默认为true")
(double, minVariance, filter, 0.15, u8"框变化的最小方差值")
(double, maxVariance, filter, 5, u8"框变化的最大方差值")
(int, minTrackedCount, filter, 4, u8"框最小跟踪数量")
(bool, checkDirection, filter, false, u8"是否检测目标框移动方向")
)

#define FIRESMOKE_CFG ia::firesmoke::Config::instance()
