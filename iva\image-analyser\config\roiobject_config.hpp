#pragma once
#include "ini/inibase.h"

/*
 * 抛洒物 检测配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::roiobject, "config/roi_object.ini",

(int,       featureNum,			        featureModel,   6,		u8"缓存特征帧数")

(int,       detectInputW,	    	    detectModel,    96,     u8"检测模型输入宽度")
(int,       detectInputH,	    	    detectModel,    96,     u8"检测模型输入宽度")
(double,    detectScaleForSeg,		    detectModel,    2.0,    u8"切割roi的外扩系数")
(bool,      detectSegMode,		        detectModel,    true,   u8"切割模式")
(double,    detectNmsIouThres,		    detectModel,    0.05,   u8"nms的iou阈值")
(double,    detectScoresThres,		    detectModel,    0.25,   u8"nms的score阈值")
(double,    detectRectIouThres,		    detectModel,    0.1,    u8"判断检测结果有效性的iou阈值")
(int,       detectBatch,		        detectModel,    1,      u8"batch的数量")
(int,       detectPaddingValue,		    detectModel,    128,    u8"padding的像素值")

(double,    classScaleForSeg,		    classModel,     1.5,    u8"切割roi的外扩系数")
(double,    classScaleForPadding,		classModel,     1.1,    u8"切割roi的padding系数")
(bool,      classSegMode,		        classModel,     false,  u8"切割模式")
(double,    classScoresGip,		        classModel,     -0.1,   u8"分类模型的偏移量")
(int,       classBatch,		            classModel,     1,      u8"batch的数量")
(int,       classPaddingValue,		    classModel,     128,    u8"padding的像素值")

(double,    motionThres,		        bgGenerator,    0.31,   u8"")
(double,    bgFrontThres,		        bgGenerator,    0.35,   u8"")
(double,    unFillRatioThres,		    bgGenerator,    0.0,    u8"")

(int,       distanceValidNum,		    multiDistances, 3,      u8"")
(int,       distanceMoveNum,		    multiDistances, 2,      u8"")
(double,    distanceDisThres,		    multiDistances, 0.35,   u8"")

(int,       selectMinH,		            select,         5,      u8"")
(int,       selectMinW,		            select,         5,      u8"")
(int,       selectMaxH,		            select,         75,     u8"")
(int,       selectMaxW,		            select,         75,     u8"")
(int,       selectMaxHForFirstFilter,	select,         100,    u8"")
(int,       selectMaxWForFirstFilter,	select,         100,    u8"")
(double,    selectCarLeastWidth,		select,         35.0,   u8"")
(double,    selectScoreThresForBinary,	select,         0.6,    u8"")
(double,    selectIouThresForAlarm,		select,         0.4,    u8"")
(double,    selectIouThresForSelect,	select,         0.5,    u8"")
(bool,      selectIsOpening,		    select,         false,   u8"")
(double,    selectFarRatio,		        select,         0.4,    u8"")
(int,       selectRoadNums,		        select,         5,      u8"")
(double,    selectCarNumForRoad1,		select,         2.5,    u8"")
(double,    selectCarNumForRoad2,		select,         2.5,    u8"")
(double,    selectCarNumForRoad3,		select,         2.5,    u8"")
(double,    selectCarNumForRoad4,		select,         2.5,    u8"")
(double,    selectCarNumForRoad5,		select,         2.5,    u8"")

(int,       staticFrontTimesThres,		tracker,        3,     u8"")
(int,       maxDecreaseTimes,		    tracker,        3,      u8"")
(double,    compareIouThres1,		    tracker,        0.6,    u8"")
(double,    compareIouThres2,		    tracker,        0.35,   u8"")
(bool,      isUseRectFlag,		        tracker,        true,   u8"")
(double,    compareStaticThres,		    tracker,        0.6,    u8"")
(bool,      newTraceAppendFlag,		    tracker,        true,   u8"")

(string,    imgSaveDir,		            saveImg,        "./roi_object_debug",   u8"")
(string,    bgDir,		                saveImg,        "bg",             u8"")
(string,    checkBgDir,		            saveImg,        "checkBg",        u8"")
(string,    binaryDir,		            saveImg,        "binary",         u8"")
(string,    interestedDir,		        saveImg,        "interest",       u8"")
(string,    alarmDir,		            saveImg,        "alarm",          u8"")
(string,    classifyDir,		        saveImg,        "classify",       u8"")
(string,    detectDir,		            saveImg,        "detect",         u8"")
(string,    classSegDir,		        saveImg,        "classifySeg",    u8"")
(string,    detectSegDir,		        saveImg,        "detectSeg",      u8"")
(bool,      saveBgImgFlag,		        saveImg,        false, u8"")
(bool,      saveBinaryImgFlag,		    saveImg,        false, u8"")
(bool,      saveOtherImgsFlag,		    saveImg,        false, u8"")

)

#define ROIOBJECT_CFG ia::roiobject::Config::instance()
