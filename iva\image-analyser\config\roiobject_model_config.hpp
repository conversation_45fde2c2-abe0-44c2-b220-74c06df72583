#pragma once
#include "ini/inibase.h"

/*
 * 抛洒物模型 检测配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::roiobject_model, "/data/opt/models/roiobject/roi_object_model.ini",

(string,   featureModelPath,		Model,     "roiobject/roiobject_feature.trt",    u8"模型相对路径")
(string,   detectModelPath,		    Model,     "roiobject/roiobject_detect.trt",     u8"模型相对路径")
(string,   classModelPath,		    Model,     "roiobject/roiobject_class.trt",      u8"模型相对路径")

)

#define ROIOBJECT_MODEL_CFG ia::roiobject_model::Config::instance()
