#pragma once
#include "ini/inibase.h"

/*
 * 路障 检测配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::throwaway, "config/throw_away.ini",
(int,    framesNum,		    throwaway,   3,	 u8"缓存帧数")
(int,    matchedCount,		throwaway,   2,	 u8"在缓存帧中出现该目标多少次")
(double, iouThres,		    throwaway,   0.38, u8"目标的当前框与历史框IOU阈值")
(bool,   isFilterObject,    throwaway, true, u8"是否过滤检出框,默认为true")
(double, ratioThres,	    throwaway, 0.8,	 u8"目标框和子区域重叠的IOU阈值")
(bool,   enablePostprocess, throwaway, true, u8"是否开启后处理,默认为true")
)

#define THROWAYAY_CFG ia::throwaway::Config::instance()
