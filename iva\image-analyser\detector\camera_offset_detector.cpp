/**
 * Project IVA (image analyser)
 */
#include <map>
#include <atomic>
#include <chrono>
#include <iostream>
#include "camera_offset_detector.h"
#include "config/detect_config.hpp"
#include "config/cameraoffset_config.hpp"

using namespace std;


namespace ia
{

	CameraOffsetDetector::CameraOffsetDetector()
	{
        setParams();
    }

	void CameraOffsetDetector::setParams()
	{
        //seg scoreThres
        m_cameraOffsetParams.modelScores = CAMERAOFFSET_CFG->scoresThres();

        //Multi-frame voting parameters 
        m_cameraOffsetParams.validSegResThres = CAMERAOFFSET_CFG->validSegResThres();
        m_cameraOffsetParams.validSegPointThres = CAMERAOFFSET_CFG->validSegPointThres();
        m_cameraOffsetParams.framesNum = CAMERAOFFSET_CFG->framesNum();
        m_cameraOffsetParams.matchRatioOffsetThres1 = CAMERAOFFSET_CFG->matchRatioOffsetThres1();
        m_cameraOffsetParams.matchRatioOffsetThres2 = CAMERAOFFSET_CFG->matchRatioOffsetThres2();
        m_cameraOffsetParams.matchRatioNormalThres1 = CAMERAOFFSET_CFG->matchRatioNormalThres1();
        m_cameraOffsetParams.matchRatioNormalThres2 = CAMERAOFFSET_CFG->matchRatioNormalThres2();
        m_cameraOffsetParams.countThres1 = CAMERAOFFSET_CFG->countThres1();
        m_cameraOffsetParams.countThres2 = CAMERAOFFSET_CFG->countThres2();

        //filter params
        m_cameraOffsetParams.dilationSize = CAMERAOFFSET_CFG->dilationSize();
        m_cameraOffsetParams.erodeSize = CAMERAOFFSET_CFG->erodeSize();
        m_cameraOffsetParams.validRoiWidthThres = CAMERAOFFSET_CFG->validRoiWidthThres();
        m_cameraOffsetParams.idChangeTime = CAMERAOFFSET_CFG->idChangeTime();

        //debug params
        m_cameraOffsetParams.saveDebug = CAMERAOFFSET_CFG->saveDebug();
        m_cameraOffsetParams.isSaveSkeleton = CAMERAOFFSET_CFG->isSaveSkeleton();
	}

	CameraOffsetDetector::~CameraOffsetDetector()
	{
	}

    void CameraOffsetDetector::setInterestedMaskByPoint(cv::Mat img){
        RegionPoints regionPoints = this->regionMask.get();
        if (!m_polyMaskInited && regionPoints.size() > 0)
        {
            m_polyMaskInited = true;
            int h = img.size().height*2;
            int w = img.size().width;

			m_gtLaneLines.clear();
			constexpr int INTERPOLATION_SIZE = 6;
			
            std::vector<std::vector<cv::Point>> roiPts;
            for (auto& datas : regionPoints)
            {
				std::vector<cv::Point> points;
				std::vector<cv::Point> interpolatedPoints;
                for (size_t i=0; i< datas.size(); i++)
                {
					cv::Point2f p(datas[i].x * (float)w, datas[i].y * (float)h );
                    points.push_back(p);

					if (i == datas.size()-1)
					{
						if(p.y > h/ 2)
							interpolatedPoints.emplace_back(p.x, p.y - h/2);
					}
					else
					{
						if(p.y > h/ 2)
							interpolatedPoints.emplace_back(p.x, p.y - h/2);

						cv::Point2f next(datas[i+1].x * (float)w, datas[i+1].y * (float)h);
						float vx = next.x - p.x;
						float vy = next.y - p.y;
						float vlen = sqrtf( vx * vx + vy * vy);
						if (vlen <= 0.f)
							continue;

						vx /= vlen;
						vy /= vlen;
						vx *= INTERPOLATION_SIZE;
						vy *= INTERPOLATION_SIZE;

						cv::Point2f inter(p.x + vx, p.y + vy);
						while (inter.x >= 0 && inter.y >= 0 && inter.x <= w && inter.y <= h)
						{
							float x = next.x - inter.x;
							float y = next.y - inter.y;
							float len = sqrtf(x * x + y * y);
							if (len < INTERPOLATION_SIZE)
								break;

							if (inter.y > h/ 2)
								interpolatedPoints.emplace_back(inter.x, inter.y - h/2);

							inter.x += vx;
							inter.y += vy;
						}
					}
                }
                roiPts.emplace_back(points);
				this->m_gtLaneLines.emplace_back(interpolatedPoints);
            }

            std::vector<int> validPolyIndex;
            cv::Mat tmpMask = cv::Mat::zeros(cv::Size(w,h),CV_8UC1);
            for(int i=0;i<roiPts.size();i++){
                tmpMask = tmpMask*0;
                cv::fillConvexPoly(tmpMask, roiPts[i], 255);
                if(isValidPoly(tmpMask))
                    validPolyIndex.push_back(i);
            }

            m_templateImg = cv::Mat::zeros(cv::Size(w,h),CV_8UC1);
            for(int i=0;i<validPolyIndex.size();i++){
                cv::polylines(m_templateImg, roiPts[validPolyIndex[i]], false, 255);
            }

            cv::Rect roi = cv::Rect(0,h/2,w,h/2);
            m_templateImg = m_templateImg(roi);
            cv::resize(m_templateImg,m_templateImg,cv::Size(img.size().width, img.size().height),(0.0),(0.0),cv::INTER_LINEAR);

            cv::Mat m_templateImgIn = m_templateImg.clone();
            m_templateImg = m_templateImg*0;
            getSkeleton(m_templateImgIn,m_templateImg);
            int m_templatePointsNum = cv::countNonZero(m_templateImg);

            if(m_cameraOffsetParams.saveDebug>0)
            {
                std::string command = "mkdir -p ../cameraOffsetMask";
                system(command.c_str());
                std::string path = "../cameraOffsetMask/cameraOffsetSkeleton_";
                path = path + "channel"+std::to_string(m_channel)+".jpg";
                cv::imwrite(path,m_templateImg);
            }

            cv::Mat element = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(m_cameraOffsetParams.dilationSize, m_cameraOffsetParams.dilationSize));
            cv::dilate(m_templateImg, m_templateImg, element);
            cv::threshold(m_templateImg, m_templateImg, 120, 127, cv::THRESH_BINARY);

			cv::dilate(m_templateImg, m_templateImgCompare, element);
            if(m_cameraOffsetParams.saveDebug>0)
			{
                std::string path = "../cameraOffsetMask/cameraOffsetMask_";
                path = path + "channel"+std::to_string(m_channel)+".jpg";
                cv::imwrite(path,m_templateImg);
            }
        }
    }

    void CameraOffsetDetector::reset()
    {
        m_matchRatios.clear();
        m_cameraStateType = FRAME_STATE_NORMAL;
        m_cameraState = CAM_STATE_NORMAL;
        m_lastCameraState = CAM_STATE_NORMAL;
        m_frameNum = 0;
        m_lastFrameNum = -100;
        historyState = false;
		m_polyMaskInited = false;
    }

    bool CameraOffsetDetector::isValidPoly(cv::Mat& mask){
        int num = 0;
        for(int i=0;i<mask.size().height;i++){
            std::vector<int> startCoords;
            std::vector<int> endCoords;
            int last = 0;
            for(int j=0;j<mask.size().width;j++){
                if(mask.at<uchar>(i,j)>0 && last==0){
                    startCoords.push_back(j);
                }
                if(mask.at<uchar>(i,j)==0 && last>0){
                    endCoords.push_back(j);
                }
                if(mask.at<uchar>(i,j)>0 && j==(mask.size().width-1)){
                    endCoords.push_back(j);
                }
                last = mask.at<uchar>(i,j);
            }
            for(int j=0;j<startCoords.size();j++){
                if((endCoords[j]-startCoords[j])>m_cameraOffsetParams.validRoiWidthThres){
                    num += 1;
                    if(num>5)
                        return false;
                }
            }
        }
        if(num>5)
            return false;
        else
            return true;
    }

    void CameraOffsetDetector::getSkeleton(cv::Mat& binary,cv::Mat& skeleton){
        for(int i=0;i<binary.size().height;i++){
            std::vector<int> startCoords;
            std::vector<int> endCoords;
            int last = 0;
            for(int j=0;j<binary.size().width;j++){
                if(binary.at<uchar>(i,j)>0 && last==0){
                    startCoords.push_back(j);
                }
                if(binary.at<uchar>(i,j)==0 && last>0){
                    endCoords.push_back(j);
                }
                if(binary.at<uchar>(i,j)>0 && j==(binary.size().width-1)){
                    endCoords.push_back(j);
                }
                last = binary.at<uchar>(i,j);
            }
            for(int j=0;j<startCoords.size();j++){
                int coordY = int((startCoords[j]+endCoords[j])/2);
                skeleton.at<uchar>(i,coordY) = 127;
            }
        }
    }

    float CameraOffsetDetector::calIouPoint(cv::Mat skeleton){
       try {
            int m_templatePointsNum = cv::countNonZero(m_templateImg);
            m_templatePointsNum = int(m_templatePointsNum/5);
            if(m_templatePointsNum<10){
                return -1;
            }
            int skeletonPointNum = cv::countNonZero(skeleton);
            if(skeletonPointNum < m_cameraOffsetParams.validSegPointThres)
            {
                return (m_cameraOffsetParams.matchRatioOffsetThres1+m_cameraOffsetParams.matchRatioOffsetThres2)/2;
            }else if(skeletonPointNum*1.0/m_templatePointsNum < m_cameraOffsetParams.validSegResThres)
            {
                return -1;
            }

			// 旧逻辑
            //cv::Mat img;
            //cv::bitwise_and(skeleton,m_templateImg,img);
            //int iouPointNum = cv::countNonZero(img);
            //return iouPointNum*1.0/skeletonPointNum;

/*** 第一版逻辑 ***/
			/*
			cv::Mat element = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(3, 3));
			cv::dilate(skeleton, skeleton, element);
			cv::erode(skeleton, skeleton, element);
			cv::dilate(skeleton, skeleton, element);

			vector<vector<cv::Point>> contours;
			findContours(skeleton, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

			std::vector<LaneLine> laneLines;
			for (size_t i = 0; i < contours.size(); i++) {

				double area = contourArea(contours[i]);
				if (area < 20) continue;

				auto rect = minAreaRect(contours[i]);

				LaneLine lane{ area ,rect };
				lane.quality = std::min (1.f, (float)area / 150.f);
				laneLines.push_back(lane);
			}

			std::sort(laneLines.begin(), laneLines.end(),
				[](LaneLine& a, LaneLine& b) {return a.area > b.area; });

			int index = 0;
			constexpr int MAX_COMPUTE_COUNT = 15;
			for (auto& lane : laneLines)
			{
				if (index++ > MAX_COMPUTE_COUNT)
					break;

				cv::Point2f vertices[4];
				lane.rect.points(vertices);
				vector<cv::Point> points;
				for (int i = 0; i < 4; i++) {
					points.push_back(vertices[i]);
				}
				cv::Mat laneMat = cv::Mat::zeros(m_templateImgCompare.size(), CV_8UC1);
				cv::fillConvexPoly(laneMat, points, cv::Scalar(255, 255, 255));

				cv::Mat result;
				cv::bitwise_and(laneMat, m_templateImgCompare, result);

				int laneAnd = cv::countNonZero(result);
				int laneOrigin = cv::countNonZero(laneMat);

				if(laneOrigin > 0)
					lane.matchedScore = (float)laneAnd / (float)laneOrigin;
			}

			std::sort(laneLines.begin(), laneLines.end(),
				[](LaneLine& a, LaneLine& b) {return a.matchedScore* a.quality > b.matchedScore * a.quality; });

			// TODO 待完善 更优的投票计分方式
			if(laneLines.size() > 0)
				return laneLines.begin()->matchedScore;
			else
				return -1;
				*/

/*** 第二版逻辑 ***/
				cv::Mat element = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(3, 3));
				cv::dilate(skeleton, skeleton, element);
				cv::erode(skeleton, skeleton, element);
				cv::dilate(skeleton, skeleton, element);

				//static bool saved = false;
				//cv::Mat debug = m_templateImgCompare.clone();

				std::vector<LaneLine> laneLines;
				for (auto& gtLane : m_gtLaneLines)
				{
					LaneLine line{0};
					int hits = 0;
					for (auto& p : gtLane)
					{
						//cv::circle(debug, p, 3, cv::Scalar(255, 255, 255));
						if(skeleton.at<char>(p) > 0)
							hits++;
						else
							hits = 0;

						if (hits > line.maxHits)
							line.maxHits = hits;
					}
					laneLines.emplace_back(line);
				}

				//if (!saved)
				//{
				//	saved = true;
				//	cv::imwrite("keypoint.png", debug);
				//	cv::imwrite("template.png", m_templateImgCompare);
				//}

				std::sort(laneLines.begin(), laneLines.end(),
					[](LaneLine& a, LaneLine& b) {return a.maxHits > b.maxHits; });

				constexpr int MAX_OFFSET_HIT = 1;
				constexpr int MIN_NORMAL_HIT = 3;
				constexpr int HIGHSCORE_NORMAL_HIT = 5;
				if (laneLines.size() > 0)
				{
					if(laneLines.size() > 1 && laneLines[1].maxHits > MIN_NORMAL_HIT)
						return 0.98f;
					else if (laneLines[0].maxHits > HIGHSCORE_NORMAL_HIT)
						return 0.95f;
					else if (laneLines[0].maxHits > MIN_NORMAL_HIT)
						return 0.85f;
					else if (laneLines[0].maxHits > MAX_OFFSET_HIT)
						return 0.75f;
					else
						return 0.5f;
				}

				return -1;

        } catch (...) {
            return -1;
        }
    }

    CurrFrameState CameraOffsetDetector::calOffsetState(float matchRatio){
        m_matchRatios.push_back(matchRatio);
        if(m_matchRatios.size()<max(m_cameraOffsetParams.countThres1,m_cameraOffsetParams.countThres2)){
            return FRAME_STATE_NORMAL;
        }
        if(m_matchRatios.size() > m_cameraOffsetParams.framesNum){
            m_matchRatios.erase(m_matchRatios.begin());
        }
        int count1=0;
        int count2=0;
        for(int i=0;i<m_matchRatios.size();i++){
            if(m_matchRatios[i]>m_cameraOffsetParams.matchRatioNormalThres1){
                count1++;
            }
            if(m_matchRatios[i]>m_cameraOffsetParams.matchRatioNormalThres2){
                count2++;
            }
        }
        if(count1>=m_cameraOffsetParams.countThres1 || count2>=m_cameraOffsetParams.countThres2){
            return FRAME_STATE_NORMAL;
        }
        count1=0;
        count2=0;
        for(int i=0;i<m_matchRatios.size();i++){
            if(m_matchRatios[i]<=m_cameraOffsetParams.matchRatioOffsetThres1){
                count1++;
            }
            if(m_matchRatios[i]<=m_cameraOffsetParams.matchRatioOffsetThres2){
                count2++;
            }
        }
        if(count1>=m_cameraOffsetParams.countThres1 || count2>=m_cameraOffsetParams.countThres2){
            return FRAME_STATE_OFFSET;
        }else{
            return FRAME_STATE_UNCERTAIN;
        }
    }


	bool CameraOffsetDetector::detect(const FrameData& frameData)
	{
		if (!shouldDetect())
			return historyState;

		// 获取上游模块已检出的 车道线数据
		cv::Mat binary = frameData.lanes;

		if(binary.empty()) // TODO 暂未兼容 上游模块无检测能力的情况
			return historyState;
        RegionPoints regionPoints = this->regionMask.get();
        if (regionPoints.size() <= 0)
            return historyState;
        int w = binary.size().width;
        int h = binary.size().height;
		cv::Rect roi = cv::Rect(0, h / 2, w, h / 2);
		binary = binary(roi);
		h = binary.size().height;
		cv::Mat skeleton = cv::Mat::zeros(cv::Size(w, h), CV_8UC1);


        setInterestedMaskByPoint(skeleton);
        for (int x = 0; x < binary.size().width; x++)
        {
            for (int y = 0; y < binary.size().height; y++)
            {
                if (binary.at<uchar>(y, x) > m_cameraOffsetParams.modelScores * 255) {
                    binary.at<uchar>(y, x) = 255;
                }
                else {
                    binary.at<uchar>(y, x) = 0;
                }
            }
        }
        cv::Mat element = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(m_cameraOffsetParams.erodeSize, m_cameraOffsetParams.erodeSize));
        cv::dilate(binary, binary, element);
        getSkeleton(binary,skeleton);

        if(m_cameraOffsetParams.isSaveSkeleton){
            std::string command = "mkdir -p ../skeleton";
            system(command.c_str());
            m_frameNum = (m_frameNum + 1) % 100;
			cv::imwrite("../skeleton/channel" + std::to_string(frameData.channelID) + "_" + std::to_string(m_frameNum) + ".jpg", skeleton + m_templateImg);
		}
        float matchRatio = calIouPoint(skeleton);
		
        if(matchRatio > -0.5){
            m_cameraStateType = calOffsetState(matchRatio);
        }else{
            m_cameraStateType = FRAME_STATE_UNCERTAIN;
        }
        if(m_cameraStateType == FRAME_STATE_NORMAL){
            m_cameraState = CAM_STATE_NORMAL;
        }else if(m_cameraStateType == FRAME_STATE_OFFSET){
            m_cameraState = CAM_STATE_OFFSET;
        }else{
            m_cameraState = m_lastCameraState;
        }
        m_lastCameraState = m_cameraState;
		if(m_cameraState == CAM_STATE_NORMAL){
			historyState = false;
			return false;
		}else{
			historyState = true;
			return true;
		}
	}

}
