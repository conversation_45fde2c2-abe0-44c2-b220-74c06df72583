/**
 * Project IVA (image analyser)
 */
#include <cmath>
#include "firesmoke_detector.h"
#include "log.h"
#include "module_option.h"
#include "config/firesmoke_config.hpp"
#include "util/object_statistic.h"

namespace ia
{
    constexpr auto MAX_OBJECT_CACHE_SIZE = 100; //!< 最大检出框缓存大小
    constexpr auto MIN_OBJECT_LIFE = -5;        //!< 最小生命值
    constexpr auto MIN_MOVE_DOWN_RATIO = 0.8;        //!< 向下运动的烟火占总数的比例最小值，向下运动的烟火累计次数大于该比例，则认为是向下运动
    constexpr auto MULTI_OBJECT_VARIANCE_COEFF = 7;        //!< 单个目标的方差阈值的倍数（一帧出现多个目标时，方差阈值相对单个目标较为宽松）
	FiresmokeDetector::FiresmokeDetector()
	{
		setParams();
	}

    /**
     * 重置后处理检测
     */
	void FiresmokeDetector::reset()
	{
		BaseDetector::reset();
        frameIndex = 0;
		frameDetections.clear();
		historyObjects.clear();
        trackedObjects.clear();
	}

    /**
     * 设置初始化参数
     */
	void FiresmokeDetector::setParams()
	{
		preprocessFrame = FIRESMOKE_CFG->preprocessFrame();
		frameAtLeast = FIRESMOKE_CFG->frameAtLeast();
		distanceAtLeast = FIRESMOKE_CFG->distanceAtLeast();
		enablePostprocess = FIRESMOKE_CFG->enablePostprocess();
        minVariance = FIRESMOKE_CFG->minVariance();
        maxVariance = FIRESMOKE_CFG->maxVariance();
        minTrackedCount = FIRESMOKE_CFG->minTrackedCount();
        enableCheckDirection = FIRESMOKE_CFG->checkDirection();
	}

    /**
     * 烟火过滤
     * @param objects 一帧中检出的目标框集合
     */
    void FiresmokeDetector::filterObject(ImageObjectList& objects)
    {
        auto currentFrameObjectSize = objects.size();
        //! 目标缓存处理
        for (auto it = trackedObjects.begin(); it !=trackedObjects.end(); )
        {
            if (--(it->second.life) < MIN_OBJECT_LIFE)
            {
                it = trackedObjects.erase(it);
                continue;
            }

            if ((int)it->second.tracks.size() > preprocessFrame)
            {
                if (it->second.moveDownCount > 0)
                    --(it->second.moveDownCount);
                it->second.tracks.erase(it->second.tracks.begin());
            }
            ++it;
        }

        //! 对当前帧的目标进行方差、方向、聚集度计算判断
        for (auto it = objects.begin(); it != objects.end(); )
        {
            auto trackId = it->id;
            if (trackedObjects.find(it->id) != trackedObjects.end())
            {
                trackedObjects[trackId].tracks.emplace_back(*it);
                trackedObjects[trackId].life++;
            }
            else
            {
                trackedObjects[trackId] = {1,  {*it}};
            }

            if (((int)trackedObjects[trackId].tracks.size() < minTrackedCount)     //!< 跟踪数量不够
                || !checkDirection(trackId))                                       //!< 运动方向向下
            {
                it = objects.erase(it);
                continue;
            }

            auto variance = ObjectStatistic().calculateVariance(trackedObjects[trackId].tracks);
            if (opt::getOptionEnabled(opt::FIRE_SMOKE_OPT))
                IVA_LOG_INFO("[fireSmoke] variance.x:{} variance.y:{} objectSize: {}", variance.x, variance.y, currentFrameObjectSize);

            //! 多个目标时的过滤判断
            if ((currentFrameObjectSize > 1) && ((variance.x < MULTI_OBJECT_VARIANCE_COEFF * maxVariance) || (variance.y < MULTI_OBJECT_VARIANCE_COEFF * maxVariance)))
            {
                ObjectStatistic statistic;
                statistic.calcMeanValue(objects);
                if (statistic.meanDistance < 3.0f * statistic.meanBoxDiagonal) //!< 框之间的平均距离小于 3倍框对角线长度，则认为框有聚集性，符合烟火
                {
                    historyObjects = objects;
                    return;
                }
            }

            if ( (variance.x > minVariance || variance.y > minVariance) && ((variance.x < maxVariance) && (variance.y < maxVariance)))
                ++it;
            else
                it = objects.erase(it);

        }
        historyObjects = objects;
    }

    /**
     * 烟火方向过滤
     * @param trackId 跟踪Id
     * @return ture 向上运行 false 向下运动
     */
    bool FiresmokeDetector::checkDirection(int trackId)
    {
        if (!enableCheckDirection)
           return true;

        auto moveDistance = trackedObjects[trackId].tracks.back().y - trackedObjects[trackId].tracks.front().y;

        if (moveDistance > 0) //!< 反向移动
        {
            ++trackedObjects[trackId].moveDownCount;
            return false;
        }
        //!< 如果此次是正向运动，则检查历史反向运动所占比例
        return (trackedObjects[trackId].moveDownCount < ((float)trackedObjects[trackId].tracks.size() - (float)minTrackedCount) * MIN_MOVE_DOWN_RATIO);
    }

    /**
     * 烟火后处理检测
     * @param frameData 当前检测帧，包含当前帧的检出框
     */
    ImageObjectList FiresmokeDetector::detect(const FrameData &frameData)
    {
        if (frameData.inputObjects.find(DetectorType::Firesmoke) == frameData.inputObjects.end())
            return {};

        auto objects = frameData.inputObjects.at(DetectorType::Firesmoke);
        //! 如果不使用后处理，则直接返回推理检测框
        if (!enablePostprocess)
            return objects;
#if 1
        //! 过滤跳帧或非法置信度的box
        objects.erase(std::remove_if(objects.begin(), objects.end(),[&](ImageObject& obj){
            return obj.confidence < 0;}), objects.end());
        if (objects.empty())
            objects = historyObjects;
        else
            filterObject(objects);

        return objects;
#else
        //! 过滤跳帧或非法置信度的box
        objects.erase(std::remove_if(objects.begin(), objects.end(),[&](ImageObject& obj){
            return obj.confidence < 0;}), objects.end());

        if (objects.empty())
            return historyObjects;

        if (++frameIndex >= (std::numeric_limits<decltype(frameIndex)>::max() - 1))
            frameIndex = 0;
        for (const auto& obj : objects)
            frameDetections.emplace_back(frameIndex, obj);

        if (opt::getOptionEnabled(opt::FIRE_SMOKE_OPT))
            IVA_LOG_DEBUG("[fireSmoke] frameDetections.size():{} channel:{}", frameDetections.size(), frameData.channelID);

        //! 计算相邻框左上角坐标欧式距离
        ImageObjectList outputObjects;
        if ((int)frameDetections.size() >= frameAtLeast)
        {
            double distance = 0;
            for (auto it = frameDetections.begin(); std::next(it) != frameDetections.end(); ++it)
                distance += std::hypot((std::next(it)->second.x - it->second.x), (std::next(it)->second.y - it->second.y));

            double averageDistance = distance / ((double)frameDetections.size() - 1.f);

            if (opt::getOptionEnabled(opt::FIRE_SMOKE_OPT))
                IVA_LOG_DEBUG("[fireSmoke] averageDistance:{} channel:{}", averageDistance, frameData.channelID);

            if (averageDistance > distanceAtLeast)
                outputObjects = objects;
        }

        //! 如果帧数计数器自增到最大帧数，或者框的数量达到阈值，则全部清空
        if ((frameIndex == 0) || (frameDetections.size() > MAX_OBJECT_CACHE_SIZE))
            frameDetections.clear();

        //! 只保留preprocessFrame帧的检测结果
        if (!frameDetections.empty() && (frameIndex > preprocessFrame))
        {
            int frameRemoveIndex = frameIndex - preprocessFrame;
            frameDetections.erase(std::remove_if(frameDetections.begin(), frameDetections.end(),[&](std::pair<int,ImageObject>& a){
               return a.first < frameRemoveIndex;}), frameDetections.end());
        }

        historyObjects = outputObjects;
        return outputObjects;
#endif
    }
}

