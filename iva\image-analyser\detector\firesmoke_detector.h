/**
 * Project IVA (image analyser)
 */
#pragma once
#include "base_detector.h"


namespace ia
{

    class FiresmokeDetector : public ObjectDetector
    {
    public:
        FiresmokeDetector();

        /**
         * 重置后处理检测
         */
        void reset() override;

        /**
         * 烟火后处理检测
         * @param frameData 当前检测帧，包含当前帧的检出框
         */
        ImageObjectList detect(const FrameData &frameData) override;

    private:
        /**
         * 设置初始化参数
         */
        void setParams();

        /**
         * 烟火过滤
         * @param objects 一帧中检出的目标框集合
         */
        void filterObject(ImageObjectList& objects);

        /**
         * 烟火方向过滤
         * @param trackId 跟踪Id
         * @return ture 向上运行 false 向下运动
         */
        bool checkDirection(int trackId);

    private:
        int preprocessFrame = 0;                                  //!< 预存帧数
        int frameAtLeast = 0;                                     //!< 检出框个数阈值
        int distanceAtLeast = 0;                                  //!< 检出相邻框欧式距离平均阈值
        bool enablePostprocess = true;                            //!< 是否打开后处理，默认为true
        float minVariance = 0.15;                                 //!< 框变化的最小方差值
        float maxVariance = 5.0;                                  //!< 框变化的最大方差值
        int minTrackedCount = 4;                                  //!< 框最小跟踪数量
        bool enableCheckDirection = false;                        //!< 是否检测目标框移动方向

        struct ObjectTrack
        {
            int life = 0;                                         //!< 目标生命值
            ImageObjectList tracks;                               //!< 目标跟踪的历史框
            int moveDownCount = 0;                                //!< 目标向下运动的累计次数
        };
        std::unordered_map<int, ObjectTrack> trackedObjects;

        std::vector<std::pair<int, ImageObject>> frameDetections; //!< 检出框缓存队列 key:frameId

    };
}