/**
 * Project IVA (image analyser)
 */
#include <algorithm>
#include <chrono>
#include "roadblock_detector.h"
#include "module_option.h"
#include "log.h"
#include "config/roadblock_config.hpp"

namespace ia
{
    using namespace std;
    using namespace roadblock;

	RoadblockDetector::RoadblockDetector()
	{
		setParams();
	}

	void RoadblockDetector::reset()
	{
		BaseDetector::reset();
        trackedObjects.clear();
		objectsCache.clear();
        isTrackedObjectEnough = false;
		frameNum = 0;
        polyMaskInited = false;
	}

    /**
     * @brief  设置路障跟踪模块、路障事件参数
     */
    void RoadblockDetector::setParams()
    {
        trackerParam.enableTracker = ROADBLOCK_CFG->enableTracker();
        trackerParam.framesNum = ROADBLOCK_CFG->framesNum();
        trackerParam.scoreThres1 = ROADBLOCK_CFG->scoreThres1();
        trackerParam.scoreThres2 = ROADBLOCK_CFG->scoreThres2();
        trackerParam.countThres1 = ROADBLOCK_CFG->countThres1();
        trackerParam.countThres2 = ROADBLOCK_CFG->countThres2();
		trackerParam.interval = ROADBLOCK_CFG->interval();
        trackerParam.iouThreshold = ROADBLOCK_CFG->iouThres();
        isFilterObject = ROADBLOCK_CFG->isFilterObject();
        ratioThreshold = ROADBLOCK_CFG->ratioThres();
        enablePostprocess = ROADBLOCK_CFG->enablePostprocess();
	}

    /**
     * @brief  生成感兴趣掩码
     * @param[in] width,height 帧图像宽高
     */
	void RoadblockDetector::setInterestedMask(int width, int height)
    {
        auto& regionData = this->regionMask.get();
        if (!polyMaskInited && !regionData.empty())
        {
            polyMaskInited = true;

            interestedMasks.clear();
            for (auto& data : regionData)
            {
                //! cv::Point2d 转换成成 cv::Point
                std::vector<cv::Point> points;
                for (auto& p : data)
                    points.emplace_back(p.x * (float)width, p.y * (float)height);

                //! 生成掩码 检测区为黑色、非检测区为白色
                cv::Mat scaledMask(height, width, CV_8UC1);
                scaledMask *= 0;
                cv::fillConvexPoly(scaledMask, points, 255);
                interestedMasks.emplace_back(std::move(scaledMask));
            }
        }
    }

    /**
     * 过滤检测框
     * @param[in] indices 跟踪得到的index
     * @param[in] objects 推理检测出的目标
     */
    ImageObjectList RoadblockDetector::filterRects(vector<bool>& indices, ImageObjectList& objects)
	{
        ImageObjectList results;
        for (const auto& interestedMask : interestedMasks)
        {
            int trackedNum = 0;
            ImageObjectList regionInterestedRects;
            for (size_t i = 0; i < objects.size(); ++i)
            {
                /// 将 object 转换为矩形
                cv::Rect2d rect(objects[i].x, objects[i].y, objects[i].width, objects[i].height);

                // 掩码图区域
                cv::Rect2d maskRect(0,0, interestedMask.size().width, interestedMask.size().height);

                // 求交集 得到有效区域 避免越界
                cv::Rect2d validRect = rect & maskRect;

                int noZeroCount = cv::countNonZero(interestedMask(validRect));
                auto ratio = noZeroCount * 1.0 / (rect.width * rect.height);

                //! 符合阈值范围的框保存
                if (ratio > ratioThreshold)
                {
                    regionInterestedRects.emplace_back(objects[i]);
                    if (indices[i])
                        trackedNum++;
                }
            }

            if (trackedNum > 0)   //!< 至少一个框被跟踪上，则符合阈值的框都输出
            {
                for (auto rect : regionInterestedRects) //!< object可能出现在多个子区域中，过滤重复的object
                {
                    if (results.end() == std::find_if(results.begin(), results.end(), [&](ImageObject& obj){ return obj.id == rect.id;}))
                        results.emplace_back(rect);
                }
            }
        }
        return results;
    }

    /**
     * iou跟踪过滤
     */
    std::vector<bool> RoadblockDetector::iouTrackerFilter(ImageObjectList &objects)
    {
        vector<bool> indices;
        if ((int)trackedObjects.size() < (trackerParam.framesNum))
        {
            trackedObjects.emplace_back(objects);
        }

        if ((int) trackedObjects.size() >= trackerParam.framesNum)
        {
            for (const auto &currFrameObject: objects)
            {
                long count1 = 0, count2 = 0;
                for (const auto& eachFrameObjects: trackedObjects)
                {
                    float maxIou = 0.0;
                    ImageObject matchedObject{};
                    for (auto object : eachFrameObjects) ///< 从每一帧中找到iou最大的目标
                    {
                        auto iou = calIOU(currFrameObject, object);
                        if (iou > trackerParam.iouThreshold && iou > maxIou)
                        {
                            maxIou = iou;
                            matchedObject = object;
                        }
                    }
                    if (matchedObject.confidence > trackerParam.scoreThres1)
                        count1++;
                    if (matchedObject.confidence > trackerParam.scoreThres2)
                        count2++;
                }
                auto tracked = ((count1 > trackerParam.countThres1) && (count2 > trackerParam.countThres2));
                indices.emplace_back(tracked);
            }
            trackedObjects.erase(trackedObjects.begin());
        }
        else
        {
            indices = vector<bool>(objects.size(), false);
        }


        return indices;
    }
    /**
     * 跟踪过滤 同一个目标在指定的缓存帧数量里，分别满足scoreThres1和scoreThres2的个数
     * @param[in] objects 推理检测出的目标
     */
    std::vector<bool> RoadblockDetector::trackerFilter(ImageObjectList &objects)
    {
        vector<bool> indices;
		if (frameNum < (trackerParam.framesNum-1) * trackerParam.interval && !isTrackedObjectEnough){
			if (frameNum % trackerParam.interval == 0)
				trackedObjects.emplace_back(objects);
		}else {
            isTrackedObjectEnough = true;
			trackedObjects.emplace_back(objects);
		}
        if ((int) trackedObjects.size() > trackerParam.framesNum)
        {
            for (const auto& currFrameObject : objects)
            {
                long count1 = 0, count2 = 0;
                for (auto eachFrameObjects : trackedObjects)
                {
                    count1 += std::count_if(eachFrameObjects.begin(), eachFrameObjects.end(),[&](ImageObject& obj){
                        return (currFrameObject.id == obj.id && obj.confidence > trackerParam.scoreThres1);});
                    count2 += std::count_if(eachFrameObjects.begin(), eachFrameObjects.end(),[&](ImageObject& obj){
                        return (currFrameObject.id == obj.id && obj.confidence > trackerParam.scoreThres2);});
                }
                auto tracked = ((count1 > trackerParam.countThres1) && (count2 > trackerParam.countThres2));
                indices.emplace_back(tracked);
            }
			if (frameNum % trackerParam.interval > 0) {
				trackedObjects.pop_back();
			}
			else {
					trackedObjects.erase(trackedObjects.begin());
			}
        }
        else
        {
            indices = vector<bool>(objects.size(), false);
        }
        return indices;
    }

    /**
     * 路障检测函数
     * @param frameData 当前检测帧，包含当前帧的相关信息
     */
	ImageObjectList RoadblockDetector::detect(const FrameData& frameData)
	{
        ImageObjectList outputObjects{};
        if (frameData.inputObjects.find(DetectorType::Roadblock) == frameData.inputObjects.end())
        {
            if (!objectsCache.empty())
            {
                outputObjects = objectsCache.front();
                if (cacheCount-- < 0)
                    objectsCache.erase(objectsCache.begin());
            }

            return outputObjects;
        }

		if (++frameNum >= 100000)
			frameNum = 0;

		auto objects = frameData.inputObjects.at(DetectorType::Roadblock);

        //! 如果不使用后处理，则直接返回推理检测框
        if (!enablePostprocess)
            return objects;

        //! 使用后处理逻辑筛选推理检测框
        auto startTime = std::chrono::steady_clock::now();

        //! 1、过滤跳帧或非法置信度的box
        objects.erase(std::remove_if(objects.begin(), objects.end(),[&](ImageObject& obj){
            return obj.confidence < 0;}), objects.end());

        if (objects.empty())
            objects = historyObjects;
        else
            historyObjects = objects;

        //! 2、产生感兴趣区域的mask
        setInterestedMask(frameData.width, frameData.height);

        //! 3、跟踪检出的路障框
        vector<bool> indices(objects.size(), true);
        if (trackerParam.enableTracker)
            indices = iouTrackerFilter(objects);

        //! 4、筛选过滤目标框
        if (isFilterObject) //!< 采用过滤规则过滤目标框
        {
            outputObjects = filterRects(indices, objects);
        }
        else                //!< 不过滤，直接返回跟踪上的目标框
        {
            for (size_t i = 0; i < indices.size(); i++)
            {
                if (indices[i])
                    outputObjects.push_back(objects[i]);
            }
        }

        printAverageTime(startTime);

        if (!outputObjects.empty())
        {
            objectsCache.emplace_back(outputObjects);
            if ((int)objectsCache.size() > trackerParam.interval)
                objectsCache.erase(objectsCache.begin());

            cacheCount = trackerParam.interval;
        }
        
        return outputObjects;

	}

    /**
     * 打印计算平均用时
     * @param startTime 起始时间
     */
    void RoadblockDetector::printAverageTime(const chrono::steady_clock::time_point &startTime)
    {
        if (saveAverageTimeFlag > 0 && opt::getOptionEnabled(opt::ROAD_BLOCK_OPT))
        {
            averageTime += chrono::round<chrono::milliseconds>(chrono::steady_clock::now() - startTime);
            if (frameNum % 99 == 0)
            {
                IVA_LOG_WARN("roadblock average time for one frame is {} ms", (averageTime / 100).count());
                averageTime = 0ms;
                saveAverageTimeFlag--;
            }
        }
    }
}
