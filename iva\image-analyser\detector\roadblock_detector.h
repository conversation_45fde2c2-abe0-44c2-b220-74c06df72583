/**
 * Project IVA (image analyser)
 */
#pragma once
#include <atomic>
#include <vector>
#include "base_detector.h"
#include "detector/base_detector.h"
#include "util/algorithm_util.h"

namespace ia
{
    struct TrackerParam
    {
        bool  enableTracker;    //!< 是否使能跟踪
        int   framesNum;        //!< 跟踪缓存的帧数
        float scoreThres1;      //!< 目标框阈值1，需要满足countThres1个当前阈值的目标
        float scoreThres2;      //!< 目标框阈值2，需要满足countThres2个当前阈值的目标
        int   countThres1;      //!< 对应scoreThres1阈值的个数阈值
        int   countThres2;      //!< 对应scoreThres2阈值的个数阈值
		int   interval;         //!< 跟踪帧的交替频率，用于模拟抽帧
        float iouThreshold;     //!< 跟踪iou阈值
    };

    /**
     * 路障检测器
     */
    class RoadblockDetector : public ObjectDetector
    {
    public:
        RoadblockDetector();
        /**
         * 重置通道
         */
        void reset() override;

        /**
        * 路障检测函数
        * @param frameData 当前检测帧，包含当前帧的相关信息，比如：宽高
        */
        ImageObjectList detect(const FrameData& frameData) override;

    private:
        /**
         * 获取初始化参数
         */
        void setParams();

        /**
         * @brief  生成感兴趣掩码
         * @param[in] width,height 帧图像宽高
         */
        void setInterestedMask(int width, int height);

        /**
         * 过滤检测框
         * @param[in] indices 跟踪得到的index
         * @param[in] objects 推理检测出的目标
         */
        ImageObjectList filterRects(std::vector<bool>& indices, ImageObjectList& objects);

        /**
         * 跟踪过滤 同一个目标在指定的缓存帧数量里，分别满足scoreThres1和scoreThres2的个数
         * @param[in] objects 推理检测出的目标
         */
        std::vector<bool> trackerFilter(ImageObjectList &objects);

        std::vector<bool> iouTrackerFilter(ImageObjectList &objects);

        /**
         * 打印计算平均用时
         * @param startTime 起始时间
         */
        void printAverageTime(const std::chrono::steady_clock::time_point &startTime);

    private:
        //! 后处理过滤
        bool   enablePostprocess = true;                          //!< 是否打开后处理
        double ratioThreshold = 0.8;                              //!< 目标框与检测区重叠阈值
        bool   isFilterObject = true;                             //!< 是否过滤目标框
        std::vector<cv::Mat> interestedMasks;                     //!< 子区域点掩码
        std::vector<ImageObjectList> objectsCache;                //!< 缓存N帧目标框
        int cacheCount = 0;

        //! tracker
        TrackerParam trackerParam;                                //!< 跟踪模块相关参数初始化
        std::vector<ImageObjectList> trackedObjects;              //!< 记录N帧跟踪的目标框
		bool isTrackedObjectEnough = false;                       //!< 记录初次trackerParam.framesNum帧图片是否就位

        //! debug
        int frameNum = 0;                                         //!< 当前帧数，每次调用自增一
        int saveAverageTimeFlag = 10;                             //!< 打印后处理时间频率
        std::chrono::milliseconds averageTime = 0ms;              //!< 后处理的平均时间

    };
}
