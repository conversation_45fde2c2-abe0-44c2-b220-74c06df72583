#include "roi_object_class_model.h"

namespace roi_object_detect{

    RoiObjectClassModel::RoiObjectClassModel(ClassModelParam& classModelParam)
    {
        m_classModelParam = classModelParam;
    }

    RoiObjectClassModel::~RoiObjectClassModel(){

    }

    int RoiObjectClassModel::classify(ROIObjectParam* roiObjectParam, int deviceID){
        ClassModel* classModel = RoiObjectClassModelPrivate::getInstance()->getModel(m_classModelParam.modelPath,deviceID);
        int m_inputSizeW,m_inputSizeH;
        classModel->getInputSize(m_inputSizeW,m_inputSizeH);
        roiObjectParam->classifyRects.clear();
        for(int i=0;i<roiObjectParam->alarmRects.size();i++){
            ObjBox box;
            float saclePara = m_classModelParam.scaleForSeg;
            box.x = max(1,int(roiObjectParam->alarmRects[i].x-roiObjectParam->alarmRects[i].width*(saclePara-1.0)/2));
            box.y = max(1,int(roiObjectParam->alarmRects[i].y-roiObjectParam->alarmRects[i].height*(saclePara-1.0)/2));
            box.width = min(int(roiObjectParam->alarmRects[i].width*saclePara),roiObjectParam->orginImg.size().width-1-box.x);
            box.height = min(int(roiObjectParam->alarmRects[i].height*saclePara),roiObjectParam->orginImg.size().height-1-box.y);
            cv::Rect rect=cv::Rect(int(box.x),int(box.y),int(box.width),int(box.height));
            cv::Mat imgSeg = roiObjectParam->orginImg(rect);
            int h = imgSeg.size().height;
            int w = imgSeg.size().width;
            int s;
            if(w>h){
                s = int(w*m_classModelParam.scaleForPadding);
            }else{
                s = int(h*m_classModelParam.scaleForPadding);
            }
            if(m_classModelParam.segMode){
                s = max(m_inputSizeH,s);
            }
            cv::Mat imgPadding = cv::Mat::zeros(s,s,CV_8UC3)+m_classModelParam.paddingValue;
            imgPadding.setTo(m_classModelParam.paddingValue);
            int w_Start = int((s-w)/2);
            int h_Start = int((s-h)/2);
            cv::Rect roi = cv::Rect(w_Start,h_Start,w,h);
            imgSeg.copyTo(imgPadding(roi));
            roiObjectParam->segClassImg = imgPadding;
            std::vector<float> scores = classModel->infer(imgPadding, false).front();
            float maxScore = -0.5;
            int maxIndex = -1;
            for(int j=0;j<scores.size();j++){
                if(maxScore<scores[j]){
                    maxScore = scores[j];
                    maxIndex = j;
                }
            }
            if(maxIndex == 0){
                roiObjectParam->classifyRects.push_back(roiObjectParam->alarmRects[i]);
            }else{
                float scoreDiv = scores[0] - maxScore;
                roiObjectParam->scoreDiv = scoreDiv;
                if(scoreDiv > m_classModelParam.scoresGip){
                    roiObjectParam->classifyRects.push_back(roiObjectParam->alarmRects[i]);
                }
            }
        }
        return 0;
    }

}
