#pragma once

#include "model_warehouse/class_model.h"
#include "roi_object_parms.h"
#include "util/algorithm_util.h"

#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

using namespace model_warehouse;
using namespace algorithm_util;

namespace roi_object_detect{

	class RoiObjectClassModelPrivate
	{
	public:
		static RoiObjectClassModelPrivate* getInstance()
		{
			static RoiObjectClassModelPrivate instance;
			return &instance;
		}

        ClassModel* getModel(std::string& modelPath, int deviceID)
		{
			if (!m_classModel)
			{
                m_classModel = new ClassModel(modelPath, deviceID); //ClassModel : initResource of class model. now is using res18
                m_classModel->initResource();
			}
			return m_classModel;
		}
	private:
        ClassModel* m_classModel;

	};

    class RoiObjectClassModel{
    public:
        RoiObjectClassModel(ClassModelParam& classModelParam);
        ~RoiObjectClassModel();

		// classify input images , judge whether it is throwing img or not 
        int classify(ROIObjectParam* roiObjectParam, int deviceID);

    private:
	    //parameters for class model
        ClassModelParam m_classModelParam;
    };
}
