#include "roi_object_detect_model.h"

namespace roi_object_detect{

    RoiObjectDetectModel::RoiObjectDetectModel(DetectModelParam& detectModelParam)
    {
        m_detectModelParam = detectModelParam;
    }

    RoiObjectDetectModel::~RoiObjectDetectModel(){

    }

    int RoiObjectDetectModel::detect(ROIObjectParam* roiObjectParam, int deviceID, int& ret){
        DetectModel* detectModel = RoiObjectDetectModelPrivate::getInstance()->getModel(m_detectModelParam.modelPath,deviceID, ret);
        int m_inputSizeW,m_inputSizeH;
        detectModel->getInputSize(m_inputSizeW,m_inputSizeH);
        roiObjectParam->detectRects.clear();
        for(int i=0;i<roiObjectParam->classifyRects.size();i++){
            ObjBox box;
            float saclePara = m_detectModelParam.scaleForSeg;
            if(m_detectModelParam.segMode){
                float minScale = m_inputSizeH/max(roiObjectParam->classifyRects[i].width,roiObjectParam->classifyRects[i].height);
                saclePara = max(saclePara,minScale);
            }
            box.x = max(1,int(roiObjectParam->classifyRects[i].x-roiObjectParam->classifyRects[i].width*(saclePara-1.0)/2));
            box.y = max(1,int(roiObjectParam->classifyRects[i].y-roiObjectParam->classifyRects[i].height*(saclePara-1.0)/2));
            box.width = min(int(roiObjectParam->classifyRects[i].width*saclePara),roiObjectParam->orginImg.size().width-1-box.x);
            box.height = min(int(roiObjectParam->classifyRects[i].height*saclePara),roiObjectParam->orginImg.size().height-1-box.y);
            cv::Rect rect=cv::Rect(int(box.x),int(box.y),int(box.width),int(box.height));
            cv::Mat imgSeg = roiObjectParam->orginImg(rect);
			std::vector <std::vector<ObjBox>> outResults;
            roiObjectParam->segDetectImg = imgSeg;
            outResults = detectModel->infer({ imgSeg }, true);
			if (outResults.empty())
				return 0;

			std::vector<ObjBox>& outBoxes = outResults[0];
            if(outBoxes.size()>0){
                for(int j=0;j<outBoxes.size();j++){
                    ObjBox boxRes;
                    boxRes.x = outBoxes[j].x+box.x;
                    boxRes.y = outBoxes[j].y+box.y;
                    boxRes.width = outBoxes[j].width;
                    boxRes.height = outBoxes[j].height;
                    boxRes.score =  outBoxes[j].score;
                    boxRes.class_ =  outBoxes[j].class_;
                    float iou = rectIouDivOne(roiObjectParam->classifyRects[i],boxRes);
                    if(iou>m_detectModelParam.rectIouThres){
                        roiObjectParam->detectRects.push_back(boxRes);
                    }
                }

                RectSelector rSelector;
                rSelector.init(roiObjectParam->detectRects);
                rSelector.rmInside();
                rSelector.rmBig(roiObjectParam->selectRectParam->maxW,roiObjectParam->selectRectParam->maxH);
                rSelector.rmBigRatio(roiObjectParam->selectRectParam->maxW,roiObjectParam->selectRectParam->maxH,roiObjectParam->imgH, roiObjectParam->selectRectParam->farRatio);
                if(roiObjectParam->selectRectParam->isOpening){
                    if(!roiObjectParam->selectRectParam->interestedMask.empty()){
                        rSelector.rmBigWithRoadMask(roiObjectParam->selectRectParam->interestedMask,roiObjectParam->selectRectParam->carNumForRoads,roiObjectParam->selectRectParam->minW);
                    }
                }
                roiObjectParam->detectRects = rSelector.getRect();
            }
        }
        return 0;
    }

}
