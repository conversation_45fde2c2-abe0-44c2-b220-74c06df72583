#include "roi_object_distance_solver.h"

namespace roi_object_detect{

    DistanceSolver::DistanceSolver(DistanceSolverParam& distanceSolverParam){
        m_framesNum = distanceSolverParam.maxDistanceNum;
        m_validNum = distanceSolverParam.validDistanceNum;
        m_moveNumStart = distanceSolverParam.moveDistanceNum;
        m_updataDisThres = distanceSolverParam.updataDisThres;
        reset();
    }

    DistanceSolver::~DistanceSolver(){

    }

    void DistanceSolver::reset(){
        m_saveFeatures.clear();
        m_currFeature.clear();
        m_saveDis.clear();
    }

    void DistanceSolver::appendFeature(std::vector<std::vector<float>> feature){
        m_currFeature = feature;
        calAndSaveDis();
        m_saveFeatures.push_back(feature);
        if(m_saveFeatures.size() > m_framesNum){
            m_saveFeatures.erase(m_saveFeatures.begin());
        }
    }

    void DistanceSolver::calAndSaveDis(){
        m_saveDis.clear();
        for(int i=0;i<m_saveFeatures.size();i++){
            m_saveDis.push_back( calVectorDistances(m_currFeature,m_saveFeatures[i]) );
        }
    }

    std::vector<std::vector<double>> DistanceSolver::getDis(){
            return m_saveDis;
    }

    void DistanceSolver::updateDis(ROIObjectParam* roiObjectParam){
        for(int i=0;i<roiObjectParam->distances.size();i++){
            for(int j=0;j<roiObjectParam->distances[i].size();j++){
                if(roiObjectParam->bgFillFlags[j]){
                    if(roiObjectParam->distances[i][j]<m_updataDisThres || roiObjectParam->bgFrontDis[j]<m_updataDisThres){
                         roiObjectParam->distances[i][j] = min(roiObjectParam->distances[i][j],roiObjectParam->bgFrontDis[j]);
                    }
                }
            }
        }
    }

    void DistanceSolver::putDis(std::vector<std::vector<double>>& dis){
            m_saveDis = dis;
    }

    int DistanceSolver::getSingleDistance(int front_num , std::vector<double>* dis){
        int index = front_num - 1;
        if(front_num<1 || m_saveDis.size()<1 || index<0 || index>(m_saveDis.size()-1)){
            return -1;
        }
        *dis = m_saveDis[index];
        return 0;
    }

    int DistanceSolver::getMultiDistance(std::vector<double>* dis){
        if(m_saveDis.size()<m_framesNum){
            return -1;
        }
        dis->clear();
        for(int i=0;i<m_saveDis[0].size();i++){
            std::vector<double> tmpDisVector;
            tmpDisVector.clear();
            for(int j=0;j<m_saveDis.size();j++){
                tmpDisVector.push_back(m_saveDis[j][i]);
            }
            sort(tmpDisVector);
            double res = 0.0;
            if(tmpDisVector.size()>=(m_moveNumStart+m_validNum)){
                for(int i=m_moveNumStart;i<m_moveNumStart+m_validNum;i++){
                    res += tmpDisVector[i];
                }
            }
            res = res/m_validNum;
            dis->push_back(res);
        }
        return 0;
    }

    int DistanceSolver::solveDis(ROIObjectParam* roiObjectParam){
        if(roiObjectParam->bgFillFlags.size() && roiObjectParam->distances.size() > 0){
            // clear up the tail of every single distance, by using of bgFront distance
            updateDis(roiObjectParam);
        }
        // put the many distances
        putDis(roiObjectParam->distances);
        // get single distance between currImg and first forward img (singleDis1)
        getSingleDistance(1,&(roiObjectParam->singleDis1));
        // get single distance between currImg and second forward img (singleDis2)
        getSingleDistance(2,&(roiObjectParam->singleDis2));
        // get single distance between currImg and fifth forward img (singleDis2)
        getSingleDistance(5,&(roiObjectParam->singleDis5));
        getSingleDistance(4,&(roiObjectParam->singleDis4));
        // get single distance between currImg and many previous imgs (multiDis)
        getMultiDistance(&(roiObjectParam->multiDis));
        return 0;
    }

}
