#include "roi_object_rect_generator.h"

namespace roi_object_detect{

    RectGenerator::RectGenerator(SelectRectParam& selectRectParam){
        m_selectRectParam = selectRectParam;
        for(int i=0; i<m_selectRectParam.roadNums;i++){
            m_carRoadNumsList.push_back(selectRectParam.carNumForRoads[i]);
        }
        m_isFirstGenImg = true;
        reset();
    }
    void RectGenerator::updateParams(SelectRectParam& selectRectParam){
        m_selectRectParam.interestedMask = selectRectParam.interestedMask;
    }

    void RectGenerator::reset(){
        m_currRects.clear();
        m_rectAlarmFlags.clear();
    }

    void RectGenerator::catBinary(ROIObjectParam* roiObjectParam){
        int wi = 2;
        int hi = 2;
        int gapDis = 10;
        cv::Mat binaryImg = cv::Mat::zeros(cv::Size(roiObjectParam->imgFeatureInputW*wi+gapDis*(wi-1),roiObjectParam->imgFeatureInputH*hi+gapDis*(hi-1)),CV_8UC1);
        cv::Rect rect;
        int w = roiObjectParam->imgFeatureInputW+gapDis;
        int h = roiObjectParam->imgFeatureInputH+gapDis;
        std::vector<cv::Mat> imgs;
        imgs.push_back(m_binarySingle1*255);
        imgs.push_back(m_binarySingle5*255);
        imgs.push_back(m_binaryBgDis*255);
        imgs.push_back(m_binary*255);
        cv::Mat gray;
        if(!roiObjectParam->resizeImg.empty()){
            cv::cvtColor(roiObjectParam->resizeImg, gray, cv::COLOR_BGR2GRAY);
        }
        for(int i=0;i<imgs.size();i++){
            cv::Mat imgTmp = imgs[i];
            int c = i%wi;
            int k = int(i/wi);
            if(!imgTmp.empty() && !gray.empty()){
                add(imgTmp*0.25,gray*0.75,imgTmp);
                if(i==3){
                    imgTmp = drawRectangle(imgTmp,m_currRects,cv::Scalar(255,255,255));
                }
                rect =cv::Rect(int(w*c),int(h*k),int(roiObjectParam->imgFeatureInputW),int(roiObjectParam->imgFeatureInputH));
                imgTmp.copyTo(binaryImg(rect));
            }
        }
        roiObjectParam->binaryImg = binaryImg;
    }

    void RectGenerator::genImgfromDistanceScore(ROIObjectParam* roiObjectParam){
        if(m_isFirstGenImg){
            m_imgSingle1 = cv::Mat::zeros(cv::Size(roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH),CV_8UC1);
            m_imgSingle2 = m_imgSingle1.clone();
            m_imgSingle4 = m_imgSingle1.clone();
            m_imgSingle5 = m_imgSingle1.clone();
            m_imgMulti = m_imgSingle1.clone();
            m_imgBgDis = m_imgSingle1.clone();
            m_isFirstGenImg = false;
        }
        genImgfromScore(m_imgSingle1,roiObjectParam->singleDis1,roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH,roiObjectParam->downScale);
        genImgfromScore(m_imgSingle2,roiObjectParam->singleDis2,roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH,roiObjectParam->downScale);
        genImgfromScore(m_imgSingle4,roiObjectParam->singleDis4,roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH,roiObjectParam->downScale);
        genImgfromScore(m_imgSingle5,roiObjectParam->singleDis5,roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH,roiObjectParam->downScale);
        genImgfromScore(m_imgMulti,roiObjectParam->multiDis,roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH,roiObjectParam->downScale);
        genImgfromScore(m_imgBgDis,roiObjectParam->bgFrontDis,roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH,roiObjectParam->downScale);
    }

    void RectGenerator::calBoxesGrids(ROIObjectParam* roiObjectParam){
        for(int i=0 ;i<roiObjectParam->singleDis1.size();i++){
            roiObjectParam->singleDis1[i] = roiObjectParam->singleDis1[i]>m_selectRectParam.scoreThresForBinary;
            roiObjectParam->singleDis2[i] = roiObjectParam->singleDis2[i]>m_selectRectParam.scoreThresForBinary;
            roiObjectParam->singleDis4[i] = roiObjectParam->singleDis4[i]>m_selectRectParam.scoreThresForBinary;
            roiObjectParam->singleDis5[i] = roiObjectParam->singleDis5[i]>m_selectRectParam.scoreThresForBinary;
            roiObjectParam->multiDis[i] = roiObjectParam->multiDis[i]>m_selectRectParam.scoreThresForBinary;
            roiObjectParam->bgFrontDis[i] = roiObjectParam->bgFrontDis[i]>m_selectRectParam.scoreThresForBinary;

            roiObjectParam->bgFrontDis[i] = roiObjectParam->bgFrontDis[i]*(1-roiObjectParam->singleDis1[i])*(1-roiObjectParam->singleDis2[i]);
            roiObjectParam->singleDis5[i] = roiObjectParam->singleDis5[i]*roiObjectParam->singleDis4[i];
        }
    }

    void RectGenerator::calBoxes(ROIObjectParam* roiObjectParam){
        m_diffBinary = diffImgs(roiObjectParam->bgImg,roiObjectParam->resizeImg);
        int thresh=255*m_selectRectParam.scoreThresForBinary;
        int maxVal=255;
        cv::threshold(m_imgBgDis, m_binaryBgDis, thresh, maxVal, cv::THRESH_BINARY);
        cv::threshold(m_imgSingle1, m_binarySingle1, thresh, maxVal, cv::THRESH_BINARY);
        cv::threshold(m_imgSingle2, m_binarySingle2, thresh, maxVal, cv::THRESH_BINARY);
        cv::threshold(m_imgSingle5, m_binarySingle5, thresh, maxVal, cv::THRESH_BINARY);
        cv::threshold(m_imgSingle4, m_binarySingle4, thresh, maxVal, cv::THRESH_BINARY);
        cv::threshold(m_imgMulti, m_binaryMulti, thresh, maxVal, cv::THRESH_BINARY);
        m_binarySingle5 = m_binarySingle5/255;
        m_binarySingle4 = m_binarySingle4/255;
        cv::multiply(m_binarySingle4,m_binarySingle5,m_binarySingle5);
        //cv::multiply(m_binaryBgDis/255,m_diffBinary/255,m_binary);
        m_binary = m_binaryBgDis/255;
        cv::multiply(1-m_binarySingle1/255,m_binary,m_binary);
        cv::multiply(1-m_binarySingle2/255,m_binary,m_binary);
    }

    void RectGenerator::getRoiImgs(ROIObjectParam* roiObjectParam){
        m_currRois.clear();
        for(int i=0;i<m_currRects.size();i++){
            ObjBox box = m_currRects[i];
            cv::Rect rect=cv::Rect(int(box.x),int(box.y),int(box.width),int(box.height));
            cv::Mat roiBinary = m_binary(rect);
            cv::resize(roiBinary,roiBinary,cv::Size(m_currRects[i].width*roiObjectParam->ratioW, m_currRects[i].height*roiObjectParam->ratioH),(0.0),(0.0),cv::INTER_LINEAR);
            m_currRois.push_back(roiBinary);
        }
    }

#if 0
   void RectGenerator::calAlarmFlags(){
       m_rectAlarmFlags.clear();
       for(int i=0;i<m_currRects.size();i++){
           ObjBox box = m_currRects[i];
           cv::Rect rect=cv::Rect(int(box.x),int(box.y),int(box.width),int(box.height));
           cv::Mat roiSingle5 = m_binarySingle5(rect);
           cv::Mat roiBinary = m_binary(rect);
           cv::Mat innerImg,unionImg;
           cv::multiply(roiSingle5,roiBinary,innerImg);
           unionImg = roiSingle5 + roiBinary;
           int noZeroInnerCount = cv::countNonZero(innerImg);
           int noZeroUnionCount = cv::countNonZero(unionImg);
           float ratio = noZeroInnerCount*1.0/noZeroUnionCount;
           m_rectAlarmFlags.push_back(ratio>m_selectRectParam.iouThres);
       }
   }
#endif

    void RectGenerator::calAlarmFlags(){
        m_rectAlarmFlags.clear();
        for(int i=0;i<m_currRects.size();i++){
            ObjBox box = m_currRects[i];
            cv::Rect rect=cv::Rect(int(box.x),int(box.y),int(box.width),int(box.height));
            cv::Mat roiMulti = m_binarySingle5(rect);
            int noZeroCount = cv::countNonZero(roiMulti);
            float ratio = noZeroCount*1.0/(box.width*box.height);
            m_rectAlarmFlags.push_back(ratio>m_selectRectParam.iouThresForAlarm);
        }
    }

    void RectGenerator::selectRectGrid(ROIObjectParam* roiObjectParam){
        m_currRects.clear();
        m_indicesRes.clear();
        std::vector<ObjBox> rects;
        std::vector<std::vector<GridCoord>> indicesAll;
        genBoxesFromVector(roiObjectParam->bgFrontDis,roiObjectParam->imgFeatureInputW/roiObjectParam->downScale,roiObjectParam->downScale,&rects,&indicesAll);
        RectSelector rSelector;
        rSelector.init(rects);
        rSelector.rmInside();
        rSelector.rmOverlop(m_selectRectParam.iouThresForSelect);
        rSelector.rmLittle(m_selectRectParam.minW,m_selectRectParam.minH);
        rSelector.rmBig(m_selectRectParam.maxWForFirstFilter,m_selectRectParam.maxHForFirstFilter);
        if(m_selectRectParam.isOpening){
            if(!m_selectRectParam.interestedMask.empty()){
                rSelector.rmBigWithRoadMask(m_selectRectParam.interestedMask,m_carRoadNumsList,m_selectRectParam.carLeastWidth);
            }
        }
        rSelector.rmRatio();
        m_currRects = rSelector.getRect();
        int rectResSize = m_currRects.size();
        int tmpIndices[rectResSize];
        for(int i=0;i<rects.size();i++){
            for(int j=0;j<rectResSize;j++){
                if(m_currRects[j].x==rects[i].x && m_currRects[j].y==rects[i].y && m_currRects[j].width==rects[i].width){
                    tmpIndices[j] = i;
                }
            }
        }
        for(int i=0;i<rectResSize;i++){
            m_currRects[i].gridCoords = indicesAll[tmpIndices[i]];
            m_indicesRes.push_back(indicesAll[tmpIndices[i]]);
        }
    }

    void RectGenerator::calAlarmFlagsGrid(ROIObjectParam* roiObjectParam){
        m_rectAlarmFlags.clear();
        for(int i=0;i<m_indicesRes.size();i++){
            int noZeroInnerCount = 0;
            for(int j=0; j<m_indicesRes[i].size(); j++){
                if(roiObjectParam->singleDis5[m_indicesRes[i][j].i]>=0.65){
                    noZeroInnerCount++;
                }
            }
            int noZeroUnionCount = m_indicesRes[i].size();
            float ratio = noZeroInnerCount*1.0/noZeroUnionCount;
            m_rectAlarmFlags.push_back(ratio>m_selectRectParam.iouThresForAlarm);
        }
    }

    std::vector<ObjBox> RectGenerator::selectRects(){
        std::vector<ObjBox> rects = genBoxesFromBinary(m_binary);
        RectSelector rSelector;
        rSelector.init(rects);
        rSelector.rmInside();
        rSelector.rmOverlop(m_selectRectParam.iouThresForSelect);
        rSelector.rmLittle(m_selectRectParam.minW,m_selectRectParam.minH);
        rSelector.rmBig(m_selectRectParam.maxWForFirstFilter,m_selectRectParam.maxHForFirstFilter);
        if(m_selectRectParam.isOpening && !m_selectRectParam.interestedMask.empty())  {
            rSelector.rmBigWithRoadMask(m_selectRectParam.interestedMask,m_carRoadNumsList,m_selectRectParam.carLeastWidth);
        }
        rSelector.rmRatio();
        std::vector<ObjBox> rectRes = rSelector.getRect();
        return rectRes;
    }

    void RectGenerator::resizeRects(ROIObjectParam* roiObjectParam){
        for(int i=0;i<m_currRects.size();i++){
            m_currRects[i].x = int(m_currRects[i].x*roiObjectParam->ratioW);
            m_currRects[i].y = int(m_currRects[i].y*roiObjectParam->ratioH);
            m_currRects[i].width = int(m_currRects[i].width*roiObjectParam->ratioW);
            m_currRects[i].height = int(m_currRects[i].height*roiObjectParam->ratioH);
        }
    }

    void RectGenerator::getBoxes(ROIObjectParam* roiObjectParam,bool catBinaryFlag){
        if(roiObjectParam->multiDis.size()>0 && roiObjectParam->bgFrontDis.size()>0){
            //gen imgs from the distances
            genImgfromDistanceScore(roiObjectParam);
            //generate bounding boxes by singleMap1,singleMap2,bgMap,binaryMap
            calBoxes(roiObjectParam);
            // selecting by boxSize/boxIou/boxContains and so on
            m_currRects = selectRects();
            // calculate the alarm states of every rect
            calAlarmFlags();
            getRoiImgs(roiObjectParam);
            resizeRects(roiObjectParam);

            if(catBinaryFlag){
                catBinary(roiObjectParam);
            }

            roiObjectParam->singleFrameRects = m_currRects;
            roiObjectParam->singleFrameRois = m_currRois;
            roiObjectParam->singleFrameRectAlarmFlags = m_rectAlarmFlags;
        }
    }

    void RectGenerator::getBoxesGrid(ROIObjectParam* roiObjectParam,bool catBinaryFlag){
        if(roiObjectParam->multiDis.size()>0 && roiObjectParam->bgFrontDis.size()>0){
            //generate bounding boxes by singleMap1,singleMap2,bgMap,binaryMap
            calBoxesGrids(roiObjectParam);

            // selecting by boxSize/boxIou/boxContains and so on
            selectRectGrid(roiObjectParam);

            // calculate the alarm states of every rect
            calAlarmFlagsGrid(roiObjectParam);

            //getRoiImgs(roiObjectParam);
            if(catBinaryFlag){
                genImgfromDistanceScore(roiObjectParam);
                //generate bounding boxes by singleMap1,singleMap2,bgMap,binaryMap
                calBoxes(roiObjectParam);
                catBinary(roiObjectParam);
            }

            resizeRects(roiObjectParam);
            roiObjectParam->singleFrameRects = m_currRects;
           // roiObjectParam->singleFrameRois = m_currRois;
            roiObjectParam->singleFrameRectAlarmFlags = m_rectAlarmFlags;

        }
    }

    void RectGenerator::getBoxesForTowBg(ROIObjectParam* roiObjectParam){
            cv::Mat imgByTowBg = cv::Mat::zeros(cv::Size(roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH),CV_8UC1);
            genImgfromScore(imgByTowBg,roiObjectParam->towBgDis,roiObjectParam->imgFeatureInputW,roiObjectParam->imgFeatureInputH,roiObjectParam->downScale);
            cv::Mat binaryByTowBg;
            cv::threshold(imgByTowBg, binaryByTowBg, 101, 255, cv::THRESH_BINARY);
            roiObjectParam->towBgRects = genBoxesFromBinary(binaryByTowBg);
    }
}
