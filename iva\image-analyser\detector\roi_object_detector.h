/**
 * Project IVA (image analyser)
 */
#pragma once
#include "base_detector.h"

#include "roi_object_detect/roi_object_parms.h"
#include "roi_object_detect/roi_object_bg_generator.h"
#include "roi_object_detect/roi_object_class_model.h"
#include "roi_object_detect/roi_object_detect_model.h"
#include "roi_object_detect/roi_object_distance_solver.h"
#include "roi_object_detect/roi_object_feature_model.h"
#include "roi_object_detect/roi_object_rect_generator.h"
#include "roi_object_detect/roi_object_tracker.h"

namespace ia
{
	using namespace roi_object_detect;
	class ROIObjectDetector : public ObjectDetector
	{
	public:
		ROIObjectDetector(int interval, int deviceID);
        ~ROIObjectDetector();

		ImageObjectList detect(const FrameData& frameData) override;
        void reset()override;

	private:
		bool initModels();
		void setParams();

        void setFeatureParams();

        //update configs by setting.ini file
        void updateConfig();

        //generate interested mask from ployPoints
        bool setInterestedMaskByPoint(ROIObjectParam* roiObjectParam);

        //init var struct for current frame.
        void initRoiObjectParam(ROIObjectParam* roiObjectParam, cv::Mat img);

        //calculate the distances which between current frame and Previous frames
        void featureModelImagePreprocess(ROIObjectParam* roiObjectParam);
        void featureModelForward(ROIObjectParam* roiObjectParam);

        //gen bgImg, update bgImg
        void bgFeatureModelImagePreprocess(ROIObjectParam* roiObjectParam);
        void bgFeatureModelForward(ROIObjectParam* roiObjectParam);
        void updateBg(ROIObjectParam* roiObjectParam);

        //logical calculate for many frames's distances. 
        void solveDistance(ROIObjectParam* roiObjectParam);

        //calculate front rects for every frames 
        void getBoxes(ROIObjectParam* roiObjectParam);

        // Filter some rects out with the iou traceing
        void traceBoxes(ROIObjectParam* roiObjectParam);

        //  Filter some rects out with the iou InterestMask.
        void filterByInterestMask(ROIObjectParam* roiObjectParam);

        //  Filter some rects out with the iou detect model.
        void filterByClassify(ROIObjectParam* roiObjectParam);

        //  Filter some rects out with the iou class model.
        void filterByDetector(ROIObjectParam* roiObjectParam);

        // put the results of throwaway_Coords to vector throwaways
        void putThrowawaysResults(ROIObjectParam* roiObjectParam);

        //if opening saveFlag, draw and save the images in the processing.
        void drawAndSaveImgs(ROIObjectParam* roiObjectParam, std::string strFd);

	private:
        // params struct for every modules
        FeatureModelParam m_featureModelParam;
        DetectModelParam m_detectModelParam;
        ClassModelParam m_classModelParam;
        BgImgGeneratorParam m_bgImgGeneratorParam;
        DistanceSolverParam m_distanceSolverParam;
        SelectRectParam m_selectRectParam;
        RoiObjectTrackerParam m_roiObjectTrackerParam;
        ImgSavePathParam m_imgSavePathParam;

        //models
        RoiObjectFeatureModel* m_featureModel = nullptr; // feature extract model
        RoiObjectClassModel* m_classModel = nullptr; //classify model
        RoiObjectDetectModel* m_detectModel = nullptr; //detect model

        //Logical processing modules
        DistanceSolver* m_distanceSolver = nullptr; //logical calculate for many frames's distances. 
        RectGenerator* m_rectGenerator = nullptr; //calculate front rects for every frames.   calculate that the rect is be throwaway.
        StaticObjGenerator* m_staticObjGenerator = nullptr; // Filter some rects out with the iou traceing.
        BgImgGenerator* m_bgImgGenerator = nullptr; //generate bgImg, and update bgImg

        cv::Mat m_interestedMask; //interested mask for throwaway detecting.

        std::vector<cv::Rect2d> throwaways; //results

        //Assist in seting the name of saved imgs.
        int m_frameNum = 1; //record the curr frame index.  
        std::string m_channelID; //record the curr channel index. 

        //save average times
        long m_averageTime = 0.0; //average time of solving one frame
        bool m_saveAverageTimeFlag = true; // is save average time to log

        bool is_gen_feature_success = false;
	};
}
