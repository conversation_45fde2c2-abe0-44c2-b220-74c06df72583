/**
 * Project IVA (image analyser)
 */

#include "image_analyser.h"
#ifdef ENABLE_TRACK_FEATURE
#include "model_warehouse/feature_task.h"
#endif

 /**
  * ImageAnalyser implementation
  *
  * 图像分析模块
  */
namespace ia
{
	/**
	 * 当前所有通道
	 * map <通道ID， 通道对象>
	 */
	std::map<int, Channel*> channels;
	std::mutex channelMutex;
    int deviceId = 0;


	/**
	 * 获取通道
	 * @param channelID
	 * @param deviceId GPU ID
	 * @return Channel*
	 */
	Channel *getChannel(int channelID, int deviceID)
	{
        deviceId = deviceID;
		std::lock_guard<std::mutex> m(channelMutex);
		if (channels.find(channelID) == channels.end())
		{
			auto channel = new Channel(channelID, deviceID);
			channels[channelID] = channel;
		}
		return channels[channelID];
	}

	/**
	 * 释放通道
	 * @param channelID 通道ID  (-1 清除所有)
	 */
	void releaseChannel(int channelID)
	{
		std::lock_guard<std::mutex> m(channelMutex);
		if (channelID == -1)
		{
			for (auto& channel : channels)
			{
				delete channel.second;
			}
			channels.clear();
		}
		else
		{
			auto it = channels.find(channelID);
			if (it != channels.end())
			{
				delete it->second;
				channels.erase(it);
			}
		}
	}

	///////////////////////////特征检索 接口/////////////////////////////////////////
	/**
	* 检测目标特征
	* @param imgs 输入数据
	* @param userData 自定义数据
	*/
	void retrieveFeature(cv::Mat img, void* userData)
	{
#ifdef ENABLE_TRACK_FEATURE
		auto task = getModelTask(ModelTaskType::FeatureRetrieve, deviceId);
		if (task)
			task->submit(img, userData);
#endif
	}

	/**
	* 设置目标特征回调
	* @param callback 目标特征回调
	*/
	void setFeatureCallback(FeatureOutputCallback callback, int deviceID)
	{
#ifdef ENABLE_TRACK_FEATURE
		auto task = getModelTask(ModelTaskType::FeatureRetrieve, deviceID);
		if (task)
		{
			auto taskPtr = std::dynamic_pointer_cast<FeatureTask>(task);
			if(taskPtr)
				taskPtr->setOutputCallback(callback);
		}
#endif
	}
}
