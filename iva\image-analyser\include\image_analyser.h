/**
 * Project IVA (image analyser)
 */
#pragma once
#include "channel.h"
#include "model_data.h"

/**
*
* 图像分析模块
*/
namespace ia
{
	/**
	 * 获取通道
	 * @param channelID 通道ID
	 * @param deviceId GPU ID
	 */
    Channel *getChannel(int channel, int deviceId);

	/**
	 * 释放通道
	 * @param channelID 通道ID  (-1 清除所有)
	 */
	void releaseChannel(int channelID = -1);

	///////////////////////////特征检索 接口/////////////////////////////////////////
	/**
	* 检测目标特征
	* @param img 输入数据
	* @param userData 自定义数据
	*/
	void retrieveFeature(cv::Mat img, void* userData = nullptr);

	/**
	* 设置目标特征回调
	* @param callback 目标特征回调
	*/
	void setFeatureCallback(FeatureOutputCallback callback, int deviceID);
}
