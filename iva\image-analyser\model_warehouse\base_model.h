#pragma once

#include <mutex>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <chrono>
#include <iostream>
#include <fstream>
#include <cuda_runtime.h>
#include <cuda_runtime_api.h>
#include <NvInfer.h>
#include <NvOnnxConfig.h>
#include "NvInferPlugin.h"
#include "opencv2/core/core.hpp"
#include "opencv2/opencv.hpp"
#include "logging.h"
#include <sys/time.h>
#include "device_launch_parameters.h"
#include "util/algorithm_util.h"
#include "log.h"


using namespace nvinfer1;
using namespace nvonnxparser;
using namespace algorithm_util;

//extern "C" void calGpuDistance(float** f1,float** f2,float** dis,int outN,int outC);

namespace model_warehouse
{
    #define CHECK_CUDA_STATUS(cuda_status, error_str, isAssert) do { \
      if ((cuda_status) != cudaSuccess) {                        \
        IVA_LOG_ERROR ("{}, error:{} \n", \
            error_str, cudaGetErrorName(cuda_status));             \
        if (isAssert) assert((cuda_status) == cudaSuccess); \
      } \
    } while (0)

	enum class NetworkInputType
	{
		HWC,
		CHW
	};

    class CudaPredictor
    {
    public:
        /**
         * 模型相关初始化，加载模型
         * @param[in] path      模型路径
         * @param[in] deviceID  gpuId
         * @param[in] verbose   是否打卡日志verbose级别信息
         */
        bool init(string path, int deviceID, bool verbose = false);

        /**
         * 获取模型维度信息
         */
        Dims getBindingDimensions(int index);

        /**
         * 模型推理
         * @param buffers 待推理的帧数据
         * @param batch   batch size
         * @return
         */
        bool infer(vector<void *> &buffers, int batch = 1);

    private:
        /**
         * 加载序列化模型
         * @param path 模型路径
         * @return
         */
        bool loadModel(string path);
        bool prepare();
    private:
        std::shared_ptr<nvinfer1::IRuntime> runtime = nullptr;
        std::shared_ptr<nvinfer1::ICudaEngine> engine = nullptr;
        std::shared_ptr<nvinfer1::IExecutionContext> context = nullptr;
        std::shared_ptr<cudaStream_t> stream = nullptr;
    };

    template <class RESULT = bool>
    class BaseModel
    {
    public:
        BaseModel(std::string modelFile, int deviceID, NetworkInputType inputType = NetworkInputType::HWC, std::string meanFile = "") :
               deviceId(deviceID), modelPath(std::move(modelFile)), networkInputType (inputType), meanFilePath(std::move(meanFile)){};

        virtual ~BaseModel();

        /**
         * @brief               模型加载，资源初始化
         */
        virtual void initResource(){};

        /**
         * @brief                  图像预处理，padding、颜色通道转换、尺寸缩放
         * @param[in] images       原始输入图像
         * @param[in] padding      是否进行padding
         * @param[in] convertRGB   是否进行图像颜色转换
         * @param[out] imageData   预处理后输出的图像数据
         */
        virtual bool preprocess(std::vector<cv::Mat>& images, bool padding, bool convertRGB, vector<float>& imageData);

        /**
         * @brief                  多bach图像推理：预处理、推理、后处理输出
         * @param[in] images       原始输入图像
         * @param[in] padding      是否进行图像padding
         * @return                 推理后处理输出结果
         */
        virtual RESULT infer(std::vector<cv::Mat> images, bool padding) = 0;

        /**
         * @brief                  单bach图像推理：预处理、推理、后处理输出
         * @param[in] image        原始输入图像
         * @param[in] padding      是否进行图像padding
         * @return                 推理后处理输出结果
         */
         virtual RESULT infer(cv::Mat& image, bool padding){std::vector<cv::Mat> images = {image}; return infer(images, padding);};

        /**
         * @brief                  推理后处理，处理推理输出结果
         * @param imageSize        推理图像数量
         * @return                 推理后处理输出结果
         */
        virtual RESULT postprocess(int imageSize) = 0;

        /**
         * 获取模型的输入维度尺寸
         * @param[out] width 模型输入Width
         * @param[out] height 模型输入Height
         */
        void getInputSize(int& width,int& height);

        /**
         * 图像预处理 格式转换为CV_32FC3，尺寸缩放
         * @param[in]  img        输入待预处理的图像
         * @param[out] imageData  转换后的输出数据
         */
        void imagePreprocess(cv::Mat& img,vector<float>& imageData);

    protected:
        /**
         * @brief 初始化模型输入维度信息
         */
        virtual void initInputDimensions();

        /**
         * @brief 初始化模型输出维度信息
         */
        virtual void initOutputDimensions();

        /**
         * @brief                  读取均值文件
         */
        bool readMeanImageFile();

        /**
         * @brief                  图像padding处理
         * @param imageSize        待处理的图像
         * @return                 处理完的图像
         */
        virtual std::vector<cv::Mat> imagePadding(std::vector<cv::Mat>& images);

        /**
         * 尺寸缩放，如有均值文件，并进行均值预处理
         * @param[in]  src   待缩放的输入源图像
         * @param[in]  size  指定缩放尺寸
         * @param[out] data  缩放处理后的数据
         */
        void imageResize(const cv::Mat &src,cv::Size size,std::vector<float>& data);

    protected:

        //! 模型输入、输出信息
        int inputHeight   = 0;
        int inputWidth    = 0;
        int inputChannel  = 0;
        int outputHeight  = 0;
        int outputWidth   = 0;
        int outputChannel = 0;

        int batchSize    = 1;
        int downScale    = 0;                                //!< 特征模型下采样倍数
        NetworkInputType          networkInputType;          //!< 模型网络类型 NCHW 、 NWHC

        //! 模型资源相关
        int deviceId     = 0;                                //!< GPU ID
        std::string modelPath;                               //!< 模型路径
        atomic_bool modelInited = false;                     //!< 模型初始化资源加载标志

        //! 预处理相关
        int paddingValue = 0;                                //!< padding值
        std::string               meanFilePath;              //!< 均值文件路径
        std::shared_ptr<float []> meanFileData = nullptr;    //!< 均值数据

        //! 推理相关数据
        float*    imageDataDevice = nullptr;                 //!< 待推理的device侧帧数据
        CudaPredictor predictor;                             //!< 推理context

        std::shared_ptr<float[]> scoreDataHost = nullptr;    //!< 推理结果置信度 host侧
        float*                   scoreDataDevice = nullptr;  //!< 推理结果置信度 device侧

        float*                   boxDataDevice = nullptr;    //!< 推理结果box数据 host侧
        std::shared_ptr<float[]> boxDataHost = nullptr;      //!< 推理结果box数据 device侧
    };

}
#include "base_model.ixx"
