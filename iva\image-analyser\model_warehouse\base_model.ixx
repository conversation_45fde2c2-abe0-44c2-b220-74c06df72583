

namespace model_warehouse
{
    template <class RESULT>
    BaseModel<RESULT>::~BaseModel()
    {
       if (imageDataDevice)
           cudaFree(imageDataDevice);
       if (scoreDataDevice)
           cudaFree(scoreDataDevice);
       if(boxDataDevice)
           cudaFree(boxDataDevice);
    }

    /**
     * @brief 初始化模型输入维度信息
     */
    template <class RESULT>
    void BaseModel<RESULT>::initInputDimensions()
    {
        auto dims0 = predictor.getBindingDimensions(0);
        batchSize = dims0.d[0];
        if (networkInputType == NetworkInputType::CHW)
        {
            inputChannel = dims0.d[1];
            inputHeight  = dims0.d[2];
            inputWidth   = dims0.d[3];
        }
        else
        {
            inputHeight  = dims0.d[1];
            inputWidth   = dims0.d[2];
            inputChannel = dims0.d[3];
        }
    }

    /**
     * @brief 初始化模型输出维度信息
     */
    template <class RESULT>
    void BaseModel<RESULT>::initOutputDimensions()
    {
        auto dims1 = predictor.getBindingDimensions(1);
        outputChannel = dims1.d[1];
        outputHeight  = (dims1.d[2] < 1) ? 1 : dims1.d[2];
        outputWidth   = (dims1.d[3] < 1) ? 1 : dims1.d[3];
    }

    /**
     * 获取模型的输入维度尺寸
     * @param[out] width 模型输入Width
     * @param[out] height 模型输入Height
     */
    template <class RESULT>
    void BaseModel<RESULT>::getInputSize(int& width, int& height)
    {
        height = inputHeight;
        width = inputWidth;
    }

    /**
     * 尺寸缩放，如有均值文件，并进行均值预处理
     * @param[in]  src   待缩放的输入源图像
     * @param[in]  size  指定缩放尺寸
     * @param[out] data  缩放处理后的数据
     */
    template <class RESULT>
    void BaseModel<RESULT>::imageResize(const cv::Mat &src, cv::Size size,std::vector<float>& data)
    {
        cv::Mat dst;
        cv::resize(src, dst, size, (0.0), (0.0), cv::INTER_LINEAR);
        int h = size.height;
        int w = size.width;

        if (networkInputType == NetworkInputType::CHW)
        {
            readMeanImageFile();
            data.resize(w * h * 3);
            std::vector<float> dataTmp = (std::vector<float>)(dst.reshape(1, 1));

            for (int row=0; row < h; row++)
            {
                for (int col = 0; col < w; col++)
                {
                    for (int k = 0; k < 3; k++)
                    {
                        int dstIndex = w * h * k + row * w + col;
                        int srcIndex = row * w * 3 + col * 3 + k;
                        data[dstIndex] = (float)dataTmp[srcIndex];
                        if (meanFileData)
                            data[dstIndex] -= meanFileData[srcIndex];
                    }
                }
            }
        }
        else
        {
            data = (std::vector<float>)(dst.reshape(1, 1));
        }
    }

    template <class RESULT>
    std::vector<cv::Mat> BaseModel<RESULT>::imagePadding(std::vector<cv::Mat>& images)
    {
        std::vector<cv::Mat> imgPaddings;
        for (int i = 0; i < images.size();i++)
        {
            cv::Mat image = images[i];
            int h = image.size().height;
            int w = image.size().width;
            int s = std::max(h,w);

            cv::Mat imgPadding = cv::Mat::zeros(s,s,CV_8UC3);
            imgPadding.setTo(paddingValue);
            int wStart = int((s-w)/2);
            int hStart = int((s-h)/2);
            cv::Rect roi = cv::Rect(wStart,hStart,w,h);
            image.copyTo(imgPadding(roi));
            imgPaddings.push_back(imgPadding);
        }
        return imgPaddings;
    }


    /**
     * 读取均值文件
     */
    template <class RESULT>
    bool BaseModel<RESULT>::readMeanImageFile()
    {
        if (meanFileData != nullptr || meanFilePath.empty())
            return false;

        std::ifstream infile(meanFilePath, std::ifstream::binary);
        if (!infile.good() || !infile.is_open())
        {
            IVA_LOG_ERROR("failed to open mean file : {}" , meanFilePath)
            return false;
        }

        size_t size = inputHeight * inputWidth * inputChannel;
        uint8_t tempMeanDataChar[size];

        std::string magic, max;
        unsigned int h, w;
        infile >> magic >> w >> h >> max;
        if ((magic != "P3" && magic != "P6") || (w != inputWidth || h != inputHeight))
        {
            IVA_LOG_ERROR ("Mismatch between ppm mean image resolution and network resolution ");
            return false;
        }

        infile.get();
        infile.read((char*)tempMeanDataChar, size);
        if (infile.gcount() != (int)size || infile.fail())
        {
            IVA_LOG_ERROR("Failed to read sufficient bytes from mean file: {}" , meanFilePath)
            return false;
        }

        meanFileData = std::shared_ptr<float []>(new float[size]);
        for (size_t i = 0; i < size; i++)
        {
            meanFileData[i] = (float)tempMeanDataChar[i];
        }
        return true;
    }

    /**
     * 图像预处理 格式转换为CV_32FC3，尺寸缩放
     * @param[in]  img        输入待预处理的图像
     * @param[out] imageData  转换后的输出数据
     * @return
     */
    template <class RESULT>
    void BaseModel<RESULT>::imagePreprocess(cv::Mat& img, vector<float>& imageData)
    {
       cv::Mat src;
       if(img.channels() == 1)
           cv::cvtColor(img, src, cv::COLOR_GRAY2RGB);
        else
            src = img; //img.clone();

        cv::Mat input;
       src.convertTo(input, CV_32FC3);
       vector<float> data;
       imageResize(input, cv::Size(inputWidth, inputHeight), data);   //!< 尺寸缩放 NCHW和NHWC转换
       imageData.insert(imageData.end(), data.begin(), data.end());
    }

    /**
     * @brief                  图像预处理，padding、颜色通道转换、尺寸缩放
     * @param[in] images       原始输入图像
     * @param[in] padding      是否进行padding
     * @param[in] convertRGB   是否进行图像颜色转换
     * @param[out] imageData   预处理后输出的图像数据
     */
    template <class RESULT>
    bool BaseModel<RESULT>::preprocess(std::vector<cv::Mat>& images, bool padding, bool convertRGB, vector<float>& imageData)
    {
        std::vector<cv::Mat> tempImages = padding ? imagePadding(images) : images;

        for (int i = 0; i < tempImages.size(); i++)
        {
            cv::Mat img ;// .clone();
            if (convertRGB)
                cv::cvtColor(tempImages[i], img, cv::COLOR_BGR2RGB);
            else
                img = tempImages[i];
            imagePreprocess(img, imageData);
        }
        return true;
    }
}