#include "class_model.h"

#include <utility>

namespace model_warehouse{

    ClassModel::ClassModel(std::string modelFile, int deviceID, bool isVehicle, std::string meanFile)
				:BaseModel(std::move(modelFile), deviceID, isVehicle ? NetworkInputType::CHW : NetworkInputType::HWC, std::move(meanFile))
    {
		isVehicleModel = isVehicle;
    }

    /**
     * 模型加载，资源初始化
     */
    void ClassModel::initResource()
    {
        if (!modelInited)
        {
            predictor.init(std::move(modelPath), deviceId);

            initInputDimensions();
            initOutputDimensions();

            if (!scoreDataHost)
                scoreDataHost = std::shared_ptr<float[]>(new float[batchSize * outputChannel]);
            if (!scoreDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&scoreDataDevice, batchSize * outputChannel * sizeof(float)), "cudaMalloc failed", true);
            if (!imageDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&imageDataDevice, batchSize * inputWidth * inputHeight * inputChannel * sizeof(float)), "cudaMalloc failed", true);

            modelInited = true;
        }
    }

    /**
     * @brief 初始化模型输入维度信息
     */
    void ClassModel::initInputDimensions()
    {
        if (isVehicleModel)
        {
            auto dims0 = predictor.getBindingDimensions(0);
            batchSize = 1;
			inputChannel = dims0.d[1];
			inputHeight = dims0.d[2];
			inputWidth = dims0.d[3];
        }
        else
        {
            BaseModel::initInputDimensions();
        }
    }

    /**
     * @brief 初始化模型输出维度信息
     */
    void ClassModel::initOutputDimensions()
    {
        auto dims1 = predictor.getBindingDimensions(1);
        //outputChannel = isVehicleModel ? dims1.d[0] : dims1.d[1];
		outputChannel = dims1.d[1];
    }

    /**
     * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
     * @param[in] images       原始输入图像
     * @param[in]  padding     是否进行图像padding
     * @return                 推理后处理输出结果
     */
    CLASS_RESULT ClassModel::infer(std::vector<cv::Mat> images, bool padding)
    {
        vector<float> imageData;
        preprocess(images, false, !isVehicleModel, imageData);

        CHECK_CUDA_STATUS(cudaMemcpy(imageDataDevice, imageData.data(), imageData.size() * sizeof(float), cudaMemcpyHostToDevice),"Failed to copy image data to device",false);
        vector<void *> buffers = {imageDataDevice, scoreDataDevice};
        if (!predictor.infer(buffers, (int)images.size()))
            return {};

        return postprocess((int)images.size());
    }

    /**
     * @brief                  推理后处理，处理推理输出结果
     * @param imageSize        推理图像数量
     * @return                 推理后处理输出结果
     */
    CLASS_RESULT ClassModel::postprocess(int imageSize)
    {
        std::vector<std::vector<float>> scores;
        CHECK_CUDA_STATUS(cudaMemcpy(scoreDataHost.get(), scoreDataDevice, batchSize * outputChannel * sizeof(float), cudaMemcpyDeviceToHost),"Failed to copy score data to host", false);

        for(int i=0;i< imageSize; i++)
        {
            float sumScores = 0.0;
            float minScore = 999999999.0;

            for(int j=0; j < outputChannel; j++)
            {
                if(minScore > scoreDataHost[i * outputChannel + j])
                    minScore = scoreDataHost[i * outputChannel + j];
            }

            if (minScore < 0)
            {
                for(int j=0; j < outputChannel; j++)
                    scoreDataHost[i * outputChannel + j] = scoreDataHost[i * outputChannel + j] - minScore + 0.01;
            }

            for(int j=0; j < outputChannel; j++)
                sumScores += scoreDataHost[i * outputChannel + j];

            std::vector<float> score;
            for(int j=0; j < outputChannel; j++)
                score.push_back(scoreDataHost[i * outputChannel + j] / sumScores);

            scores.push_back(score);
        }

        return scores;
    }


    std::optional<int> ClassModel::getMaxResult(std::vector<float>& results, float threshold)
    {
        auto itMax = std::max_element(results.begin(), results.end());
        if ((*itMax) > threshold)
        {
            int klass =(int)(itMax - results.begin());
            return klass;
        }
        return std::nullopt;
    }
}
