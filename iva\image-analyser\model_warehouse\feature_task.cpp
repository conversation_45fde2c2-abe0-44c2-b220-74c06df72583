/**
 * Project IVA (image analyser)
 */

#include "feature_task.h"
#include "config/detect_config.hpp"
#include "config/feature_model_config.hpp"


/**
* 特征推理队列任务 (图像特征 及车牌号)
*/
namespace ia {

	union HexFloat
	{
		float f;
		int i;
	};

    static void split(const std::string& input, std::vector<std::string>& tokens, const std::string& delimiters = " ")
    {
        auto lastPos = input.find_first_not_of(delimiters, 0);
        auto pos = input.find_first_of(delimiters, lastPos);
        while (std::string::npos != pos || std::string::npos != lastPos)
        {
            tokens.emplace_back(input.substr(lastPos, pos - lastPos));
            lastPos = input.find_first_not_of(delimiters, pos);
            pos = input.find_first_of(delimiters, lastPos);
        }
    }

    FeatureAction::FeatureAction(int deviceID) : BaseModelAction(deviceID)
	{
	}

	FeatureAction::~FeatureAction()
	{
		if (featureModel)
			delete featureModel;

		if (plateLocateModel)
			delete plateLocateModel;

		if (plateRecognizeModel)
			delete plateRecognizeModel;
	}

	/**
	* 检测模型初始化
	*/
	void FeatureAction::checkInitModels()
	{
		if (!featureModel)
		{
			std::cout << DETECT_CFG->modelPath() + "/" + FEATURE_MODEL_CFG->featureModelPath() << std::endl;
			featureModel = new FeatureModel(DETECT_CFG->modelPath() + "/" + FEATURE_MODEL_CFG->featureModelPath(), deviceId);
            featureModel->initResource();
		}

		if (!plateLocateModel)
		{
			std::string path = DETECT_CFG->modelPath() + "/" + FEATURE_MODEL_CFG->plateLocateModelPath();
			std::cout << path << std::endl;
			plateLocateModel = new DetectModel(path, deviceId, FEATURE_MODEL_CFG->plateLocateScoreThresh(), FEATURE_MODEL_CFG->plateLocateIouThresh(), 128);
            plateLocateModel->initResource();
		}

		if (!plateRecognizeModel)
		{
            parsePlateLabelsFiles();
			std::string path = DETECT_CFG->modelPath() + "/" + FEATURE_MODEL_CFG->plateRecognizeModelPath();
			std::cout << path << std::endl;
			plateRecognizeModel = new DetectModel(path, deviceId, FEATURE_MODEL_CFG->plateRecognizeScoreThresh(), FEATURE_MODEL_CFG->plateRecognizeIouThresh(), 128);
            plateRecognizeModel->initResource();
			//plateLocateModel->setParam(0.3f, 0.5f, 0);
		}
	}

	/**
	* 特征推理处理
	* @param input 输入数据
	*/
	FeatureOutputData FeatureAction::process(ModelInputData& input)
	{
		FeatureOutputData ouput;
		ouput.userData = input.userData;

		// 检测模型初始化
		checkInitModels();

		// 1、特征提取
		auto featureOutput = featureModel->infer(input.image, false);

		for (auto& vec : featureOutput)
		{
			for (float f : vec)
			{
				//HexFloat data;
				//data.f = f;
				//
				//char hexStr[9]{ 0 };	// 浮点数--> 十六进制
				//sprintf(hexStr, "%X", data.i);
				//ouput.feature.emplace_back(hexStr);
				ouput.feature.emplace_back(f);
			}
		}

		// 2、车牌定位
		std::vector<std::string> results;
        auto outBoxes = plateLocateModel->infer(input.image, true);
		if(outBoxes.empty() || outBoxes[0].empty())
			return ouput;
		
		std::vector<ObjBox> plateBoxs = outBoxes[0];
		std::sort(plateBoxs.begin(), plateBoxs.end(), [](ObjBox& a, ObjBox& b) {
				return a.score > b.score;
			});
		
		ObjBox box = plateBoxs[0];
		
		// 3、车牌识别
		float scaleParam = 0.3f;
		int bia = scaleParam * box.height;
		int x2 = int(box.width + box.x + bia);
		int y2 = int(box.height + box.y + bia);
		box.x = max(1, int(box.x - bia));
		box.y = max(1, int(box.y - bia));
		box.width = min(input.image.size().width - 1, x2) - box.x;
		box.height = min(input.image.size().height - 1, y2) - box.y;
		cv::Rect rect = cv::Rect(int(box.x), int(box.y), int(box.width), int(box.height));
		cv::Mat imgSeg = input.image(rect);

        auto charOutBoxes = plateRecognizeModel->infer(imgSeg , true);

		std::string plateNum;
		try
		{
			std::vector<float> Xs;
			std::vector<float> Ys;
			for (auto out : charOutBoxes[0]) {
				out.x = out.x + box.x;
				out.y = out.y + box.y;
				Xs.push_back(-1.0 * out.x);
				Ys.push_back(-1.0 * out.y);
			}

			std::vector<int> indices = getSortIndex(Xs);
			if (box.class_ == 2 || box.class_ == 3) {
				if (Xs.size() >= 6 && Xs.size() <= 8) {
					std::vector<int> indices_y = getSortIndex(Ys);
					auto& elemY0 = charOutBoxes[0][indices_y[0]];
					auto& elemY1 = charOutBoxes[0][indices_y[1]];
					auto& elem0 = charOutBoxes[0][indices[0]];
					int y3 = charOutBoxes[0][indices_y[3]].y;
					int y0 = charOutBoxes[0][indices_y[0]].y;
					float meanH = 0.0;
					for (auto out : charOutBoxes[0]) {
						meanH += out.height;
					}
					meanH = meanH / charOutBoxes[0].size();
					if (elem0.class_<36 && (y3 - y0)>(meanH * 0.4) && ((elemY0.class_ >= 36) || (elemY1.class_ >= 36) || (elemY0.class_ == 18) || (elemY1.class_ == 18))) {
						std::vector<int> newIndices = { indices_y[0],indices_y[1] };
						if (charOutBoxes[0][newIndices[0]].x > charOutBoxes[0][newIndices[1]].x) {
							int tmp = newIndices[0];
							newIndices[0] = newIndices[1];
							newIndices[1] = tmp;
						}

						for (auto index : indices) {
							if (index != newIndices[0] && index != newIndices[1]) {
								newIndices.push_back(index);
							}
						}
						indices = newIndices;
					}
				}
			}
			for (int j = 0; j < indices.size(); j++) {
				plateNum += plateChars[charOutBoxes[0][indices[j]].class_];
			}
		}
		catch (const std::exception& e)
		{
			std::cerr << "Plate recognize error:" << e.what() << std::endl;
		}
		
		ouput.plateNum = plateNum;
		ouput.plateColor = plateColors[box.class_];
		return ouput;
	}

    /**
     *	从label.txt中读取车牌号、颜色标签
     */
    bool FeatureAction::parsePlateLabelsFiles()
    {
        if (!plateColors.empty() && !plateChars.empty())
            return true;

        std::string labelsFile = DETECT_CFG->modelPath() + "/" +  FEATURE_MODEL_CFG->plateLabelsPath();

        if (!fs::exists(labelsFile) || (fs::is_empty(labelsFile)))
        {
            IVA_LOG_ERROR("\n\n\n车牌标签配置文件{}不存在或者配置为空\n\n\n", labelsFile)
            return false;
        }

        std::ifstream infile(labelsFile);

        std::string lineContent;
        std::getline(infile, lineContent);
        split(lineContent, plateColors, ";");
        if (plateColors.empty())
        {
            IVA_LOG_ERROR("\n\n\n车牌标签配置文件{},车牌颜色配置错误{},请使用分号间隔\n\n\n" ,labelsFile, lineContent)
            return false;
        }

        lineContent.clear();
        std::getline(infile, lineContent);
        split(lineContent, plateChars, ";");
        if (plateChars.empty())
        {
            IVA_LOG_ERROR("\n\n\n车牌标签配置文件{},车牌号配置错误{},请使用分号间隔\n\n\n" ,labelsFile, lineContent)
            return false;
        }
        return true;
    }

}

