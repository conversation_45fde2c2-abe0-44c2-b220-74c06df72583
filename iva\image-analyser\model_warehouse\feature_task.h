/**
 * Project IVA (image analyser)
 */

#pragma once
#include "model_task.h"
#include "detect_model.h"
#include "class_model.h"
#include "feature_model.h"

/**
* 特征推理队列任务 (图像特征 及车牌号)
*/
namespace ia {
	using namespace model_warehouse;

	/**
	* 特征提取流程
	*/
	class FeatureAction : public BaseModelAction<FeatureOutputData> {
	public:
		FeatureAction(int deviceID = 0);
		~FeatureAction();

		/**
		* 特征推理处理
		* @param input 输入数据
		*/
		FeatureOutputData process(ModelInputData& input) override;

		/**
		* 检测模型初始化
		*/
		void checkInitModels();

        /**
         *	从label.txt中读取车牌号、颜色标签
         */
        bool parsePlateLabelsFiles();

	private:
		// 特征提取模型
		FeatureModel* featureModel = nullptr;
		// 车牌定位
		DetectModel* plateLocateModel = nullptr;
		// 车牌识别
		DetectModel* plateRecognizeModel = nullptr;

        // 车牌颜色
        std::vector<std::string> plateColors;
        // 车牌号
        std::vector<std::string> plateChars;
	};

	typedef ModelTask<FeatureOutputData> FeatureTask;
}
