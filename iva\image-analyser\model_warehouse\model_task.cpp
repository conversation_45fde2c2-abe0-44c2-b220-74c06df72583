/**
 * Project IVA (image analyser)
 */

#include <map>
#include <mutex>
#include <string>
#include "model_task.h"
#include "feature_task.h"

namespace ia {
	std::mutex mutexModel;
	std::map<ModelTaskType, ModelTaskPtr> modelTasks;

	/**
	* 获取模型任务
	* @param taskType 任务类型
	*/
	ModelTaskPtr getModelTask(ModelTaskType taskType, int deviceID)
	{
		std::lock_guard<std::mutex> m(mutexModel);
		if (modelTasks.find(taskType) != modelTasks.end())
			return modelTasks[taskType];

		ModelTaskPtr task = nullptr;
		switch (taskType)
		{
		case ModelTaskType::FeatureRetrieve:
			task = modelTasks[taskType] = std::make_shared<FeatureTask>(new FeatureAction(deviceID));
			break;
		}
		return task;
	}
}
