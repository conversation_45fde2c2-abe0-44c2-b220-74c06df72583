/**
 * Project IVA (image analyser)
 */

#pragma once
#include <queue>
#include <mutex>
#include <thread>
#include <condition_variable>
#include "model_data.h"

/**
* 模型推理队列任务
*
*		<Action>	推理流程
*		<Task>		基于Action封装的异步任务
*/
namespace ia {

	template<class OUTPUT>
	class BaseModelAction {
	public:
        explicit BaseModelAction(int deviceID) : deviceId(deviceID){};
		virtual OUTPUT process(ModelInputData& input) = 0;

    protected:
        int deviceId = 0;
	};

	class BaseModelTask {
	public:
		/**
		* 提交任务请求
		* @param img 输入图像
		* @param userData 用户数据
		*/
		virtual void submit(cv::Mat img, void* userData) = 0;
	};

	/**
	* 基础模型推理流程
	*/
	template<class MODEL>
	class ModelAction : public BaseModelAction<ModelOutputData> {
	public:
		ModelAction(std::string path){modelPath = path;}
		~ModelAction(){delete model;}

		ModelOutputData process(ModelInputData& input)override
		{
			if (model == nullptr)
			{
				int ret;	//TODO gpuid 设置、 ret写法完善
				model = new MODEL(modelPath, ret);
			}
			ModelOutput result = model->predictNoPadding({ input.image });
			return { result, input.userData };
		}
	private:
		MODEL* model = nullptr;
		std::string modelPath;
	};


	/**
	* 基础模型推理任务
	*/
	template<class OUTPUT>
	class ModelTask : public BaseModelTask {
	public:
		ModelTask(BaseModelAction<OUTPUT>* action)
		{
			modelAction = action;
			processThread = std::thread([this]() {onModelActionRun(); });
		}

		~ModelTask()
		{
			running = false;
			processThread.join();
			delete modelAction;
		}

		/**
		* 设置输出回调
		* @param callback 回调接口
		*/
		void setOutputCallback(std::function<void(OUTPUT)> callback)
		{
			outputCallback = callback;
		}

		/**
		* 提交推理任务
		* @param img 输入图像
		* @param userData 用户数据
		*/
		void submit(cv::Mat img, void* userData) override
		{
			std::lock_guard<std::mutex> m(inputMutex);
			inputQueue.emplace(std::move(ModelInputData{ img, userData}));
			inputCondition.notify_one();
		}

		void onModelActionRun()
		{
			while (running)
			{
				std::unique_lock<std::mutex> syncLock(condMutex);
				if (!inputCondition.wait_for(syncLock, std::chrono::milliseconds(20),
					[this]() { return !this->inputQueue.empty(); }))
				{
					continue;
				}

				inputMutex.lock();
				auto data = inputQueue.front();
				inputQueue.pop();
				inputMutex.unlock();

				OUTPUT output = modelAction->process(data);
				if (outputCallback)
					outputCallback(output);
			}
		}

	private:
		bool running = true;

		std::thread processThread;
		std::mutex inputMutex;
		std::mutex condMutex;
		std::queue<ModelInputData> inputQueue;
		std::condition_variable inputCondition;

		std::function<void(OUTPUT)> outputCallback;
		BaseModelAction<OUTPUT>* modelAction = nullptr;
	};

	typedef std::shared_ptr<BaseModelTask> ModelTaskPtr;
	typedef ModelTask<ModelOutputData> CommonModelTask;

	/**
	* 模型任务类型
	*/
	enum class ModelTaskType
	{
		FeatureRetrieve,	// 目标特征
	};

	/**
	* 获取模型任务
	* @param taskType 任务类型
	*/
	ModelTaskPtr getModelTask(ModelTaskType taskType, int deviceID);
}
