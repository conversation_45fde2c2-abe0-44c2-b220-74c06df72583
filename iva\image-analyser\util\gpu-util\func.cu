#include "cuda_runtime.h"  
#include "device_launch_parameters.h"  
#include <vector>
#include <stdio.h>
#include "device_functions.h"
using namespace std;


__global__ void calDistance1(float* f1, float* f2, float* disTmp, int N, int nBlockNum, int threadnum)
{
    int nTidIdx = blockIdx.x * blockDim.x + threadIdx.x;
    disTmp[nTidIdx] = (f1[nTidIdx] - f2[nTidIdx])*(f1[nTidIdx] - f2[nTidIdx]);

}

__global__ void calDistance2(float disTmp[], float dis[],int channel)
{
    float tmp = 0.0;
    for(int i=0;i<channel;i++){
        tmp += disTmp[blockIdx.x*channel+i];
    }
    dis[blockIdx.x] = sqrt(tmp);
}

extern "C" void calGpuDistance(float** f1,float** f2,float** dis,int outN,int outC){
    int n=outN;
    int c=outC;
    float* dotCuda;
    float* disCuda;
    cudaMalloc((float**)&dotCuda,n*c*sizeof(float));
    cudaMalloc((float**)&disCuda,n*1*sizeof(float));
    calDistance1<<<n, c>>>(*f1, *f2, (float *)dotCuda,n*c,n,c);
    calDistance2<<<n, 1>>>((float *)dotCuda, (float *)disCuda,c);

    *dis = new float[n];

    cudaMemcpy(*dis, disCuda,n*sizeof(float), cudaMemcpyDeviceToHost);
    cudaFree(dotCuda);
    cudaFree(disCuda);

    //  float* ff1;
    //  ff1 = new float[8040*40];
    //  cudaMemcpy(ff1, *f1,8040*40*sizeof(float), cudaMemcpyDeviceToHost);
    //  float* ff2;
    //  ff2 = new float[8040*40];
    //  cudaMemcpy(ff2, *f2,8040*40*sizeof(float), cudaMemcpyDeviceToHost);
    //  float* score;
    //  score = new float[8040*40];
    //  cudaMemcpy(score, dotCuda,8040*40*sizeof(float), cudaMemcpyDeviceToHost);
    //  float* score1;
    //  score1 = new float[8040];
    //  cudaMemcpy(score1, disCuda,8040*sizeof(float), cudaMemcpyDeviceToHost);

    //  for(int j = 0; j < 1000; j++) {
    //            printf("%.4f ",ff1[j]);
    //            printf("%.4f ",ff2[j]);
    //            printf("%.4f ",score[j]);
    //            printf("%.4f ",score1[j]);
    //            printf("%.4f ",(*dis)[j]);
    //            printf(" ###  ");
    //  }




}
