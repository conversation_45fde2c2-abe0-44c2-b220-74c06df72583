#pragma once
#include <numeric>
#include <iostream>
#include "frame_data.h"
#include "opencv2/core.hpp"

namespace ia
{
    //! 目标框统计计算
    struct ObjectStatistic
    {
        cv::Point2f mean;            //!< 坐标点集合均值
        double meanBoxDiagonal;      //!< box对角线长度
        double meanDistance;         //!< 相邻box的距离

        /**
         * 获取目标框的x中心点
         */
        static float centerX(ImageObject &obj)
        { return obj.x + 0.5f * obj.width; }

        /**
         * 获取目标框的y中心点
         */
        static float centerY(ImageObject &obj)
        { return obj.y + 0.5f * obj.height; }

        /**
         * 计算一组目标框的方差
         */
        cv::Point2f calculateVariance(ImageObjectList &objs)
        {
            //! 计算x,y的累计和
            auto size = (float) objs.size();
            if (size < 2)
                return {0.f, 0.f};

            auto sum = std::accumulate(objs.begin(), objs.end(), ImageObject(),[&](ImageObject &lastSum, ImageObject &obj){
                lastSum.x += obj.x; lastSum.y += obj.y;
                return lastSum;
            });

            //! 计算x,y的均值
            mean = {sum.x / size, sum.y / size};

            //! 计算坐标点集合x,y的样本方差
            auto variance = std::accumulate(objs.begin(), objs.end(), ImageObject(),[this, size](ImageObject &lastSum, ImageObject &obj) {
                lastSum.x += powf((obj.x - mean.x), 2) / (size - 1);
                lastSum.y += powf((obj.y - mean.y), 2) / (size - 1);
                return lastSum;
            });
            return {variance.x, variance.y};
        }

        /**
         * 计算x,y的标准差
         */
        cv::Point2f calculateStddev(ImageObjectList &objs)
        {
            if (objs.size() < 2)
                return {0.f, 0.f};
            auto variance = calculateVariance(objs);
            return {sqrtf(variance.x / ((float) objs.size() - 1)), sqrtf(variance.y / ((float) objs.size() - 1))};
        }

        /**
         * 计算一组目标框的标准差系数
         */
        cv::Point2f calcStddevCoefficient(ImageObjectList &objs)
        {
            auto stddev = calculateStddev(objs);
            return {stddev.x / mean.x, stddev.y / mean.y};
        }

        /**
         * 计算一组目标框的一些平均值，包括相邻距离均值，对角线均值
         */
        void calcMeanValue(ImageObjectList &objs)
        {
            if (objs.size() == 1)
            {
                meanBoxDiagonal = std::hypot(objs[0].width, objs[0].height);
                meanDistance = 0;
            }
            else if (objs.size() > 1)
            {
                double distance = 0;
                double diagonal = 0;
                for (auto it = objs.begin(); std::next(it) != objs.end(); ++it)
                {
                    diagonal += std::hypot(it->width, it->height);
                    distance += std::hypot((std::next(it)->x - it->x), (std::next(it)->y - it->y));
                }

                meanBoxDiagonal = diagonal / ((double) objs.size() - 1.f);
                meanDistance = distance / ((double) objs.size() - 1.f);
            }
        }
    };
}