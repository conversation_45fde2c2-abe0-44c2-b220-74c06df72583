
## AI图像搜索服务(image-search-service)

### 主要接口
-----------------------------
```html
1. 待搜图片预处理
【接口名称】	待搜图片预处理
【功能描述】	对待搜索的图片进行预处理，识别单张图片中的车辆位置。
【发送者】	AI视频分析数据平台服务端
【接收者】	算法模块
【url】		/api/detect 
【请求方式】	POST
【输入参数】	img,图片数据,Base64数值
【输入示例】
```	
```json
		{
		        "img":"<raw image data>"
		}
```
```html
【输出参数】
			msg,字符串,接口返回值提示信息
			code,整型,接口业务状态码,200:正常，500+:异常
			data,数组,结果数据集
			rect,数组,车辆在图片中的位置信息[左上,右下]
【输出示例】
```	

```json
		{
			"code":200,
			"msg":"success"
			"data":[{"rect":[{"x":0.786931,"y":0.5138888},{"x":0.80965906,"y":0.60416}]},{"rect":[{"x":0.786931,"y":0.5138888},{"x":0.80965906,"y":0.60416}]}]
		}
```
```html
2. 识别车辆属性
【接口名称】	识别车辆属性
【功能描述】	基于图片和图片中目标车辆的位置信息，识别车辆的特征值、车型、颜色、车牌等属性。
【发送者】	AI视频分析数据平台服务端
【接收者】	算法模块
【url】		/api/feature
【请求方式】	POST
【输入参数】
			img,图片数据,Base64数值
			rect,数组,车辆在图片中的位置信息[左上,右下]
【输入示例】
```	
```json
		{
		        "img":"<raw image data>"
				"rect":[{"x":0.786931,"y":0.5138888},{"x":0.80965906,"y":0.60416}]
		}
```
```html
【输出参数】
			msg,字符串,接口返回值提示信息
			code,整型,接口业务状态码,200:正常，500+:异常
			vehicle,对象类型,车辆对象
			vehicleType,整型,车辆类型
			vehicleColor,字符串,车辆颜色
			plateNum,字符串,车牌号码
			plateColor，字符串,车牌颜色
			vehicleFeature,数组，车辆特征值

【输出示例】
```	
```json
		{
			"code":200,
			"msg":"success"
			"vehicle":
			{
				"vehicleType":"car",
				"vehicleColor":"black",
				"plateNum":"鄂A12345",
				"plateColor":"blue"
				"vehicleFeature":[[0.608647346496582,0.11369985342025757,...]]
			}
		}
```
