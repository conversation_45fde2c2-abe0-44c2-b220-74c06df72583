#pragma once

#include "cereal/cereal.hpp"
#include "protocol/base.h"

namespace iss::server
{
	#define NVP cereal::make_nvp
	using namespace network;

	constexpr auto DEFAULT_PORT = 9090;

	/*
	* 返回错误码
	*/
	enum Error_Code
	{
		Code_Success = 200,
		Code_Error_NoInputImage = 501,
		Code_Error_NoInputRect,
		Code_Error_InputError,
		Code_CV_Error,
		Code_Infer_Error,
		Code_Inner_Error
	};

	/*
	* 特征信息
	*/
	struct FeatureInfo
	{
		std::string vehicleColor;					//目标颜色
		std::string vehicleType;					//目标类型
		std::string plateNum;						//车牌号码
		std::string plateColor;						//车牌颜色
		std::vector<float> vehicleFeature;			//特征

		template <class Archive>
		void serialize(Archive& ar)
		{
			ar(NVP("vehicleColor", vehicleColor));
			ar(NVP("vehicleType", vehicleType));
			ar(NVP("plateNum", plateNum));
			ar(NVP("plateColor", plateColor));
			ar(NVP("feature", vehicleFeature));
		}
	};

	/*
	* 特征信息（返回json消息）
	*/
	struct FeatureMsg
	{
		int code = Code_Success; //错误码
		std::string msg;		//错误信息
		FeatureInfo vehicle;	//车辆特征

		template <class Archive>
		void serialize(Archive& ar)
		{
			ar(NVP("code", code));
			ar(NVP("msg", msg));
			ar(NVP("vehicle", vehicle));
		}

		void setMsgCode(int code);
	};

	/*
	* 目标检测框
	*/
	struct DetectInfo
	{
		std::vector<Point> rect;	//目标框 (左上，右下)

		template <class Archive>
		void serialize(Archive& ar)
		{
			ar(NVP("rect", rect));
		}
	};

	/*
	* 目标检测信息（返回json消息）
	*/
	struct DetectMsg
	{
		int code = Code_Success; //错误码
		std::string msg;		//错误信息
		std::vector<DetectInfo> data;	//目标框列表

		template <class Archive>
		void serialize(Archive& ar)
		{
			ar(NVP("code", code));
			ar(NVP("msg", msg));
			ar(NVP("data", data));
		}

		void setMsgCode(int code);
	};

	/*
	* 初始化HTTP服务
	* @param port 监听端口
	* @param deviceId 设备ID
	*/
	void init(int port= DEFAULT_PORT, int deviceId= 0);

	// 日志相关
	#define INIT_LOG setupLog("iss")
	#define ISS_LOG getLog("iss")

	#define ISS_LOG_TRACE(content,  args...) CHECK_IF(ISS_LOG, ISS_LOG->trace(content, ##args))
	#define ISS_LOG_DEBUG(content,  args...) CHECK_IF(ISS_LOG, ISS_LOG->debug(content, ##args))
	#define ISS_LOG_INFO(content,  args...) CHECK_IF(ISS_LOG, ISS_LOG->info(content, ##args))
	#define ISS_LOG_WARN(content,  args...) CHECK_IF(ISS_LOG, ISS_LOG->warn(content, ##args))
	#define ISS_LOG_ERROR(content,  args...) CHECK_IF(ISS_LOG, ISS_LOG->error(content, ##args))

}
