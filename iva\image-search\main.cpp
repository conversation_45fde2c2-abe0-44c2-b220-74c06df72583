#include <iostream>
#include <cuda_runtime_api.h>
#include "http_server.h"
#include "log.h"

int main(int argc, char *argv[])
{
	INIT_LOG;

    // 检查显卡及驱动情况
    struct cudaDeviceProp prop{};
    auto cudaError = cudaGetDeviceProperties(&prop, 0);
    if (cudaError != cudaSuccess)
    {
		ISS_LOG_ERROR("GPU device: {}, id: {}, error: {},  please check the NVIDIA driver, device status and CUDA runtime etc.,run nvidia-smi for details"
													, prop.name, 0, static_cast<int>(cudaError));
        FINISH_LOG();
        std::getchar();
        return 0;
    }

	// 解析自定义端口
	int port = iss::server::DEFAULT_PORT;
	if (argc > 1)
	{
		try{
			port = std::stoi(argv[1]);
		}
		catch (std::exception& e) {
			ISS_LOG_ERROR("\n 启动端口配置错误{}  {} \n", argv[1], e.what());
		}
	}

	// 解析自定义设备id
	int deviceId = 0;
	if (argc > 2)
	{
		try {
			deviceId = std::stoi(argv[2]);
		}
		catch (std::exception& e) {
			ISS_LOG_ERROR("\n 启动设备ID配置错误{}  {} \n", argv[2], e.what());
		}
	}

	// 启动http 服务
	iss::server::init(port, deviceId);

    std::string input;
    while(true)
    {
        std::cin>>input;
        if (input == "quit")
        {
            break;
        }
    }
	ISS_LOG_DEBUG("[ISS]==process exit.");
	FINISH_LOG();
    return 0;
}
