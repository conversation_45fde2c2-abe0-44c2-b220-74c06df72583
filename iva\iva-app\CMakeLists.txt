cmake_minimum_required (VERSION 3.5.2)
## 目标生成
set(APP_NAME "iva-app")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/out)
set(LIB_IVA ${PROJECT_SOURCE_DIR}/out/lib)

add_compile_options(-g -std=c++17 -fPIC -fstack-protector-all -Wno-unknown-pragmas -Wno-unused-variable -Wno-unused-function -Werror=return-type -Wall)

find_package(PkgConfig)
pkg_check_modules(GST REQUIRED gstreamer-1.0>=1.4
                               gstreamer-base-1.0>=1.4
                               gstreamer-video-1.0>=1.4)

# boost
set(BOOST_HOME "/opt/boost")
set(BOOST_LIB
        boost_fiber
        boost_context
        boost_filesystem
        boost_thread
        boost_log
        boost_log_setup
)

# 头文件
include_directories(
	${PLATFORM_INCLUDES}
    ${BOOST_HOME}/include/
    ${GST_INCLUDE_DIRS}
	${OpenCV_INCLUDE_DIRS}
    ${PROJECT_SOURCE_DIR}/iva-log
    ${PROJECT_SOURCE_DIR}/common
    ${PROJECT_SOURCE_DIR}/network
    ${PROJECT_SOURCE_DIR}/network/include
    ${PROJECT_SOURCE_DIR}/event-analyser/include
    protocol
    pipeline
    includes
    plugins/gst-iva-plugin-1/record
)

# 库路径
link_directories(
    ${PLATFORM_LIBRARIES_DIR}
    ${BOOST_HOME}/lib/
    ${LIB_IVA}
)

# 当前文件夹cpp
FILE(GLOB src "src/*.cpp")
SET(ALL_SRC ${include} ${src})

# 生成iva-app
add_executable(${APP_NAME} ${ALL_SRC} ${protocol} main.cpp)

target_link_libraries(${APP_NAME} stdc++fs rt pthread ${PLATFORM_LIBRARIES}
    ${GST_LIBRARIES}  gstivaplugin1 ivacommon ivalog ai_network event_analyser iva_protocol)

if(DEFINED PLATFORM_DEPENDENCIES)
	add_dependencies(${APP_NAME} ${PLATFORM_DEPENDENCIES})
endif()

#iva插件
add_subdirectory(plugins)
#协议库
add_subdirectory(protocol)
#模型相关
add_subdirectory(model)

SET(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
set_target_properties(${APP_NAME} PROPERTIES INSTALL_RPATH "./lib/")
