#pragma once
#include <string>
#include "ini/inibase.h"
#include "version.h" // Generated by CMAKE

static std::string getVersion() {
	auto tt = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
	struct tm *ptm = localtime(&tt);
	int iYear = ptm->tm_year + 1900;

	char version[128];
	sprintf(version, "%s Copyright © 2016 - %d  WELLTRANS O&E CO., All Rights Reserved.", IVA_VERSION, iYear);
	return version;
}

#if CAMBRICON
	#ifdef CAMBRICON_MLU270
		#define DEFAULT_INTERVAL 1
	#else
		#define DEFAULT_INTERVAL 1
	#endif // CAMBRICON
#else
	#define DEFAULT_INTERVAL 1
#endif // !CAMBRICON

/*
 * IVA 配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(iva, "config/iva.ini",
(int,	maxSizeBuffers,			pipe,   20,	u8"管线buffer的最大尺寸")
(int,	batchTimeout,			pipe,   33000,  u8"批处理超时")
(int,	udpBufferSize,			pipe,   0,		u8"UDP接收缓冲区字节大小")
//(bool,	enableImageAnalyser,	option, true,	u8"是否开启图像诊断模块")
(bool,	usingCustomOsd,			option, false,	u8"是否使用定制化的事件截图")
//(bool,	enableFvmOffsetFilter,  option, true,	u8"是否使用fvm偏移事件过滤")
(bool,	usingPersonModel,		option, false,	u8"是否使用多模型（区分行人）")
#ifdef USE_NEW_STREAMMUX
(int, videoWidth, video, 1920, u8"输出视频-宽") 
(int, videoHeight, video, 1080, u8"输出视频-高")
#else
(int, videoWidth, video, 640, u8"输出视频-宽") //744
(int, videoHeight, video, 384, u8"输出视频-高") //416
#endif // !USE_NEW_STREAMMUX
(int, framerate, video, 0, u8"输出视频-帧率")
(int, bitrate, video, 2800, u8"输出视频-码率kbps")
(int,   interpolation,			video,  3,		u8"插值方法")
(bool,  drawTrackLine,			osd,	true,	u8"是否绘制拖尾")
(bool,  drawSpeed,				osd,	false,	u8"是否绘制车速")
(bool,  drawTypeID,				osd,	false,	u8"是否绘制ID")
(bool,  drawEventROI,			osd,	false,	u8"是否绘制事件ROI")
(bool,  drawConstruct,			osd,	false,	u8"是否绘制施工关联目标")
(bool,  drawAllPedstrain,		osd,	false,	u8"是否绘制所有行人")
(bool,  drawImageEvent,			osd,	true,	u8"是否实时绘制图像事件")
//(int,   trackerType,			tracker, 1,		u8"跟踪类型 |0 KF&munkres 1 IOU 2 KLT")
(int,   vehicleInferInterval,	infer, DEFAULT_INTERVAL,u8"推理抽帧(车辆)")
(int,   nonVehicleInferInterval,infer,	 5,		u8"推理抽帧(非车辆)")
(int,	firesmokeInterval,		infer,	 45,	u8"推理抽帧(烟火)")
(int,	roadblockInterval,		infer,	 125,	u8"推理抽帧(路障)")
(int,	laneInterval,			infer,	 200,	u8"推理抽帧(车道线)")
(int,	roiobjectInterval,		infer,	 125,	u8"推理抽帧(抛洒物)")
(string, fontPath,	path, "/data/opt/fonts/simhei.ttf", u8"中文字体目录路径")
(string, modelPath,	path, "/data/opt/models",			u8"模型目录路径")
(string, resourceIP,ip,   "",							u8"资源IP(外网映射用)")
#ifdef USE_VIDEO_RECORD
(bool,   recordVideo,           record,  true,   u8"是否录制视频")
(int,    recordDuration,        record,	  60,	 u8"文件录制时长")
#endif// !USE_VIDEO_RECORD
#ifdef USE_SEARCH_MODULE
(int,   imageQuality,           search,   70,	 u8"图像存储的质量0-100")
(bool,  drawRects,              search,  false,  u8"截图是否绘制目标框")
#endif// !USE_SEARCH_MODULE
)

#define SETTINGS iva::Config::instance()
