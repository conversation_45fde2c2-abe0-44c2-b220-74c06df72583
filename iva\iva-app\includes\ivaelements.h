#pragma once
#ifndef NVIDIA
#include "bufsurface.h"
#include "ai_meta.h"
#include "gst_ai_meta.h"
#endif

#ifdef NVIDIA
#define GST_CAPS_FEATURE_DEVICE_MEMORY "memory:NVMM"
#define BufSurface NvBufSurface
#define FrameMeta NvDsFrameMeta
#define BatchMeta NvDsBatchMeta
#define ObjectMeta NvDsObjectMeta
#define UserMeta NvDsUserMeta
#define AiInferSegmentationMeta NvDsInferSegmentationMeta
#define AIINFER_SEGMENTATION_META NVDSINFER_SEGMENTATION_META
#define AIMetaType NvDsMetaType

#define AIMetaList NvDsMetaList
#define ObjectMetaList NvDsObjectMetaList

#define AIMetaCopyFunc NvDsMetaCopyFunc
#define AIMetaReleaseFunc NvDsMetaReleaseFunc
#define gst_buffer_get_batch_meta gst_buffer_get_nvds_batch_meta

#else
#define GST_CAPS_FEATURE_DEVICE_MEMORY "memory:mlu"
#endif

//NvBufSurface
