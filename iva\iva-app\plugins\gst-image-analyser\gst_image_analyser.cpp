
#include <string>
#include "log.h"
#include "gst_image_analyser.h"
#include "ivautils.h"
#include "ivaconfig.hpp"

#ifdef NVIDIA
#include <npp.h>
#include "gstnvdsinfer.h"
#endif

/// TODO 华为和寒武纪统一该头文件
#ifdef HUAWEI
#include "../../../platforms/npu/sources/infer/aiinfer/aiinfer.h"
#endif

using std::chrono::duration_cast;
using std::chrono::milliseconds;
#define MILLISECONDS_SINCE(start) duration_cast<milliseconds>(steady_clock::now() - start).count()

/* Define our element type. Standard GObject/GStreamer boilerplate stuff */
#define gst_imageanalyser_parent_class parent_class
G_DEFINE_TYPE(GstImageAnalyser, gst_imageanalyser, GST_TYPE_BASE_TRANSFORM);

static void gst_imageanalyser_set_property(GObject * object, guint prop_id, const GValue * value, GParamSpec * pspec);
static void gst_imageanalyser_get_property(GObject * object, guint prop_id, GValue * value, GParamSpec * pspec);
static gboolean gst_imageanalyser_set_caps(GstBaseTransform * btrans, GstCaps * incaps, GstCaps * outcaps);
static gboolean gst_imageanalyser_start(GstBaseTransform * btrans);
static gboolean gst_imageanalyser_stop(GstBaseTransform * btrans);
static GstFlowReturn gst_imageanalyser_transform_ip(GstBaseTransform *btrans, GstBuffer * inbuf);

/* Install properties, set sink and src pad capabilities, override the required
 * functions of the base class, These are common to all instances of the
 * element.
 */
static void gst_imageanalyser_class_init(GstImageAnalyserClass * klass)
{
	GObjectClass *gobject_class;
	GstElementClass *gstelement_class;
	GstBaseTransformClass *gstbasetransform_class;

	gobject_class = (GObjectClass *)klass;
	gstelement_class = (GstElementClass *)klass;
	gstbasetransform_class = (GstBaseTransformClass *)klass;

	/* Overide base class functions */
	gobject_class->set_property = GST_DEBUG_FUNCPTR(gst_imageanalyser_set_property);
	gobject_class->get_property = GST_DEBUG_FUNCPTR(gst_imageanalyser_get_property);
	gstbasetransform_class->set_caps = GST_DEBUG_FUNCPTR(gst_imageanalyser_set_caps);
	gstbasetransform_class->start = GST_DEBUG_FUNCPTR(gst_imageanalyser_start);
	gstbasetransform_class->stop = GST_DEBUG_FUNCPTR(gst_imageanalyser_stop);
	gstbasetransform_class->transform_ip = GST_DEBUG_FUNCPTR(gst_imageanalyser_transform_ip);

	/* Install properties */
	g_object_class_install_property(gobject_class, PROP_GPU_DEVICE_ID,
		g_param_spec_uint("gpu-id", "GPU device ID", "GPU device ID", 0, G_MAXUINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_PRINT_PROCESS_LOG,
		g_param_spec_boolean("print-log", "Print log enabled", "Print log enabled", true,
		(GParamFlags)(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS)));

	g_object_class_install_property(gobject_class, PROP_DETECTING,
		g_param_spec_int("detecting", "Channel detecting", "Channel detecting", 0, G_MAXINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_VIDEO_ID,
		g_param_spec_int("video-id", "Video ID in current channel", "Video ID in current channel", 0, G_MAXINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_PRESET_ID,
		g_param_spec_int("preset-id", "Preset ID in current channel", "Preset ID in current channel", 0, G_MAXINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	/* Set sink and src pad capabilities */
	gst_element_class_add_pad_template(gstelement_class, gst_static_pad_template_get(&gst_imageanalyser_src_template));
	gst_element_class_add_pad_template(gstelement_class, gst_static_pad_template_get(&gst_imageanalyser_sink_template));
	gst_element_class_set_details_simple(gstelement_class, "IVA image analyser", "IVA image analyser", "IVA image analyser", "AI team @ wtoe@ https://wtoe.cn");
}

static void gst_imageanalyser_init(GstImageAnalyser * imageanalyser)
{
	GstBaseTransform *btrans = GST_BASE_TRANSFORM(imageanalyser);
	gst_base_transform_set_in_place(GST_BASE_TRANSFORM(btrans), TRUE);
	gst_base_transform_set_passthrough(GST_BASE_TRANSFORM(btrans), TRUE);
	/* This quark is required to identify NvDsMeta when iterating through the buffer metadatas */
	//if (!_dsmeta_quark)
	//	_dsmeta_quark = g_quark_from_static_string(NVDS_META_STRING);

	/* Initialize all property variables to default values */
	imageanalyser->gpu_id = DEFAULT_GPU_ID;
	imageanalyser->print_process_log = TRUE;
	imageanalyser->frame_log_remain = FRAME_LOG_REMAIN;

	imageanalyser->video_id = -1;
	imageanalyser->preset_id = -1;

	imageanalyser->fps_frame_count_tmp = 0;
	imageanalyser->fps_start_time_stamp = 0;

	imageanalyser->debug_perf = std::getenv("DEBUG_GST_IMAGE") != NULL;
}

/* Function called when a property of the element is set. Standard boilerplate.
 */
static void gst_imageanalyser_set_property(GObject * object, guint prop_id, const GValue * value, GParamSpec * pspec)
{
	GstImageAnalyser *imageanalyser = GST_IMAGEANALYSER(object);
	switch (prop_id) {
	case PROP_GPU_DEVICE_ID:
		imageanalyser->gpu_id = g_value_get_uint(value);
		break;
	case PROP_PRINT_PROCESS_LOG:
		imageanalyser->print_process_log = g_value_get_boolean(value);
		break;
	case PROP_VIDEO_ID:
		imageanalyser->video_id = g_value_get_int(value);
		break;
	case PROP_PRESET_ID:
		imageanalyser->preset_id = g_value_get_int(value);
		break;
	case PROP_DETECTING:
		imageanalyser->detecting = (IVAChannelState)g_value_get_int(value);
		break;
	default:
		G_OBJECT_WARN_INVALID_PROPERTY_ID(object, prop_id, pspec);
		break;
	}
}

/* Function called when a property of the element is requested. Standard
 * boilerplate.
 */
static void gst_imageanalyser_get_property(GObject * object, guint prop_id, GValue * value, GParamSpec * pspec)
{
	GstImageAnalyser *imageanalyser = GST_IMAGEANALYSER(object);
	switch (prop_id) {
	case PROP_GPU_DEVICE_ID:
		g_value_set_uint(value, imageanalyser->gpu_id);
		break;
	case PROP_PRINT_PROCESS_LOG:
		g_value_set_boolean(value, imageanalyser->print_process_log);
		break;
	case PROP_VIDEO_ID:
		g_value_set_int(value, imageanalyser->video_id);
		break;
	case PROP_PRESET_ID:
		g_value_set_int(value, imageanalyser->preset_id);
		break;
	case PROP_DETECTING:
		g_value_set_int(value, imageanalyser->detecting);
		break;
	default:
		G_OBJECT_WARN_INVALID_PROPERTY_ID(object, prop_id, pspec);
		break;
	}
}

/**
 * Initialize all resources and start the output thread
 */
static gboolean gst_imageanalyser_start(GstBaseTransform * btrans)
{
	GstImageAnalyser *imageanalyser = GST_IMAGEANALYSER(btrans);
#ifdef NVIDIA
	CHECK_CUDA_STATUS(cudaSetDevice(imageanalyser->gpu_id),
		"Unable to set cuda device");
	CHECK_CUDA_STATUS(cudaStreamCreate(&imageanalyser->npp_stream),
		"Could not create cuda stream");
#endif // NVIDIA
	return TRUE;
#ifdef NVIDIA
error:
	return FALSE;
#endif // NVIDIA
}

/**
 * Stop the output thread and free up all the resources
 */
static gboolean gst_imageanalyser_stop(GstBaseTransform * btrans)
{
	GstImageAnalyser *imageanalyser = GST_IMAGEANALYSER(btrans);
#ifdef NVIDIA
	if (imageanalyser->inter_buf)
		NvBufSurfaceDestroy(imageanalyser->inter_buf);
	imageanalyser->inter_buf = NULL;
	if (imageanalyser->npp_stream)
	{
		cudaStreamDestroy(imageanalyser->npp_stream);
		imageanalyser->npp_stream = NULL;
	}

	GST_DEBUG_OBJECT(imageanalyser, "deleted CV Mat \n");
#endif
	return TRUE;
}

/**
 * Called when source / sink pad capabilities have been negotiated.
 */
static gboolean gst_imageanalyser_set_caps(GstBaseTransform * btrans, GstCaps * incaps, GstCaps * outcaps)
{
	GstImageAnalyser *imageanalyser = GST_IMAGEANALYSER(btrans);
	/* Save the input video information, since this will be required later. */
	gst_video_info_from_caps(&imageanalyser->video_info, incaps);
#ifdef NVIDIA
	CHECK_CUDA_STATUS(cudaSetDevice(imageanalyser->gpu_id), "Unable to set cuda device");

	if (imageanalyser->inter_buf)
		NvBufSurfaceDestroy(imageanalyser->inter_buf);
	imageanalyser->inter_buf = NULL;

	NvBufSurfaceCreateParams create_params;
	/* An intermediate buffer for NV12/RGBA to BGR conversion  will be
	 * required. Can be skipped if custom algorithm can work directly on NV12/RGBA. */
	create_params.gpuId = imageanalyser->gpu_id;
	create_params.width = imageanalyser->video_info.width;
	create_params.height = imageanalyser->video_info.height;
	create_params.size = 0;
	create_params.colorFormat = NVBUF_COLOR_FORMAT_RGBA;
	create_params.layout = NVBUF_LAYOUT_PITCH;
#ifdef __aarch64__
	create_params.memType = NVBUF_MEM_DEFAULT;
#else
	create_params.memType = NVBUF_MEM_CUDA_UNIFIED;
#endif

	if (NvBufSurfaceCreate(&imageanalyser->inter_buf, 1,
		&create_params) != 0) {
		GST_ERROR("Error: Could not allocate internal buffer for dsexample");
		goto error;
	}
#endif

	GST_DEBUG_OBJECT(imageanalyser, "created CV Mat\n");
	return TRUE;
#ifdef NVIDIA
error:

	if (imageanalyser->npp_stream) {
		cudaStreamDestroy(imageanalyser->npp_stream);
		imageanalyser->npp_stream = NULL;
	}
	return FALSE;
#endif
}

/**
 * Called when element recieves an input buffer from upstream element.
 */
static GstFlowReturn gst_imageanalyser_transform_ip(GstBaseTransform * btrans, GstBuffer * inbuf)
{
	GstImageAnalyser *imageanalyser = GST_IMAGEANALYSER(btrans);
	GstMapInfo in_map_info;
	GstFlowReturn flow_ret = GST_FLOW_ERROR;
	BufSurface *surface = NULL;
	BatchMeta *batch_meta = NULL;
	FrameMeta *ds_frame_meta = NULL;
	AIMetaList * l_frame = NULL;
	AIMetaList* l_user = NULL;
	AIMetaList* l_obj = NULL;

#ifdef NVIDIA
	CHECK_CUDA_STATUS(cudaSetDevice(imageanalyser->gpu_id), "Unable to set cuda device");
#endif

	memset(&in_map_info, 0, sizeof(in_map_info));
	if (!gst_buffer_map(inbuf, &in_map_info, GST_MAP_READ))
	{
		g_print("Error: Failed to map gst buffer\n");
		goto error;
	}
	surface = (BufSurface *)in_map_info.data;
#ifdef NVIDIA
	if (CHECK_NVDS_MEMORY_AND_GPUID(imageanalyser, surface))
	{
		goto error;
	}
#endif
	batch_meta = gst_buffer_get_batch_meta(inbuf);
	if (batch_meta == nullptr) {
		GST_ELEMENT_ERROR(imageanalyser, STREAM, FAILED, ("BatchMeta not found for input buffer."), (NULL));
		return GST_FLOW_ERROR;
	}

	// Standard way of iterating through buffer metadata
	for (l_frame = batch_meta->frame_meta_list; l_frame != NULL; l_frame = l_frame->next)
	{
		ds_frame_meta = (FrameMeta *)(l_frame->data);

		IVAFrameMeta* iva_frame_meta = new_user_meta();
		iva_frame_meta->detecting = imageanalyser->detecting;

		// TODO acquire_user_meta_from_pool接口实现
		// UserMeta* user_meta = nvds_acquire_user_meta_from_pool(batch_meta);
		UserMeta* user_meta = (UserMeta*)g_malloc0(sizeof(UserMeta));
		user_meta->user_meta_data = iva_frame_meta;
		user_meta->base_meta.meta_type = (AIMetaType)IVA_META_FRAME_INFO;
		user_meta->base_meta.copy_func = (AIMetaCopyFunc)copy_user_meta;
		user_meta->base_meta.release_func = (AIMetaReleaseFunc)release_user_meta;
		//nvds_add_user_meta_to_frame(ds_frame_meta, user_meta);
		ds_frame_meta->frame_user_meta_list = g_list_append(ds_frame_meta->frame_user_meta_list, user_meta);

		// 手工暂停 或 检测区暂停等 无需检测
		if (imageanalyser->detecting == IVA_CHANNEL_PAUSED_DEFAULT)
		{
			continue;
		}

		auto batch_id = ds_frame_meta->batch_id;
		auto stream_id = ds_frame_meta->source_id;
		imageanalyser->stream_id = stream_id;
		auto frame_num = ds_frame_meta->frame_num;

		cv::Mat frame_mat;
		guint channel_id = ds_frame_meta->source_id;
		float video_width = imageanalyser->video_info.width;
		float video_height = imageanalyser->video_info.height;

		// 记录帧率
		record_fps(imageanalyser);

#if defined(NVIDIA) || defined(HUAWEI)
        // 性能分析
        auto debug_perf_start = steady_clock::now();
        std::vector<int> perf_records;

        ia::FrameData frame_data;
        frame_data.width = video_width;
        frame_data.height = video_height;
        frame_data.channelID = channel_id;
        frame_data.frameIndex = ds_frame_meta->frame_num;

        // 解析 图像目标
        ObjectMetaList* obj_meta_list = ds_frame_meta->obj_meta_list;
        for (l_obj = ds_frame_meta->obj_meta_list; l_obj != NULL; l_obj = l_obj->next)
        {
            auto obj_meta = (ObjectMeta*)(l_obj->data);
            if (obj_meta->class_id < IVA_TARGET_TYPE_ROADBLOCK)
                continue;

            ia::ImageObject imageObject{ obj_meta->object_id,
                                         obj_meta->rect_params.left, obj_meta->rect_params.top,
                                         obj_meta->rect_params.width, obj_meta->rect_params.height,
                                         obj_meta->class_id, obj_meta->confidence};
            if (obj_meta->class_id == IVA_TARGET_TYPE_ROADBLOCK)
            {
                frame_data.inputObjects[ia::DetectorType::Roadblock].emplace_back(std::move(imageObject));
                //IVA_LOG_INFO("Channel:{}  IVA_TARGET_TYPE_ROADBLOCK at {} ", stream_id, ds_frame_meta->frame_num);
            }
            else if (obj_meta->class_id == IVA_TARGET_TYPE_FIRE || obj_meta->class_id == IVA_TARGET_TYPE_SMOKE)
            {
                frame_data.inputObjects[ia::DetectorType::Firesmoke].emplace_back(std::move(imageObject));
            }
#ifdef HUAWEI
            else if (obj_meta->class_id == IVA_TARGET_TYPE_THROWAYAY)
            {
                frame_data.inputObjects[ia::DetectorType::Roiobject].emplace_back(std::move(imageObject));
            }
#endif
        }

#ifdef HUAWEI
        for (int i = 0; i < sizeof(ds_frame_meta->misc_frame_info) / sizeof(ds_frame_meta->misc_frame_info[0]); ++i)
        {
            if (ds_frame_meta->misc_frame_info[i] == IVA_THROWAYAY_MODEL_UNIQUE_ID)
            {
                if (frame_data.inputObjects[ia::DetectorType::Roiobject].empty())
                {
                    frame_data.inputObjects[ia::DetectorType::Roiobject].emplace_back(ia::ImageObject{0, 0.0, 0.0, 0.0, 0.0, IVA_TARGET_TYPE_THROWAYAY, 0.0});
                }
                if (frame_data.inputObjects[ia::DetectorType::Roadblock].empty())
                {
                    frame_data.inputObjects[ia::DetectorType::Roadblock].emplace_back(ia::ImageObject{0, 0.0, 0.0, 0.0, 0.0, IVA_TARGET_TYPE_ROADBLOCK, 0.0});
                }
            }
//            if (ds_frame_meta->misc_frame_info[i] == IVA_FIRE_SMOKE_MODEL_UNIQUE_ID)
//            {
//                if (frame_data.inputObjects[ia::DetectorType::Firesmoke].empty())
//                {
//                    frame_data.inputObjects[ia::DetectorType::Firesmoke].emplace_back(ia::ImageObject{0, 0.0, 0.0, 0.0, 0.0, IVA_TARGET_TYPE_LIGHT, 0.0});
//                }
//            }
        }
#endif
		// 车道线
		for (l_user = ds_frame_meta->frame_user_meta_list; l_user != NULL; l_user = l_user->next)
		{
			UserMeta* user_meta = (UserMeta*)(l_user->data);
			if (user_meta->base_meta.meta_type == AIINFER_SEGMENTATION_META)
			{
                AiInferSegmentationMeta* segmeta = (AiInferSegmentationMeta*)(user_meta->user_meta_data);
				std::vector<float> temp(segmeta->width * segmeta->height);
				for (size_t i = 0; i < temp.size(); i++)
				{
					temp[i] = segmeta->class_map[i];
				}
				cv::Mat binary = cv::Mat::zeros(cv::Size(segmeta->width, segmeta->height), CV_8UC1);
				for (int x=0; x < segmeta->width; x++)
				{
					for (int y= 0; y< segmeta->height; y++)
					{
						int index = y * segmeta->width + x;
						gint klass = segmeta->class_map[index];
#ifdef HUAWEI
                        gfloat prob = (float) reinterpret_cast<float16_t *>(segmeta->class_probabilities_map)[index];
#else
                        gfloat prob = segmeta->class_probabilities_map[index];
#endif
						if(prob > 0.0)
							binary.at<uchar>(y, x) = prob*255;
					}
				}
				frame_data.lanes = binary;
				break;
			}
		}
#endif //NVIDIA
		
#ifdef ENABLE_IMAGE_ANALYSER
		// 性能分析 （获取数据）
		perf_records.push_back(MILLISECONDS_SINCE(debug_perf_start));
		auto image_channel = ia::getChannel(stream_id, imageanalyser->gpu_id);
		if (image_channel->needFrameMat())
		{
			// IVA_LOG_INFO("Channel:{}  feed frame mat at {} " , stream_id, ds_frame_meta->frame_num);
			capture_frame(imageanalyser, frame_mat, surface, ds_frame_meta->batch_id);
			frame_data.frame = frame_mat;
		}

		// 性能分析 （截图）
		perf_records.push_back(MILLISECONDS_SINCE(debug_perf_start));

		ia::OutputData output = image_channel->process(frame_data);
		generate_targets_meta(output, iva_frame_meta->firesmokes, ia::DetectorType::Firesmoke);
		generate_targets_meta(output, iva_frame_meta->roadblocks, ia::DetectorType::Roadblock);
		generate_targets_meta(output, iva_frame_meta->roiobjects, ia::DetectorType::Roiobject);

		iva_frame_meta->shifting = output.frameState[ia::DetectorType::Offset];

		// 性能分析 （检测）
		perf_records.push_back(MILLISECONDS_SINCE(debug_perf_start));

		if (imageanalyser->debug_perf)
		{
			if (perf_records.back() > 30/*ms*/ && perf_records.size() >= 3)
			{
				IVA_LOG_WARN("Channel:{} [gst imageanalyser] perf: {}:{}:{}", stream_id, perf_records[0], perf_records[1], perf_records[2]);
			}
		}
#endif
		// 打印 插件状态
		imageanalyser->frame_log_remain--;
		if (imageanalyser->frame_log_remain <= 0)
		{
			imageanalyser->frame_log_remain = FRAME_LOG_REMAIN;
		}
	}

	flow_ret = GST_FLOW_OK;
error:
	gst_buffer_unmap(inbuf, &in_map_info);
	return flow_ret;
}

/*
* 获取当前帧图像数据
* @param imageanalyser
* @param output  输出opencv Mat数据
* @param surface  NvBufSurface
* @param batch_id  batch序号
*/
static bool capture_frame(GstImageAnalyser* imageanalyser, cv::Mat& output, BufSurface* surface, int batch_id)
{
#ifdef NVIDIA
	//Memset the memory
	NvBufSurfaceMemSet(imageanalyser->inter_buf, 0, 0, 0);

	NvBufSurfTransformRect src_rect = { 0, 0, imageanalyser->video_info.width, imageanalyser->video_info.height };
	NvBufSurfTransformRect dst_rect = { 0, 0, imageanalyser->video_info.width, imageanalyser->video_info.height };

	NvBufSurfTransformParams transform_params;
	// Set the transform parameters
	transform_params.src_rect = &src_rect;
	transform_params.dst_rect = &dst_rect;

	// Transformation scaling+format conversion if any.
	NvBufSurfTransform_Error err = NvBufSurfTransform(surface, imageanalyser->inter_buf, &transform_params);
	if (err != NvBufSurfTransformError_Success) {
		GST_ELEMENT_ERROR(imageanalyser, STREAM, FAILED,
			("NvBufSurfTransform failed with error %d while converting buffer", err),
			(NULL));
		return false;
	}

	// Map the buffer so that it can be accessed by CPU
	if (NvBufSurfaceMap(imageanalyser->inter_buf, 0, 0, NVBUF_MAP_READ) != 0) {
		return false;
	}

	// Cache the mapped data for CPU access
	NvBufSurfaceSyncForCpu(imageanalyser->inter_buf, 0, 0);
	cv::Mat in_mat =
		cv::Mat(imageanalyser->video_info.height, imageanalyser->video_info.width,
			CV_8UC4, imageanalyser->inter_buf->surfaceList[batch_id].mappedAddr.addr[0],
			imageanalyser->inter_buf->surfaceList[batch_id].pitch);

#if (CV_MAJOR_VERSION >= 4)
	cv::cvtColor(in_mat, output, cv::COLOR_RGBA2BGR);
#else
	cv::cvtColor(in_mat, output, CV_RGBA2BGR);
#endif

	if (NvBufSurfaceUnMap(imageanalyser->inter_buf, 0, 0)) {
		return false;
	}
#endif
	return true;
}

/*
* 记录帧率
* @param imageanalyser
*/
static void record_fps(GstImageAnalyser* imageanalyser)
{
	if (imageanalyser->fps_start_time_stamp == 0)
	{
		imageanalyser->fps_start_time_stamp = get_system_timestamp_of_usec();
	}
	else
	{
		imageanalyser->fps_frame_count_tmp++;
		if (imageanalyser->fps_frame_count_tmp >= FPS_FRAME_COUNT)
		{
			long time_now = get_system_timestamp_of_usec();
			long time = time_now - imageanalyser->fps_start_time_stamp;
			double fps = imageanalyser->fps_frame_count_tmp / (time / 1000000.0);

			if (imageanalyser->debug_perf || (isLogEnabled(SHOW_IVA_FPS) && imageanalyser->print_process_log))
			{
				IVA_LOG_INFO("CHANNEL {} [imageanalyser] FPS:{}, total{} in {} usec",
					imageanalyser->stream_id,
					std::to_string(fps).c_str(),
					imageanalyser->fps_frame_count_tmp,
					std::to_string(time).c_str());
			}
			imageanalyser->fps_frame_count_tmp = 0;
			imageanalyser->fps_start_time_stamp = 0;
		}
	}
}

#ifdef ENABLE_IMAGE_ANALYSER
/*
* 生成图像目标 meta数据
* @param output 图像模块输出结果
* @param metalist  待生成的IVAFrameMeta的IVATargetArray对象
* @param type 检测器类型
*/
static void generate_targets_meta(ia::OutputData& output, IVATargetArray*& metalist, ia::DetectorType type)
{
	if (output.outputObjects.find(type) == output.outputObjects.end())
		return;

	ia::ImageObjectList& img_objects = output.outputObjects[type];
	if (img_objects.size() > 0)
	{
		metalist = (IVATargetArray*)g_malloc0(sizeof(IVATargetArray));
		metalist->len = img_objects.size();
		metalist->targets = (IVATarget*)g_malloc0(sizeof(IVATarget) * metalist->len);
		for (int i = 0; i < metalist->len; ++i)
		{
			metalist->targets[i].class_id = img_objects[i].klass;
			metalist->targets[i].target_id = img_objects[i].id;
			metalist->targets[i].x = img_objects[i].x;
			metalist->targets[i].y = img_objects[i].y;
			metalist->targets[i].width = img_objects[i].width;
			metalist->targets[i].height = img_objects[i].height;
		}
	}
}
#endif // ENABLE_IMAGE_ANALYSER

/**
 * Boiler plate for registering a plugin and an element.
 */
static gboolean imageanalyser_plugin_init(GstPlugin * plugin)
{
	GST_DEBUG_CATEGORY_INIT(gst_imageanalyser_debug, "imageanalyser", 0,
		"image analyser");

	return gst_element_register(plugin, "imageanalyser", GST_RANK_PRIMARY,
		GST_TYPE_IMAGEANALYSER);
}
GST_PLUGIN_DEFINE(GST_VERSION_MAJOR, GST_VERSION_MINOR, imageanalyser, DESCRIPTION, imageanalyser_plugin_init, "3.3", LICENSE, BINARY_PACKAGE, URL)


