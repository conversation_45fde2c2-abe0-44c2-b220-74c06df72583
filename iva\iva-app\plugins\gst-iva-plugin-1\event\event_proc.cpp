#include <future>
#include "event_proc.h"
#include "ivautils.h"
#include "ivaconfig.hpp"
#include "log.h"
#include "protocol_manager.h"
#include "protocol_sender.h"
#include "protocol_utility.h"
#include "opencv_frame_osd.h"
#include "opencv2/imgcodecs.hpp"

using namespace iva;
using namespace std;
using namespace network;

static auto BOX_RED_COLOR  = cv::Scalar(0, 0, 255);
static auto BOX_CRAY_COLOR = cv::Scalar(255, 255, 0);

void saveImgTask( int channelId, cv::Mat img, Area_t eventArea,
                  Area_t multiArea, const std::string& imgPath, const std::string& eventName)
{
    ai::CvFrameOsd cvFrameOsd;
    if (SETTINGS->usingCustomOsd())
    {
        auto fontPath = SETTINGS->fontPath();
        cvFrameOsd.init(fontPath);
    }

	int thickness = img.rows > 1000 ? 2 : 1;

#ifdef CAMBRICON
    thickness = 2;
#endif

	int jpegQuality = img.rows > 1000 ? 70 : 90;

    //下面是报事件的目标矩形框，可能有多个
    bool bDrawObj = false;
    auto sz = multiArea.vtPoint.size();
    if (sz > 0 && sz % 2 == 0)
    {
        int subNum = (int)sz / 2;
        for (int i = 0; i < subNum; i++)
        {
            int leftX = (int)multiArea.vtPoint[i * 2].x;
            int leftY = (int)multiArea.vtPoint[i * 2].y;
            int boxWidth = (int)multiArea.vtPoint[i * 2 + 1].x - leftX;
            int boxHeight = (int)multiArea.vtPoint[i * 2 + 1].y - leftY;

            cv::Rect rt;
            const int padding = 3;
            rt.x = std::max(int(leftX - padding), 0);
            rt.y = std::max(int(leftY - padding), 0);
            rt.width = std::min(int((boxWidth + 2 * padding)), img.cols - rt.x);
            rt.height = std::min(int((boxHeight + 2 * padding)), img.rows - rt.y);

            if (SETTINGS->usingCustomOsd())
            {
                cvFrameOsd.drawTargets(img, rt, eventName);
            }
            else
            {
                cv::rectangle(img, rt, BOX_RED_COLOR, thickness);
            }

        }
        bDrawObj = true;
    }
    //如果已经画了目标框，下面就是画所在的范围，用普通颜色，否则表示拥堵，要画红色
    cv::Scalar polyColor = (bDrawObj) ? BOX_CRAY_COLOR : BOX_RED_COLOR;
    //画目标的多边形
    sz = eventArea.vtPoint.size();
    if (sz > 0)
    {
        std::vector<cv::Point> vtPoint;
        for (size_t i = 0; i < sz; i++)
        {
            cv::Point pt((int)eventArea.vtPoint[i].x, (int)eventArea.vtPoint[i].y);
            vtPoint.push_back(pt);
        }
        vtPoint.push_back(vtPoint[0]);
        //画线
        for (size_t i = 0; i < sz; i++)
        {
            cv::Point p1(vtPoint[i].x, vtPoint[i].y);
            cv::Point p2(vtPoint[i + 1].x, vtPoint[i + 1].y);
            cv::line(img, p1, p2, polyColor, thickness, cv::LINE_AA);
        }
    }

    try {
		cv::imwrite(imgPath, img, { cv::IMWRITE_JPEG_QUALITY, jpegQuality });
    }
    catch (const cv::Exception& e)
    {
        cout << "[plugin1] Channel:"<< channelId << "eventimg save error: " << e.what();
    }
}

void saveEventImg(cv::Mat mat, Area_t eventArea,  Area_t multiArea,
                  int channelId, const std::string& imgPath, const std::string& eventName)
{
    auto future = std::async(std::launch::async,[=](){
        saveImgTask(channelId, mat, eventArea, multiArea, imgPath, eventName);
    });
}


/**
 *  更新事件 截图和上传
 */
void updateEvents(int channelID, std::vector<evt::EventInfo>* events, cv::Mat mat, int framenum, float scale_x, float scale_y)
{
    if (!events || events->empty())
		return;

	std::string eventImgPath = "eventimg";
	std::string eventVideoPath = "eventvideo";

	//创建当天的图片和视频路径
	std::string szDate = get_system_short_time_str(get_system_timestamp());
	std::string szPath = std::string(WEB_SERVER_ROOT) + "/" + eventImgPath + "/" + szDate + "/";
    protocol::createPath(szPath);
	szPath = std::string(WEB_SERVER_ROOT) + "/" + eventVideoPath + "/" + szDate + "/";
    protocol::createPath(szPath);

	for (guint i = 0; i < events->size(); ++i)
	{
		bool needClone = events->size() > 1 && i < events->size() -1;

		evt::EventInfo& evt = events->at(i);
		std::string evtNum = (events->size() > 1) ? ("_" + std::to_string(i + 1)) : "";

        stringstream fileName;
		if (evt.removeTime > 0)
		{
			std::string imagePath;
			if (evt.type == evt::EventType_Jam || evt.type == evt::EventType_RoadConstruction ) //拥堵解除时要存图片
			{
				//! 事件区域
                Area_t multiArea;
                Area_t eventArea;
				int eventAreaPointsNum = evt.occurArea.pointCount();
				for (int k = 0; k < eventAreaPointsNum; ++k)
				{
                    Point pt(evt.occurArea.getPoint(k).x / scale_x, evt.occurArea.getPoint(k).y / scale_y);
					eventArea.vtPoint.emplace_back(std::move(pt));
				}

				//! 画框存图
                fileName << szDate << "/" << get_system_simple_time_str(evt.removeTime) << "_" << channelID + 1 << "_" << evt.type << "_" << framenum % 1000 << evtNum;
				auto eventName = getEventTypeName(evt.type);
                imagePath = eventImgPath + "/" + fileName.str() + ".jpg";
                std::string szFullPath = std::string(WEB_SERVER_ROOT) + "/" + imagePath;
                saveEventImg(needClone ? mat.clone(): mat, eventArea, multiArea, channelID, szFullPath, eventName);
			}

            //! 上报事件
            protocol::postEventRemove(evt.id, get_system_full_time_str(evt.removeTime), evt.type, imagePath);
		}
		else
		{
            //! 事件区域
			bool bDrawObj = false;//有目标框,画目标框，后期可能会是多个目标框，待扩展 todo
            Area_t multiArea, drawMultiArea;
			if ( evt.occurRect.isValid() )
			{
				auto& occurRect = evt.occurRect;
				int occurAreaPointsNum = 2;
				multiArea.vtPoint.emplace_back(occurRect.getLeft() / scale_x, occurRect.getTop() / scale_y);
				multiArea.vtPoint.emplace_back(occurRect.getRight() / scale_x, occurRect.getBottom() / scale_y);

				bDrawObj = true;
			}

			drawMultiArea.vtPoint = multiArea.vtPoint;
            if ((evt.type == evt::EventType_RoadConstruction && SETTINGS->drawConstruct())
                || (evt.type == evt::EventType_Pedstrain && SETTINGS->drawAllPedstrain())
                || (evt.type == evt::EventType_Obstacle)
                || (evt.type == evt::EventType_RoadBlock)
                || (evt.type == evt::EventType_FireSmoke) )
            {
                for (auto& rt : evt.extraRects)
                {
					drawMultiArea.vtPoint.emplace_back(rt.getLeft() / scale_x, rt.getTop() / scale_y);
					drawMultiArea.vtPoint.emplace_back(rt.getRight() / scale_x, rt.getBottom() / scale_y);
                }
            }

            Area_t eventArea;	//若没有目标框，或者配置中要画ROI，则画区域
			if (!bDrawObj || SETTINGS->drawEventROI())
			{
				int eventAreaPointsNum = evt.occurArea.pointCount();
				for (int k = 0; k < eventAreaPointsNum; ++k)
				{
                    Point pt(evt.occurArea.getPoint(k).x / scale_x, evt.occurArea.getPoint(k).y / scale_y);
					eventArea.vtPoint.emplace_back(std::move(pt));
				}
			}

            //! 画框存图
            fileName << szDate << "/" << get_system_simple_time_str(evt.occurTime) << "_" << channelID + 1 << "_" << evt.type << "_" << framenum % 1000 << evtNum;
            std::string imagePath = eventImgPath + "/" + fileName.str() + ".jpg";
            std::string videoPath = eventVideoPath + "/" + fileName.str() + ".mp4";
			std::string szFullPath = std::string(WEB_SERVER_ROOT) + "/" + imagePath;
			auto eventName = getEventTypeName(evt.type);
            saveEventImg(needClone ? mat.clone(): mat, eventArea, drawMultiArea, channelID, szFullPath, eventName);

            //! 上报事件
            protocol::postEvent(channelID, videoPath, imagePath, network::ALARM, multiArea, evt);

			IVA_LOG_INFO("[plugin1]has event {}/{} channel {}, event id {}, event type {} roi {} lane {} region {}",
				i+1, events->size(), channelID, evt.id, evt.type, evt.roiID, evt.laneID, evt.regionID);
		}
	}
}

/**
 * 获取事件类型的中文名字
 * @param[in] type 事件类型枚举
 * @return 	  事件类型的中文名字
 */
std::string getEventTypeName(evt::EventType type)
{
	switch (type)
	{
	case evt::EventType_None:
		return "";
	case evt::EventType_Stop:
		return "停车";
	case evt::EventType_Opposite:
		return "逆行";
	case evt::EventType_Jam:
		return "拥堵";
	case evt::EventType_DriveIn:
		return "驶入";
	case evt::EventType_DriveAcross:
		return "变道";
	case evt::EventType_DriveAway:
		return "驶离";
	case evt::EventType_Pedstrain:
		return "行人";
	case evt::EventType_TwoWheels:
		return "摩托车";
	case evt::EventType_Obstacle:
		return "抛洒物";
	case evt::EventType_RoadBlock:
		return "路障";
	case evt::EventType_RoadConstruction:
		return "施工";
	case evt::EventType_FireSmoke:
		return "烟火";
	default:
		return "";
	}
}

/**
 *  目标过线回调
 */
void onTargetPassedCallback(evt::TargetInfoList& targets)
{
	//IVA_LOG_INFO("[plugin2]onTargetPassedCallback: target size {} ", targets.size() );
	std::vector<Trackinfo> vtTrack;
	for (auto& t : targets)
	{
        Trackinfo track;
		track.trackId = t.id;
		track.roiId = t.roiID;
		track.laneId = t.laneID;
		track.mainLabel = t.type;
		track.trackSpeed = (float)t.speed;
		track.timeBegin = get_system_full_time_str(t.createTime);
		track.timeEnd = get_system_full_time_str(t.leaveTime);
		vtTrack.emplace_back(std::move(track));
	}
    protocol::postTrack(vtTrack);
}

void onEventWithdrawCallback(evt::EventInfo ei)
{
	std::string szOccur = get_system_full_time_str(ei.occurTime);
    protocol::postEventWithdraw(ei.id, ei.type, szOccur.substr(11,szOccur.length() ) );
}
