#pragma once

#ifdef NVIDI<PERSON>
#include "nvbufsurface.h"
#include "nvbufsurftransform.h"
#include "gst-nvquery.h"
#include "gstnvdsmeta.h"
#include "nvll_osd_struct.h"
#include "nvll_osd_api.h"
#else
#include "ivaelements.h"
#include "ai_osd_api.h"
#endif //NVIDIA

#include "event_analyser.h"
#include "ivameta.h"

#ifdef NVIDIA
#define OSD_LineParams NvOSD_LineParams
#define OSD_FrameLineParams NvOSD_FrameLineParams
#define OSD_RectParams NvOSD_RectParams
#define OSD_FrameRectParams NvOSD_FrameRectParams
#define OSD_TextParams NvOSD_TextParams
#define OSD_FrameTextParams NvOSD_FrameTextParams

#define OSD_ColorParams NvOSD_ColorParams

#define ai_osd_draw_lines nvll_osd_draw_lines
#define ai_osd_draw_rectangles nvll_osd_draw_rectangles
#define ai_osd_put_text nvll_osd_put_text

#define ai_osd_create_context nvll_osd_create_context
#define ai_osd_destroy_context nvll_osd_destroy_context
#endif //NVIDIA

#define MAX_OSD_ELEMS 1000

/*
*  结构化页面 绘制参数
*/
struct OSDParam
{
	OSD_LineParams *line_params;
	OSD_FrameLineParams *frame_line_params;

	OSD_RectParams *rect_params;
	OSD_FrameRectParams *frame_rect_params;

	OSD_TextParams *text_params;
	OSD_FrameTextParams *frame_text_params;

	gchar* font_name;

	void init();
	void destroy();
};

struct OSDFrameInfo{
	int surfindex;
	float scale_x;
	float scale_y;
	bool show_track;
	int frame_num;
	IVAChannelState state;
};

/*
* 获取事件状态颜色
*/
OSD_ColorParams getEventStateColor(evt::EventState evt_state, double alpha = 1.0);

/*
*  结构化页面 绘制图像
*/
gboolean modify_frame(BufSurface* surface, void * nvosd_ctx, evt::TargetInfoList& targets, evt::EventInfoList& jam_infos,
	evt::EventObjectList& evt_objects, OSDParam& param, const OSDFrameInfo& frame_info);
