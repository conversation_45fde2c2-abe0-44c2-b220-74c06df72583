/**
 * Project IVA
 */
#pragma once
#ifdef USE_VIDEO_RECORD

#include <gst-nvdssr.h>
#include <chrono>
#include <deque>
#include <shared_mutex>
#include <atomic>
#include "event_analyser.h"
#include "ivautils.h"
#include "protocol_utility.h"
#include "opencv2/core.hpp"

namespace record
{
    using CompleteCallback = std::function<void(guint)>;
    constexpr int CACHE_SIZE_SEC = 15;                   //!< 录像预缓存大小，单位 秒
    constexpr int SMART_REC_DEFAULT_DURATION = 20;        //!< 录像时长

    /**
     * 过线目标信息
     */
    struct PassedTargetInfo
    {
        std::chrono::system_clock::time_point startTime;   //!< 目标过线时间
        evt::TargetInfoList targets;                       //!< 过线目标
        cv::Mat frame;                                     //!< 过线目标所在的帧
    };

    using RecordCallback = std::function<void(std::string, int, const PassedTargetInfo& targetInfo)>;

    /**
     * 过线目标视频录制任务
     */
    struct VideoRecordTask
    {
        PassedTargetInfo start;                            //!< 开启一段新录像时的过线目标
        std::deque<PassedTargetInfo> passedTargets;        //!< “只上报”过线目标，即在该录制视频时间区间内的过线目标，这些目标不用录像，只记录在录像的时间偏移点，并上报
    };

    /**
     * 视频录制相关属性
     */
    class VideoRecord
    {
    public:
        explicit VideoRecord(guint ch, guint before,guint after):channel(ch),beforeTime(before),afterTime(after) {};

        /**
         * 创建录像context, 设置录像参数
         * @param params
         */
        void create(NvDsSRInitParams params);

        /**
         * 销毁录像context
         */
        void destroy();

        /**
         * 开启录像
         * @param[in]  before     相对于当前时刻之前的时间
         * @param[in]  duration   录制持续时长
         * @param[out] position   如果是过线录像任务，则表示过线目标在当前录像的时间位置。如果只是单纯录像，该参数可忽略
         * @param[in]  task       过线录像任务，如果只是单纯录像，该参数可忽略
         * @return     成功开启录像任务，则返回true, 否则 false
         */
        bool start(guint before, guint duration, int& position, const std::optional<VideoRecordTask>& task = std::nullopt);

        /**
         * 停止录像
         */
        bool stop();

        /**
         * 提交录像任务到任务队列
         * @param info       过线信息
         * @param position   如果当前录像正在录像，且录像任务的起始时间在该录像时长内，则之间返回当前目标相对于录像文件的时间位置
         * @return 正在录像则返回true，将过线信息提交到任务队列中；如果当前需要开启新录像，则返回false
         */
        bool submit(const PassedTargetInfo& info);

        /**
         * 返回录像是否完成，即是否空闲，可开启新录像
         * @note 录像接口不支持并发，必须等到录像完成且调用完成回调后，才能开启新录像
         */
        bool isCompleted();

        /**
         * 设置录像完成标志位，并调用用户的录像完成回调
         */
        void setCompleted();

        /**
         * 注册用户的录像完成回调函数
         */
        void registerCompleteCallback(const CompleteCallback& func);


        void registerRecordCallback(RecordCallback func);

        /**
         * 录像完成回调，写日志，调用用户注册的回调函数
         */
        static gpointer onComplete(NvDsSRRecordingInfo * info, gpointer userData);

    public:
        inline guint getChanel() {return channel;};
        inline void  setChanel(guint ch) { channel = ch;};
        inline guint getBeforeTime(){return beforeTime;};
        inline VideoRecordTask getRecordInfo(){return currentRecord;};
        inline std::string getFileName(){return fileName;};
        inline GstElement* getRecordBin(){return (recordContext != nullptr) ? recordContext->recordbin : nullptr;};
    private:

        guint channel = 0;                                  //!< 通道号
        guint beforeTime = 2;                               //!< 相对于当前时刻之前的时间
        guint afterTime = 3;                                //!< 当前目标时刻后继续录制时间
        guint duration   = 60;                              //!< 录制持续时长 （录像文件的总时长 = beforeTime + duration）
        std::string fileName;                               //!< 视频录制的文件名，包括路径

        std::mutex recordLock;                              //!< 录像锁
        std::atomic_bool completed = true;                  //!< 录像完成回调
        VideoRecordTask  currentRecord;                     //!< 该通道当前正在录制的任务
        NvDsSRContext*   recordContext = nullptr;           //!< 视频录制Context

        CompleteCallback completeCallback;                  //!< 用户录像完成回调

        std::vector<VideoRecordTask> videoRecordTasks;      //!< 通道对应的录像任务

        RecordCallback onRecordCallback;                    //!< 录像开始回调，开启录像任务后，返回录像相关的信息

    };

    /**
     * 创建对应通道的录像context
     * @param[in] index   pipeline序号
     * @param[in] params  录像配置参数
     */
    void create(guint index, guint before, guint after, const std::string& relativePath, NvDsSRInitParams params);

    /**
     * 获取对应通道的录像context
     * @param[in] index       通道号
     */
    std::shared_ptr<VideoRecord> getVideoRecord(guint index);

}
#endif // USE_VIDEO_RECORD
