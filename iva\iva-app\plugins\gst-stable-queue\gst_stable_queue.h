
#ifndef __GST_STABLEQUEUE_H__
#define __GST_STABLEQUEUE_H__

#include <gst/base/gstbasetransform.h>
#include <gst/video/video.h>
#include <chrono>
#include <queue>
#include <mutex>

/* Package and library details required for plugin_init */
#define PACKAGE "stablequeue"
#define VERSION "1.0"
#define LICENSE "Proprietary"
#define DESCRIPTION "stable queue"
#define BINARY_PACKAGE "stable queue"
#define URL "http://wtoe.cn/"

G_BEGIN_DECLS
/* Standard boilerplate stuff */
typedef struct _GstStableQueue GstStableQueue;
typedef struct _GstStableQueueClass GstStableQueueClass;
/* Standard boilerplate stuff */
GType gst_stablequeue_get_type(void);
#define GST_TYPE_STABLEQUEUE (gst_stablequeue_get_type())
#define GST_STABLEQUEUE(obj) (G_TYPE_CHECK_INSTANCE_CAST((obj),GST_TYPE_STABLEQUEUE,GstStableQueue))
#define GST_STABLEQUEUE_CLASS(klass) (G_TYPE_CHECK_CLASS_CAST((klass),GST_TYPE_STABLEQUEUE,GstStableQueueClass))
#define GST_STABLEQUEUE_GET_CLASS(obj) (G_TYPE_INSTANCE_GET_CLASS((obj), GST_TYPE_STABLEQUEUE, GstStableQueueClass))
#define GST_IS_STABLEQUEUE(obj) (G_TYPE_CHECK_INSTANCE_TYPE((obj),GST_TYPE_STABLEQUEUE))
#define GST_IS_STABLEQUEUE_CLASS(klass) (G_TYPE_CHECK_CLASS_TYPE((klass),GST_TYPE_STABLEQUEUE))
#define GST_STABLEQUEUE_CAST(obj)  ((GstStableQueue *)(obj))
GST_DEBUG_CATEGORY_STATIC(gst_stablequeue_debug);
#define GST_CAT_DEFAULT gst_stablequeue_debug

static GstStaticPadTemplate gst_stablequeue_sink_template =
GST_STATIC_PAD_TEMPLATE ("sink",
                                GST_PAD_SINK,
                                GST_PAD_ALWAYS,
                                GST_STATIC_CAPS ("ANY"));

static GstStaticPadTemplate gst_stablequeue_src_template =
GST_STATIC_PAD_TEMPLATE ("src",
                            GST_PAD_SRC,
                            GST_PAD_ALWAYS,
                            GST_STATIC_CAPS ("ANY")
);

#define DEFAULT_MAX_QUEUE_CACHED_SIZE 200
#define DEFAULT_SMOOTH_QUEUE_CACHED_SIZE 100
#define DEFAULT_MIN_SLEEP_MS_EVERY_FRAME 30

/* Enum to identify properties */
enum
{
	PROP_0,
	PROP_MAX_QUEUE_CACHED_SIZE,
	PROP_SMOOTH_QUEUE_CACHED_SIZE,
	PROP_MIN_SLEEP_MS_EVERY_FRAME,
};

using std::chrono::steady_clock;
// Boiler plate stuff
struct _GstStableQueueClass
{
	GstBaseTransformClass parent_class;
};

struct _GstStableQueue
{
	GstBaseTransform base_trans;

	gint max_queue_cached_size;
	gint smooth_queue_cached_size;
	gint min_sleep_ms_every_frame;

	GThread* output_thread;
	std::mutex mutex_out;
	bool running;

	std::queue<GstBuffer*>* cached;
};


G_END_DECLS
#endif /* __GST_STABLEQUEUE_H__ */
