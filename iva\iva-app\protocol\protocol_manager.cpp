/**
 * Project IVA
 */
#include <future>
#include "protocol_manager.h"
#include "protocol.hpp"
#include "network.h"
#include "util/json_util.h"
#include "protocol_utility.h"
#include "ivaconfig.hpp"

/**
 * @brief: 协议接受接口
 */
namespace iva::protocol
{
    using namespace network;
    #define MSG_RECEIVED(SIGNAL) \
    auto future = std::async([this]() { \
        SIGNAL(); \
    });

    #define MSG_DATA_RECEIVED(SIGNAL, PROTOCOL, DATA) \
    { \
        auto msg = deserialize<PROTOCOL>(DATA);       \
        auto future = std::async([this, msg]() {SIGNAL(msg);}); \
    } \


    /**
     * 初始化（启动服务端及相关客户端）
     * @param processID 进程号
     */
    bool ProtocolManager::init(int processID)
    {
        if (initUdpSession)
            return true;
        // 初始化IVA 监听服务
        int receivePort = (processID -1) + UDP_START_LISTEN_PORT;
        bool rt = initServer(receivePort, [this](auto &data) { handleProtocolReceived(data); });
        if(!rt)
        {
            IVA_LOG_ERROR("FVM server start failed! port {}, err {} code {}",receivePort, lastErrorMsg(), lastErrorCode());
        }

        // 初始化客户端列表
        network::initClients({
            {SessionType::FVM, 5000, DEFAULT_CLIENT_ID, HOST_LOCAL,    ConnectionType::UDP },
            {SessionType::WEB, 8080, WEB_LOCAL,         HOST_LOCAL,    ConnectionType::HTTP}
            });

        initUdpSession = rt;
        return rt;
    }

	/**
	 * 销毁
	 */
	void ProtocolManager::dispose()
	{
		network::stopServers();
	}

    /**
     * 设置ip相关参数
     */
    void ProtocolManager::setSystemIp(const std::string& localIp, const std::string& platIp)
    {
        this->localIP = localIp;
        this->platIP = platIp;
		std::string resIP = SETTINGS->resourceIP();
        this->webURL = "http://" + (resIP.empty() ? localIp : resIP) + ":8080/";
        initWEBClient();
    }

    /**
     * 初始化web客户端
     */
    void ProtocolManager::initWEBClient()
    {
        if (!initWebClient)
        {
            initWebClient = true;
            addSessionTarget(SessionTarget{SessionType::WEB, 8080, WEB_PLATFORM, platIP, ConnectionType::HTTP});
        }
    }

    /**
     * 消息数据处理
     * @param msgData 消息内容
     */
    void ProtocolManager::handleProtocolReceived(std::string &msgData)
    {
        IVA_LOG_INFO("RECV {}", msgData);
        auto[msgType, msgJson] = parseProtocol(msgData);
        switch (msgType)
        {
            case ProtocolType::UDP_RESET_CHANNEL:      //FVM->IVA 重置通道
                MSG_DATA_RECEIVED(onResetChannel, ResetChannel, msgJson);
                break;
            case ProtocolType::UDP_SYSTEM_CONFIG:      //FVM->IVA 系统配置
                MSG_DATA_RECEIVED(onSystemConfig, SystemConfig, msgJson);
                break;
            case ProtocolType::UDP_ALGORITHM_PARAM:    //FVM->IVA 灵敏度参数
            {   //! 兼容旧版本协议 添加vtParam
                auto vtParmJson = network::util::addJsonArrayName(msgJson, "vtParam");
                MSG_DATA_RECEIVED(onAlgorithmParam, AlgorithmParam, vtParmJson);
            }
                break;
            case ProtocolType::UDP_BASIC_CONFIG:       //FVM->IVA 基本通道数据
                MSG_DATA_RECEIVED(onChannelBasicConf, VecChannelBasicConf, msgJson);
                break;
            case ProtocolType::UDP_CHANNEL_DETECT:     //FVM->IVA 通道检测参数
                MSG_DATA_RECEIVED(onChannelDetectParam, ChannelDetectParam, msgJson);
                break;
            case ProtocolType::UDP_CHANNEL_RESTORECONF: //FVM->IVA 恢复配置
                MSG_DATA_RECEIVED(onChannelRestore, ChannelRestoreConf, msgJson);
                break;
            case ProtocolType::UDP_CHANNEL_PAUSECONF:   //FVM->IVA 暂停配置
                MSG_DATA_RECEIVED(onChannelPause, ChannelPauseConf, msgJson);
                break;
            case ProtocolType::UDP_IVA_REQUEST_VIDEO:   //WEB->IVA 请求结构化视频
                MSG_DATA_RECEIVED(onRequestIvaVideo, RequestIvaVideo, msgJson);
                break;
            default:
                IVA_LOG_WARN("no msg handler {}", msgData);//打印没处理的ID
                break;
        }
    }
}


