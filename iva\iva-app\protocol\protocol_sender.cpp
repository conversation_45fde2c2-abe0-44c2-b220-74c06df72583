#include <iostream>
#include <thread>
#include <future>
#include "protocol_sender.h"
#include "protocol_manager.h"
#include "protocol_utility.h"
#include "ivautils.h"

namespace iva::protocol
{
    using namespace network;
    using namespace network::util;
    using namespace std;

    atomic_bool keepAliveThreadExit = false;
    std::thread keepAliveThread;
    std::once_flag initAliveTaskOnce;
    int processId = 1;                     //!< iva GPU设备ID
    int channelOffsetVal = 0;              //!< pipeline序号到检测通道号的转换偏移值

    /**
     * @brief      iva-->web心跳包
     */
    void keepAliveTask();

    void initStatusManager()
    {
        std::call_once(initAliveTaskOnce,[&]()
        {
            keepAliveThread = std::thread([&](){
                keepAliveTask();
            });
        });
    }

    void disposeStatusManager()
    {
        keepAliveThreadExit = true;
        if (keepAliveThread.joinable())
            keepAliveThread.join();
    }

    /**
     * @brief      iva-->web心跳包
     */
    void keepAliveTask()
    {
        std::cout << "create aliveThread" << std::endl;
        this_thread::sleep_for(100ms);
        while ( !keepAliveThreadExit )
        {
            //! TODO 多进程情况下，web暂时不能正常显示进程状态，待后续联调测试
            PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_IVA_HEART_INFO, HeartInfo{UDP_START_LISTEN_PORT + processId - 1, 1});
            for ( int i = 0; i < 50; i++ )
            {
                if ( keepAliveThreadExit )
                    break;
                this_thread::sleep_for(100ms);
            }
        }
        IVA_LOG_INFO("exit aliveThread");
    }

    /**
     * @brief     请求初始化
     * @param[in] processID   进程号
     * @param[in] channelSize 当前进程支持的通道总个数
     */
    void sendRequestInit(int processID, int channelSize)
    {
        processId = processID;
        channelOffsetVal = 1 + (processId - 1) * channelSize;
        RequestInit requestInit{processId};
        PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_REQUEST_INIT, requestInit);
        IVA_LOG_INFO("[udp][send] request initialization to FVM");
    }

    /**
     * @brief 事件解除通知
     * @param[in] eventId:      事件Id
     * @param[in] finishTime:   事件解除时的时间
     * @param[in] eventType:    事件类型
     * @param[in] imagePath:    事件对应的图片路径
     */
    void postEventRemove(const std::string& eventId, const std::string& finishTime, int eventType, const std::string& imagePath)
    {
        EventRemoveInfo eventRemoveInfo;
        eventRemoveInfo.eventId = eventId;
        eventRemoveInfo.finishTime = finishTime;
        eventRemoveInfo.eventTypeId = eventType;
        if (!imagePath.empty())
            eventRemoveInfo.finishImg = PROTOCOL_MANAGER.getWebURL() + imagePath;

        PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_EVENT_REMOVE, eventRemoveInfo);
    }

    /**
     * @brief 事件撤回通知
     * @param[in] eventId:       事件Id
     * @param[in] eventType:     事件类型
     * @param[in] occurTime:     事件发生时的时间
     */
    void postEventWithdraw(const std::string& eventId, int eventType, const std::string& occurTime)
    {
        PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_EVENT_WITHDRAW, EventWithdraw{eventId,eventType});
    }

    /**
     * @brief 视频质量告警
     * @param[in] videoId:       视频源Id
     * @param[in] alarmTypes:    告警类型集
     * @param[in] isPauseDetect: 是否暂停检测
     * @note      在iva.ini中配置了enableFvmOffsetFilter，则往fvm发送消息，否则往平台web发送消息
     */
    void postVideoQuaAlarm(int videoId, const std::vector<int>& alarmTypes, int isPauseDetect)
    {
        VideoQuaAlarmConf videoQuaAlarmConf{videoId, isPauseDetect, PROTOCOL_MANAGER.getLocalIP(), alarmTypes};
        auto ret = PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_VIDEO_QUAALARM, videoQuaAlarmConf);
        IVA_LOG_INFO("[udp] report video {} quality start ret {}", videoId, (ret ? " ok" : " fail"));
    }

    /**
     * @brief 视频质量恢复
     * @param[in] videoId:       视频源Id
     * @param[in] presetId:      预置位Id
     * @param[in] restoreTypes:  视频质量恢复类型集
     */
    void postVideoQuaRecovery(int videoId, int presetId, const std::vector<int>& restoreTypes)
    {
        VideoQuaRecovery videoQuaRecovery{videoId, presetId, PROTOCOL_MANAGER.getLocalIP(), restoreTypes};
        auto ret = PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_VIDEO_QUARECOVERY, videoQuaRecovery);
        IVA_LOG_INFO("[udp] report video {} quality stop ret {}", videoId, (ret ? " ok" : " fail"));
    }

    /**
     * @brief 通道检测状态上报
     * @param[in] index:         管道序号
     * @param[in] videoId:       视频源Id
     * @param[in] isDetect:      是否处于检测状态
     */
    void postDetectStatus(int index, int videoId, int isDetect)
    {
        if (videoId == -1)
            return;
        int channelId = index + channelOffsetVal;
        DetectStatusInfo statusInfo{channelId, videoId, isDetect};
        auto ret = PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_DETECT_STATUS, statusInfo);
        IVA_LOG_INFO("[http] report channel {} video {} detect status {} ret {} ", channelId, videoId, isDetect, (ret ? " ok" : " fail"));
    }

    /**
     * @brief 请求日夜切换
     * @param[in] index:         管道序号
     * @param[in] videoId:       视频源Id
     * @param[in] isDay:         请求日或夜的时间切换方案
     */
    void postDayNight(int index, int videoId, int isDay)
    {
        if (videoId == -1)
            return;
        int channelId = index + channelOffsetVal;
        auto ret = PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_REQUEST_DAYNIGHT, RequestDayNightInfo{channelId, videoId, isDay});
        IVA_LOG_INFO("[UDP] request day night channel {} videoId {} isDay {} ret {}", channelId, videoId, isDay, (ret ? " ok" : " fail"));
    }

    /**
     * @brief 事件上报
     */
    bool postEvent(int index, const std::string& videoPath, const std::string& imgPath,
                   AlarmType alarmType, const Area_t &objectArea, const evt::EventInfo& eventInfo)
    {
        auto tet = PROTOCOL_MANAGER.getLocalIP();
        EventOccurInfo evtOccurInfo;
        evtOccurInfo.channelId = index + channelOffsetVal;
        evtOccurInfo.roiId = eventInfo.roiID;
        evtOccurInfo.laneId = eventInfo.laneID;
        evtOccurInfo.checkAreaId = eventInfo.regionID;
        evtOccurInfo.eventTypeId = eventInfo.type;

        std::stringstream classTypes;
        for (auto itr = eventInfo.targetClassTypes.begin(); itr != eventInfo.targetClassTypes.end(); ++itr)
        {
            classTypes << itr->first << ":" << itr->second;
            if (std::next(itr) != eventInfo.targetClassTypes.end())
                classTypes << ",";
        }
        evtOccurInfo.eventSubType = classTypes.str();

        evtOccurInfo.occurTime = get_system_full_time_str(eventInfo.occurTime);;
        evtOccurInfo.eventVideo = PROTOCOL_MANAGER.getWebURL() + videoPath;
        evtOccurInfo.eventImg = PROTOCOL_MANAGER.getWebURL() + imgPath;
        evtOccurInfo.isAlarm = alarmType;
        evtOccurInfo.objectArea = objectArea.vtPoint;
        evtOccurInfo.areaTypeId = 0;
        evtOccurInfo.eventId = eventInfo.id;
        PROTOCOL_MANAGER.sendToFVM(ProtocolType::UDP_EVENT_OCCURED, evtOccurInfo);

        return true;
    }


    bool postTrack(std::vector<Trackinfo>& tracks )
    {
        if (tracks.empty())
            return false;

        PROTOCOL_MANAGER.sendToWEB(ProtocolType::HTTP_SAVE_DETECTINFO, VecDetectInfo{{DetectInfo{std::move(tracks)}}});
        return true;
    }



}
