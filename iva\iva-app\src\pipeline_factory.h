#pragma once
#include "pipeline_element.h"
#include <gst/gst.h>
#include "ivacommon.h"
#include "ivautils.h"

namespace iva::pipeline
{
	extern int processId;                    //!< 进程ID
	extern int deviceId;                     //!< gpu 设备ID
	extern int totalChannelSize;			 //!< iva可推理的通道总数
	extern GstElement* mainPipeline;		 //!< 主管道

	/**
	* @brief GstElementBin封装
	*/
	struct ElementBin
	{
		GstElement* bin = nullptr;
		GstElement* first = nullptr;
		GstElement* last = nullptr;

		ElementBin(GstElement* gstbin)
		{
			bin = gstbin;
		}

		GstElement* linkNext(GstElement* next)
		{
			gst_bin_add_many(GST_BIN(bin), next, NULL);
			if (last)
			{
				GST_ELEMENT_LINK_MANY_CHECK(next->object.name, last, next);
			}
			if (!first)
			{
				first = next;
			}
			last = next;
			return last;
		}
	};

	/**
	  * @brief 创建单个通道的pipeline的source bin (取流、rtp解包、解码、裁剪 等插件配置)
	  * @param[in] 通道序号(从0开始)
	  */
	GstElement* createSourceBin(guint index);

	/**
	 * @brief 创建单个通道的pipeline的core bin (推理、跟踪 等插件配置)
	 * @param[in] 通道序号(从0开始)
	 */
	GstElement* createCoreBin(int index);

	/**
	 * @brief 创建单个通道的pipeline的sink bin(裁剪、iva、编码、推流 等插件配置)
	 * @param[in] 通道序号(从0开始)
	 */
	GstElement* createSinkBin(guint index);

	/**
	 * @brief 创建rtmp sink相关元件
	 */
	GstElement* createRtmpSink(int channel);

	/**
	 * @brief 销毁rtmp sink相关元件
	 */
	void destroyRtmpSink(int channel);
}
