/**
* @brief   	AI视频分析pipeline(多路数)
*/
#include "main.h"
#include <atomic>
#include <stdio.h>
#include <string>
#include <malloc.h>
#include <thread>

#include "devmemory.h"

GstElement* pipeline = nullptr;

static gboolean onKeyboard(GIOChannel* source, GIOCondition cond, gpointer data);
static void onSignalAction(int signum, siginfo_t* siginfo, void* ucontext);
gboolean busCall(GstBus * bus, GstMessage * msg, gpointer data);

int totalChannelSize = 1;
int processId = 1;
int deviceId = 0;
std::atomic_bool running = false;
std::thread pipelineLoopThread;

GMainLoop* mainLoop = nullptr;

int main(int argc, char* argv[])
{
// #ifdef HUAWEI
//     mallopt(M_MMAP_MAX, 0);         ///< 禁止使用mmap进行大块内存分配，避免内存碎片
//     mallopt(M_TRIM_THRESHOLD, 0);   ///< 禁止使用madvise进行内存释放，避免内存碎片
// #endif

	struct sigaction action = {};
	action.sa_sigaction = onSignalAction;
	action.sa_flags = SA_SIGINFO;
	sigaction(SIGTERM, &action, nullptr);

	devInit();
	// gstreamer initialization
	gst_init(NULL, NULL);
	//gst_debug_set_default_threshold(GST_LEVEL_ERROR);
	//gst_debug_set_threshold_for_name("aivideodecoder", GST_LEVEL_TRACE);
	//gst_debug_set_threshold_from_string("*:1,aivideodecoder:7", TRUE);

	try {
		totalChannelSize = (argc > 1) ? std::stoi(argv[1]) : totalChannelSize;
		processId = (argc > 2) ? std::stoi(argv[2]) : processId;
		deviceId = (argc > 3) ? std::stoi(argv[3]) : deviceId;
	}
	catch (std::exception& e) {
		std::cerr << "\n\n\n运行参数配置错误:" << e.what() << "\n\n\n";
		return 0;
	}


#if defined(CAMBRICON_MLU220) || defined(CAMBRICON_MLU270)
	totalChannelSize = 1;
#elif defined(CAMBRICON_MLU370)
    totalChannelSize = 1;
#elif defined(HUAWEI_310P)
	totalChannelSize = 16;
#elif defined(HUAWEI_310)
	totalChannelSize = 16;
#else
	totalChannelSize = 16;
#endif

	GOptionEntry entries[] = {
	  { "channel", 'c', 0, G_OPTION_ARG_INT, &totalChannelSize, "max channel", NULL },
	  { "device", 'd', 0, G_OPTION_ARG_INT, &deviceId, "device id", NULL },
	  { NULL }
	};
	GError* err = NULL;
	auto ctx = g_option_context_new("iva pipeline");
	g_option_context_add_main_entries(ctx, entries, NULL);
	if (!g_option_context_parse(ctx, &argc, &argv, &err)) {
		g_print("Failed to parse input parameters: %s\n", err->message);
		g_clear_error(&err); g_option_context_free(ctx);
		return 1;
	}
	g_option_context_free(ctx);

	// pipeline new
	auto loop = mainLoop = g_main_loop_new(NULL, FALSE);
	pipeline = gst_pipeline_new("iva-pipeline");

	auto mux = gst_element_factory_make(AI_STREAMMUX, "iva-streammux");
	GST_ELEMENT_CHECK1(mux, "iva-streammux");
	gst_bin_add(GST_BIN(pipeline), mux);
	g_object_set(G_OBJECT(mux), "batch-size", totalChannelSize, NULL);

	auto demux = gst_element_factory_make(AI_STREAMDEMUX, "iva-streamdemux");
	GST_ELEMENT_CHECK1(demux, "iva-streamdemux");
	gst_bin_add(GST_BIN(pipeline), demux);

	auto core = iva::createCoreBin(0, deviceId);
	GST_ELEMENT_CHECK1(core, "core bin");
	gst_bin_add(GST_BIN(pipeline), core);

	auto encoder = iva::createEncodeBin(deviceId);
	gst_bin_add(GST_BIN(pipeline), encoder);

	// source bins  -> core bin -> sink bins
	for (int i = 0; i < totalChannelSize; ++i)
	{
		// create source bin i
		auto src = iva::createSourceBin(i, processId, deviceId);
		GST_ELEMENT_CHECK1(src, "source bin");
		gst_bin_add(GST_BIN(pipeline), src);

		// create sink bin i
		auto sink = iva::createSinkBin(i, deviceId);
		GST_ELEMENT_CHECK1(sink, "sink bin");
		gst_bin_add(GST_BIN(pipeline), sink);

		// create fake sink i
		auto fakesink = iva::createFakeSink(i);
		GST_ELEMENT_CHECK1(fakesink, "fake sink");
		gst_bin_add(GST_BIN(pipeline), fakesink);

		CHECK_IF_MSG1(gst_element_link(sink, fakesink), "fake sink link error");
		CHECK_IF_MSG1(gst_element_link(src, sink), "sss sink link error");
		//CHECK_IF_MSG1(link_element_to_streammux_sink_pad(sink, src, i), "streammux link error");
		//CHECK_IF_MSG1(link_element_to_streammux_sink_pad(mux, src, i), "streammux link error");
		//CHECK_IF_MSG1(link_element_to_demux_src_pad(demux, sink, i), "streamdemux link error");
	}

	//GST_ELEMENT_LINK_MANY_CHECK("main pipe", mux, core, demux);

	// run in loop
	g_print("[IVA]==Running...device:%d\n", deviceId);

	// listen bus message
	auto bus = gst_pipeline_get_bus (GST_PIPELINE (pipeline));
	auto bus_watch_id = gst_bus_add_watch (bus, busCall, loop);
	gst_object_unref (bus);

	// start run
	gst_element_set_state (pipeline, GST_STATE_PLAYING);
	gst_element_get_state(pipeline, 0, 0, GST_SECOND * 10);


	// run in loop
	g_print ("[IVA]==Running...\n");

	g_main_loop_run (loop);

	// stop run
	g_print ("Returned, stopping playback\n");
	gst_element_set_state (pipeline, GST_STATE_NULL);
	g_print ("Deleting pipeline\n");
	gst_object_unref (GST_OBJECT (pipeline));

	g_source_remove(bus_watch_id);
	g_main_loop_unref(loop);

	running = false;
	return 0;
}

  /**
     * @brief 消息总线回调处理
     */
    gboolean busCall(GstBus * bus, GstMessage * msg, gpointer data)
    {
        GMainLoop* loop = (GMainLoop*)data;
        switch (GST_MESSAGE_TYPE (msg)) {
            case GST_MESSAGE_EOS:
            {
                printf("End of stream\n");
                g_main_loop_quit (loop);
                break;
            }
            case GST_MESSAGE_WARNING:
            {
                gchar *debug;
                GError *error;
                gst_message_parse_warning(msg, &error, &debug);
            	g_printerr("WARNING from element %s: %s\n", GST_OBJECT_NAME(msg->src), error->message);

                g_printerr("Warning: %s\n", error->message);

                if (strstr(error->message, "Could not decode"))
                {
                    std::string element_name = GST_OBJECT_NAME(msg->src);
                    if (strstr(element_name.c_str(), "rtph264_depay") == nullptr)
                    {
                        size_t last_index = element_name.find_last_not_of("0123456789");
                        std::string stream_id_str = element_name.substr(last_index + 1);
                        int channel = std::stoi(stream_id_str);
                        //resetDecoder(channel);
                    }
                }
                g_free(debug);
                g_error_free(error);
                break;
            }
            case GST_MESSAGE_ERROR:
            {
                bool quit(false);
                gchar *debug;
                GError *error;

                gst_message_parse_error (msg, &error, &debug);

            	g_printerr("ERROR from element %s: %s\n", GST_OBJECT_NAME(msg->src), error->message);
                //if (debug)
                g_printerr("Error details: %s\n", debug);
            	if (strstr(error->message, "AiCoreException"))
                {
                    quit = true;
                    g_printerr("\n\nGot aivideodecoder AiCoreException, fatal error occured, restart is need!");
                }
                g_free(debug);
                g_error_free(error);
                // if (quit)
                // {
                //     IVA_LOG_ERROR("Got fatal error and pipeline will quit later!!!!");
                //     g_main_loop_quit (loop);
                // }
                //g_main_loop_quit (loop);
                break;
            }
            case GST_MESSAGE_ELEMENT:
            {
                const GstStructure *s = gst_message_get_structure(msg);
                if (gst_structure_has_name(s, "mux-flush-request"))
                {
                    gst_element_send_event(pipeline, gst_event_new_flush_start());
                    gst_element_send_event(pipeline, gst_event_new_flush_stop(TRUE));
                }
#ifdef NVIDIA
                if (gst_nvmessage_is_stream_eos(msg))
                {
                    guint stream_id;
                    if (gst_nvmessage_parse_stream_eos(msg, &stream_id))
                    {
                        IVA_LOG_WARN("Got EOS from stream {}\n", stream_id);
                    }
                }
                break;
#endif // !NVIDIA
            }
            default:
                break;
        }
        return TRUE;
    }


static void onSignalAction(int signum, siginfo_t* siginfo, void* ucontext) {
	switch (signum) {
	case SIGTERM:
		gst_element_send_event(pipeline, gst_event_new_eos());
		static int request_count = 0;
		if (request_count++ > 1)
			abort();
		break;
	default:
		break;
	}
}

/* Process keyboard input */
static gboolean onKeyboard(GIOChannel* source, GIOCondition cond, gpointer data)
{
	gchar* str = NULL;
	if (g_io_channel_read_line(source, &str, NULL, NULL,
		NULL) != G_IO_STATUS_NORMAL) {
		return TRUE;
	}

	switch (g_ascii_tolower(str[0]))
	{
		case 'r':
		{
			g_print("Reseting \n");

			GstElement* decoder = gst_bin_get_by_name(GST_BIN(pipeline), "cnvideodecoder0");
			GstElement* h264parse = gst_bin_get_by_name(GST_BIN(pipeline), "h264parse0");
			gst_element_set_state(h264parse, GST_STATE_NULL);
			gst_element_set_state(decoder, GST_STATE_NULL);
			g_usleep(30000);
			gst_element_set_state(h264parse, GST_STATE_PLAYING);
			gst_element_set_state(decoder, GST_STATE_PLAYING);
		}
		break;
		case 'q':
		{
			g_print("Quitting \n");

			if (pipeline)	gst_element_post_message(pipeline, gst_message_new_eos(NULL));
			else g_print("pipeline is null, please wait pipeline run !\n");

			GST_DEBUG_BIN_TO_DOT_FILE(GST_BIN(pipeline), GST_DEBUG_GRAPH_SHOW_ALL, "pipeline");
		}
		break;
		default:
		{
			char* ptr;
			auto channel = strtol(str, &ptr, 10);
			if (channel >= 0)
			{
				iva::switchChannel(channel);
			}
		}
		break;
	}
	g_free(str);
	return TRUE;
}

