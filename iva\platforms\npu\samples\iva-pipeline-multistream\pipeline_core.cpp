#include <cstdio>
#include "main.h"

// 开启图像模块检测
#define ENABLE_IMAGE_INFER

namespace iva
{
	GstElement* createCoreBin(guint index, guint device)
	{
		gchar elem_name[50];

		// core bin
		GST_ELEMENT_INDEX_NAME(elem_name, "core_bin", index);
		auto core_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(core_bin, elem_name);
		ElementBin elementBin(core_bin);

		// core queue
		GST_ELEMENT_INDEX_NAME(elem_name, "core_queue", index);
		auto core_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(core_queue, elem_name);
		elementBin.linkNext(core_queue);

		// // core infer
		// GST_ELEMENT_INDEX_NAME(elem_name, "core_infer", index);
		// auto core_pgie = gst_element_factory_make(AI_INFER, elem_name);
		// GST_ELEMENT_CHECK(core_pgie, elem_name);
		//       g_object_set(G_OBJECT(core_pgie), "config-file-path", "/data/opt/models/vehicle/config_infer.txt", "gpu-id", device,  NULL);
		// elementBin.linkNext(core_pgie);
		//
		// int gie_unique_id = 10;
		//       char class_ids_map[64] = {};
		//
		//
		// // tracker queue
		// GST_ELEMENT_INDEX_NAME(elem_name, "tracker_queue", index);
		// auto tracker_queue = gst_element_factory_make("queue", elem_name);
		// GST_ELEMENT_CHECK(tracker_queue, elem_name);
		// elementBin.linkNext(tracker_queue);
		//
		// // tracker
		// GST_ELEMENT_INDEX_NAME(elem_name, "core_tracker", index);
		// auto core_tracker = gst_element_factory_make(AI_TRACKER, elem_name);
		// GST_ELEMENT_CHECK(core_tracker, elem_name);
		// elementBin.linkNext(core_tracker);

		GST_BIN_ADD_GHOST_PAD(core_bin, elementBin.first, "sink");
		GST_BIN_ADD_GHOST_PAD(core_bin, elementBin.last, "src");

		return core_bin;
	}
}
