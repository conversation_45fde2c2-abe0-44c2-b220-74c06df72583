#include "main.h"

namespace iva
{
	GstElement* createSinkBin(guint index, guint device)
	{
		gchar elem_name[50];

		// sink bin
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_bin", index);
		auto sink_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(sink_bin, elem_name);
		ElementBin elementBin(sink_bin);

		// sink queue
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_queue", index);
		auto sink_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(sink_queue, elem_name);
		elementBin.linkNext(sink_queue);

		// // ivaplugin1
		// GST_ELEMENT_INDEX_NAME(elem_name, "sink_ivaplugin1_", index);
		// auto sink_ivaplugin1 = gst_element_factory_make("ivaplugin1", elem_name);
		// GST_ELEMENT_CHECK(sink_ivaplugin1, elem_name);
		// g_object_set(G_OBJECT(sink_ivaplugin1), "device-id", device, NULL);
		// elementBin.linkNext(sink_ivaplugin1);

		// create ghost sink pad for bin
		GST_BIN_ADD_GHOST_PAD(sink_bin, elementBin.first, "sink");
		GST_BIN_ADD_GHOST_PAD(sink_bin, elementBin.last, "src");
		return sink_bin;
	}

	GstElement* createFakeSink(guint index)
	{
		gchar elem_name[50];
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_fakesink", index);
		auto sink_fakesink = gst_element_factory_make("fakesink", elem_name);
		GST_ELEMENT_CHECK(sink_fakesink, elem_name);
		g_object_set(G_OBJECT(sink_fakesink), "sync", FALSE, "async", FALSE, "enable-last-sample", FALSE, NULL);
		return sink_fakesink;
	}

	GstElement* createEncodeBin(guint device)
	{
		gchar elem_name[50];

		// encode bin
		GST_ELEMENT_INDEX_NAME(elem_name, "encode_bin", 0);
		auto encode_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(encode_bin, elem_name);
		ElementBin elementBin(encode_bin);

		// download
		g_snprintf(elem_name, sizeof(elem_name), "conv_second_%d", 0);
		auto sink_conv_second = gst_element_factory_make(AI_CONVERTER, elem_name);
		GST_ELEMENT_CHECK(sink_conv_second, elem_name);
		g_object_set(G_OBJECT(sink_conv_second), "npu-id", 0, NULL);
		elementBin.linkNext(sink_conv_second);

		// capsfilter
		g_snprintf(elem_name, sizeof(elem_name), "crop_capsfilter%d", 0);
		auto conv_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(conv_capsfilter, elem_name);
		elementBin.linkNext(conv_capsfilter);

		gchar caps_str[128];
		g_snprintf(caps_str, sizeof(caps_str), "video/x-raw");
		auto conv_caps = gst_caps_from_string(caps_str);
		g_object_set(G_OBJECT(conv_capsfilter), "caps", conv_caps, NULL);

		// queue
		g_snprintf(elem_name, sizeof(elem_name), "sink_encode_queue%d", 0);
		auto sink_encode_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(sink_encode_queue, elem_name);
		elementBin.linkNext(sink_encode_queue);

#ifdef SOFTWARE_ENCODER
		// video converter
		g_snprintf(elem_name, sizeof(elem_name), "videoconvert%d", 0);
		auto videoconvert = gst_element_factory_make("videoconvert", elem_name);
		GST_ELEMENT_CHECK(videoconvert, elem_name);
		elementBin.linkNext(videoconvert);

		// queue
		g_snprintf(elem_name, sizeof(elem_name), "video_convert_queue%d", 0);
		auto video_convert_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(video_convert_queue, elem_name);
		elementBin.linkNext(video_convert_queue);
#endif

		// encode
		g_snprintf(elem_name, sizeof(elem_name), "sink_enc%d", 0);
		auto sink_enc = gst_element_factory_make(AI_ENCODER, elem_name);
		GST_ELEMENT_CHECK(sink_enc, elem_name);
		if (strcmp(AI_ENCODER, "x264enc") == 0)
		{
			g_object_set(G_OBJECT(sink_enc), "speed-preset", 3, NULL);
			g_object_set(G_OBJECT(sink_enc), "key-int-max", 25, NULL);		//default auto
			g_object_set(G_OBJECT(sink_enc), "bitrate", 1500, NULL);			//default 2048
			g_object_set(G_OBJECT(sink_enc), "byte-stream", true, NULL);		//avc is not used for net
			g_object_set(G_OBJECT(sink_enc), "insert-vui", false, NULL);		//vui is unnecessary 
		}

		elementBin.linkNext(sink_enc);

		// h264Parse
		g_snprintf(elem_name, sizeof(elem_name), "h264parse%d", 0);
		auto h264parse = gst_element_factory_make("h264parse", elem_name);
		GST_ELEMENT_CHECK(h264parse, elem_name);
		elementBin.linkNext(h264parse);

		// rtmp flvmux
		g_snprintf(elem_name, sizeof(elem_name), "sink_rtmp_flvmux%d", 0);
		auto sink_rtmp_flvmux = gst_element_factory_make("flvmux", elem_name);
		GST_ELEMENT_CHECK(sink_rtmp_flvmux, elem_name);
		g_object_set(G_OBJECT(sink_rtmp_flvmux), "streamable", TRUE, NULL);
		elementBin.linkNext(sink_rtmp_flvmux);

		// rtmpsink
		g_snprintf(elem_name, sizeof(elem_name), "sink_rtmpsink%d", 0);
		auto sink_rtmpsink = gst_element_factory_make("rtmpsink", elem_name);
		GST_ELEMENT_CHECK(sink_rtmpsink, elem_name);
		gchar rtmp_location[100];
		guint streamId = processId + 31000;
		g_snprintf(rtmp_location, sizeof(rtmp_location), "rtmp://127.0.0.1/live/%d", streamId);
		g_print("rtmp_location %s \n", rtmp_location);
		g_object_set(G_OBJECT(sink_rtmpsink), "location", rtmp_location, "async", FALSE, "sync", FALSE, NULL);
		elementBin.linkNext(sink_rtmpsink);

		GST_BIN_ADD_GHOST_PAD(encode_bin, elementBin.first, "sink");
		return encode_bin;
	}

	int currentRtmpStreamID = -1;
	bool rtmpSwitching = false;
	static GstPadProbeReturn onRtmpFakeSinkSwitch(GstPad* pad, GstPadProbeInfo* info, gpointer user_data)
	{
		g_print("onRtmpFakeSinkSwitch \n");

		GstElement** sinks = (GstElement**)user_data;
		GstElement* sink_bin = sinks[0];
		GstElement* last_sink = sinks[1];
		GstElement* new_sink = sinks[2];
		GstElement* sink_bin_next = sinks[3];
		GstElement* fake_sink_next = sinks[4];

		gst_pad_remove_probe(pad, GST_PAD_PROBE_INFO_ID(info));

		gst_element_unlink(sink_bin, last_sink);
		gst_element_link(sink_bin, new_sink);

		//gst_element_set_state(sink_bin, GST_STATE_NULL);
		//gst_element_set_state(new_sink, GST_STATE_NULL);
		//g_usleep(20000);

		gst_element_set_state(sink_bin, GST_STATE_PLAYING);
		gst_element_set_state(new_sink, GST_STATE_PLAYING);

		gst_element_sync_state_with_parent(new_sink);

		delete[] sinks;

		if (sink_bin_next)
		{
			GstElement** datas = new GstElement * [5] { sink_bin_next, fake_sink_next, last_sink };
			auto blockpad = gst_element_get_static_pad(sink_bin_next, "src");
			gst_pad_add_probe(blockpad, GST_PAD_PROBE_TYPE_IDLE, onRtmpFakeSinkSwitch, datas, NULL);
			gst_object_unref(blockpad);
		}
		else
		{
			rtmpSwitching = false;
		}

		return GST_PAD_PROBE_OK;
	}


	bool switchChannel(int channel)
	{
		if (rtmpSwitching) return false;
		g_print("switch to channel %d \n", channel);

		gchar elem_name[50];
		// encode bin
		GST_ELEMENT_INDEX_NAME(elem_name, "encode_bin", 0);
		GstElement* encode_bin = gst_bin_get_by_name(GST_BIN(pipeline), elem_name);

		GST_ELEMENT_INDEX_NAME(elem_name, "sink_fakesink", channel);
		GstElement* fakesink_next = gst_bin_get_by_name(GST_BIN(pipeline), elem_name);
		GST_ELEMENT_CHECK(fakesink_next, elem_name);

		rtmpSwitching = true;
		// sink bin
		GST_ELEMENT_INDEX_NAME(elem_name, "sink_bin", channel);
		GstElement* sink_bin_next = gst_bin_get_by_name(GST_BIN(pipeline), elem_name);
		GST_ELEMENT_CHECK(sink_bin_next, elem_name);

		g_snprintf(elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", channel);
		GstElement* ivaplugin1_next = gst_bin_get_by_name(GST_BIN(sink_bin_next), elem_name);
		g_object_set(G_OBJECT(ivaplugin1_next), "draw-frame", true, NULL);

		if (currentRtmpStreamID >= 0)
		{
			GST_ELEMENT_INDEX_NAME(elem_name, "sink_fakesink", currentRtmpStreamID);
			GstElement* fakesink_now = gst_bin_get_by_name(GST_BIN(pipeline), elem_name);
			GST_ELEMENT_CHECK(fakesink_now, elem_name);

			// sink bin
			GST_ELEMENT_INDEX_NAME(elem_name, "sink_bin", currentRtmpStreamID);
			GstElement* sink_bin_now = gst_bin_get_by_name(GST_BIN(pipeline), elem_name);
			GST_ELEMENT_CHECK(sink_bin_now, elem_name);

			g_snprintf(elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", currentRtmpStreamID);
			GstElement* ivaplugin1 = gst_bin_get_by_name(GST_BIN(sink_bin_now), elem_name);
			g_object_set(G_OBJECT(ivaplugin1), "draw-frame", false, NULL);

			GstElement** datas = new GstElement * [5] { sink_bin_now, encode_bin, fakesink_now, sink_bin_next, fakesink_next };
			auto blockpad = gst_element_get_static_pad(sink_bin_now, "src");
			gst_pad_add_probe(blockpad, GST_PAD_PROBE_TYPE_IDLE, onRtmpFakeSinkSwitch, datas, NULL);
			gst_object_unref(blockpad);

		}
		else
		{
			GstElement** datas = new GstElement * [5] { sink_bin_next, fakesink_next, encode_bin };
			auto blockpad = gst_element_get_static_pad(sink_bin_next, "src");
			gst_pad_add_probe(blockpad, GST_PAD_PROBE_TYPE_IDLE, onRtmpFakeSinkSwitch, datas, NULL);
			gst_object_unref(blockpad);
		}

		currentRtmpStreamID = channel;
		return true;
	}
}
