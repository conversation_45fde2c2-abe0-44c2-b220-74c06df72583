#include "main.h"

namespace iva
{
	GstElement* createSourceBin(guint index, guint process_id, guint device)
	{
		gchar elem_name[50];

		// src bin
		GST_ELEMENT_INDEX_NAME(elem_name, "src_bin", index);
		auto src_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(src_bin, elem_name);
		ElementBin elementBin(src_bin);

		// updsrc
		GST_ELEMENT_INDEX_NAME(elem_name, "udp_src", index);
		auto udp_src = gst_element_factory_make("udpsrc", elem_name);
		GST_ELEMENT_CHECK(udp_src, elem_name);
		guint port = 20000 + index * 2 + (process_id-1) * totalChannelSize * 2;
		g_object_set(G_OBJECT(udp_src), "port", port, NULL);
		g_object_set(G_OBJECT(udp_src), "buffer-size", 25 * 1024 * 1024, NULL);
		elementBin.linkNext(udp_src);

		GST_ELEMENT_INDEX_NAME(elem_name, "udp_queue", index);
		auto udp_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(udp_queue, elem_name);
		elementBin.linkNext(udp_queue);

		// capsfilter
		GST_ELEMENT_INDEX_NAME(elem_name, "rtpdepay_capsfilter", index);
		auto rtpdepay_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(rtpdepay_capsfilter, elem_name);
		auto enc_caps = gst_caps_new_simple("application/x-rtp",
			"media", G_TYPE_STRING, "video",
			"encoding-name", G_TYPE_STRING, "H264",
			"payload", G_TYPE_INT, 96,
			"clock-rate", G_TYPE_INT, 90000,
			NULL);
		g_object_set(G_OBJECT(rtpdepay_capsfilter), "caps", enc_caps, NULL);
		elementBin.linkNext(rtpdepay_capsfilter);

		// rtph264depay
		GST_ELEMENT_INDEX_NAME(elem_name, "rtph264_depay", index);
		auto rtph264_depay = gst_element_factory_make("rtph264depay", elem_name);
		GST_ELEMENT_CHECK(rtph264_depay, elem_name);
		elementBin.linkNext(rtph264_depay);

		/// h264Parse
		GST_ELEMENT_INDEX_NAME(elem_name, "h264parse%d", index);
		auto h264parse = gst_element_factory_make("h264parse", elem_name);
		GST_ELEMENT_CHECK(h264parse, elem_name);
		elementBin.linkNext(h264parse);

		/**
		 * @brief aidecoder support auto detect output resolution, two way like below:
		 * 	1) set property, like 'aidecoder device-id=1, ..., ow=640, oh=384'
		 * 	2) add caps, like 'capsfilter video/x-raw(memory:mlu), width=640, height=384, format=NV12'
		 * 	if both two way is set, then the second manner(add caps) will cover the fist manner(set property)
		 */
		GST_ELEMENT_INDEX_NAME(elem_name, "decoder", index);
		auto decodebin = gst_element_factory_make(AI_DECODER, elem_name);
		GST_ELEMENT_CHECK(decodebin, elem_name);
		g_object_set(G_OBJECT(decodebin), "device-id", device, NULL);
		g_object_set(G_OBJECT(decodebin), "process-id", process_id, NULL);
		g_object_set(G_OBJECT(decodebin), "max-decode-channels", 50, NULL);	//channel count for one process
		g_object_set(G_OBJECT(decodebin), "input-buffer-num", 10, NULL);
		g_object_set(G_OBJECT(decodebin), "output-buffer-num", 15, NULL);
		{
			char buf[128] = { 0 };
			g_snprintf(buf, sizeof(buf), "idx:%02d,port:%d", index, port);
			g_object_set(G_OBJECT(decodebin), "debug-info", buf, NULL);
		}

		elementBin.linkNext(decodebin);

#ifdef CAMBRICON
		// queue，aidecoder out frame buffer is read only and not support thread async
		 GST_ELEMENT_INDEX_NAME(elem_name, "crop_queue", index);
		 auto crop_queue = gst_element_factory_make("queue", elem_name);
		 GST_ELEMENT_CHECK(crop_queue, elem_name);
		 elementBin.linkNext(crop_queue);


		// crop converter
		 g_snprintf(elem_name, sizeof(elem_name), "crop_conv%d", index);
		 auto crop_conv = gst_element_factory_make(AI_CONVERTER, elem_name);
		 GST_ELEMENT_CHECK(crop_conv, elem_name);
		 //elementBin.linkNext(crop_conv);
#endif

		// capsfilter
		g_snprintf(elem_name, sizeof(elem_name), "crop_capsfilter%d", index);
		auto crop_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(crop_capsfilter, elem_name);

		gchar caps_str[128];

#ifdef CAMBRICON_MLU370
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu),width=%d,height=%d,format=RGB", 640, 384);
#elif CAMBRICON_MLU270
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu),width=%d,height=%d,format=RGB", 640, 640);
#elif CAMBRICON_MLU220
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu),width=%d,height=%d,format=RGBA", 640, 640);
#else
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu), width=%d, height=%d", 640, 384);
#endif
		auto crop_caps = gst_caps_from_string(caps_str);
		g_object_set(G_OBJECT(crop_capsfilter), "caps", crop_caps, NULL);
		//elementBin.linkNext(crop_capsfilter);

		GST_BIN_ADD_GHOST_PAD(src_bin, elementBin.last, "src");
		return src_bin;
	}
}
