
#include "gst_aideviceframe_allocator.h"
#include "gst_aideviceframe_allocator.ixx"
#include "videoDecoder/video_decoder_util.h"
#include "videoDecoder/video_decoder.h"
#include "videoDecoder/decoder_define.ixx"
#include "devmemory.h"

static gpointer gst_aideviceframe_memory_map(GstMemory* memory, gsize maxsize, GstMapFlags flags)
{
    const auto* mem = reinterpret_cast<GstAiDeviceFrameMemory *>(memory);
    return mem->buf;
}

static void gst_aideviceframe_memory_unmap(GstMemory* mem)
{
}

/**
 * 创建初始化aideviceframe_allocator实例对象
 */
GstAllocator* gst_aideviceframe_allocator_new(void)
{
    gpointer new_obj = g_object_new(GST_AIDEVICEFRAMEALLOCATOR_TYPE, nullptr);
    if (!new_obj)
        return nullptr;

    GstAiDeviceFrameAllocator* self = AIDEVICEFRAMEALLOCATOR_CAST(new_obj);
    if (!self)
    {
        g_object_unref(new_obj);
        return nullptr;
    }

    GstAllocator* alloc = GST_ALLOCATOR(self);
    if (!alloc)
    {
        g_object_unref(new_obj);
        return nullptr;
    }

    self->alloc_num = 0;
    alloc->mem_type = GST_ALLOCATOR_MEMORY_TYPE;
    return alloc;
}

gint gst_aideviceframe_allocator_alloc_num(GstAllocator* allocator)
{
    GstAiDeviceFrameAllocator* self = nullptr;
    if (!GST_IS_AIDEVICEFRAMEALLOCATOR(allocator) || nullptr == (self = AIDEVICEFRAMEALLOCATOR_CAST(allocator)))
    {
        GST_ERROR_OBJECT(allocator, VLOGFMT "Not GstAiDeviceFrameAllocator object, get alloc num failed !", VLOGPARAM);
        return 0;
    }

    return g_atomic_int_get(&self->alloc_num);
}

static void gst_aideviceframe_free(GstAllocator* allocator, GstMemory* memory)
{
    GstAiDeviceFrameAllocator* self = nullptr;
    if (!GST_IS_AIDEVICEFRAMEALLOCATOR(allocator) || nullptr == (self = AIDEVICEFRAMEALLOCATOR_CAST(allocator)))
    {
        GST_ERROR_OBJECT(allocator, VLOGFMT "Not GstAiDeviceFrameAllocator object, free failed !", VLOGPARAM);
        return;
    }

    auto bufmem = reinterpret_cast<GstAiDeviceFrameMemory *>(memory);
    if (bufmem->buf)
    {
        // DeviceFrame的析构函数会自动释放设备内存（当handle为nullptr时）
        // 这里只需要删除DeviceFrame对象即可
        delete bufmem->buf;
        bufmem->buf = nullptr;
    }
    g_free(bufmem);

    if (g_atomic_int_get(&self->alloc_num) <= 0)
        return;
    g_atomic_int_dec_and_test(&self->alloc_num);
}

static GstMemory* gst_aideviceframe_alloc(GstAllocator* allocator, gsize size, GstAllocationParams* params)
{
    GstAiDeviceFrameAllocator* self = nullptr;
    if (!GST_IS_AIDEVICEFRAMEALLOCATOR(allocator) || nullptr == (self = AIDEVICEFRAMEALLOCATOR_CAST(allocator)))
    {
        GST_ERROR_OBJECT(allocator, VLOGFMT "Not GstAiDeviceFrameAllocator object, alloc failed !", VLOGPARAM);
        return nullptr;
    }

    GstAiDeviceFrameMemory* bufmem = g_new0(GstAiDeviceFrameMemory, 1);
    g_return_val_if_fail(bufmem != nullptr, NULL);

    bufmem->buf = nullptr;//new ai::DeviceFrame;
    GstMemory* gst_mem = &bufmem->mem;// GST_MEMORY_CAST(bufmem);
    size_t mem_size = sizeof(ai::DeviceFrame);
    gst_memory_init(gst_mem, static_cast<GstMemoryFlags>(0), allocator, nullptr, mem_size, 0, 0, mem_size);

    g_atomic_int_inc(&self->alloc_num);
    //g_warning("gstaidecoder----new-----new count:%d, free count:%d", ++self->alloc_num, self->free_num);
    return gst_mem;
}

// bool device_frame_dev_mem_free(ai::DeviceFrame* frame)
// {
//     if (Dev_RET_SUCCESS == ai::VideoDecoder::freeDeviceMemory(frame))
//         return true;
//     return false;
// }

bool device_frame_shallow_copy(ai::DeviceFrame* dst, ai::DeviceFrame* src)
{
    //memcpy(dst, src, sizeof(ai::DeviceFrame));
    *dst = *src;

    src->frame_size = 0;
    src->n_planes = 0;

    memset(src->ptrs, 0, sizeof(src->ptrs));
    memset(src->strides, 0, sizeof(src->strides));
    return true;
}

GstMemory* gst_aideviceframe_allocator_alloc_direct(GstAllocator* allocator, ai::DeviceFrame* new_frame)
{
    GstAiDeviceFrameAllocator* self = nullptr;
    if (!GST_IS_AIDEVICEFRAMEALLOCATOR(allocator) || nullptr == (self = AIDEVICEFRAMEALLOCATOR_CAST(allocator)))
    {
        GST_ERROR_OBJECT(allocator, VLOGFMT "Not GstAiDeviceFrameAllocator object, alloc_direct failed !", VLOGPARAM);
        return nullptr;
    }

    GstAiDeviceFrameMemory* dev_mem = g_new(GstAiDeviceFrameMemory, 1);
    g_return_val_if_fail(dev_mem != nullptr, NULL);

    dev_mem->buf = new_frame;
    GstMemory* gst_mem = &dev_mem->mem;// GST_MEMORY_CAST(bufmem);
    size_t mem_size = sizeof(ai::DeviceFrame);
    gst_memory_init(gst_mem, static_cast<GstMemoryFlags>(0), allocator, NULL, mem_size, 0, 0, mem_size);

    // 增加分配计数器，保持与free函数的一致性
    g_atomic_int_inc(&self->alloc_num);

    return gst_mem;
}
