#include <set>
#include <future>
#include <chrono>
#include<gst/gst.h>
#include "vformat.h"
#include "decoder_define.ixx"
#include "video_decoder.h"
#include "videoDecoder/video_decoder_util.h"

#include "gst_aivideodecoder.h"
#include "gst_aideviceframe_allocator.h"
#include "gst_aibuffer_pool.h"

G_BEGIN_DECLS
struct _GstAiVideoDecoderPrivate
{
    GstVideoCodecState* input_state;
    GstVideoCodecState* output_state;
    GstVideoInfo* info;

    ai::VideoOption* option = nullptr;    
    ai::VideoDecoder* decoder = nullptr;
    ai::DecoderState* run_state = nullptr;

    GQueue*        queue;
    guint32        max_size;
    GMutex        lock;
    GQueue*        drop_queue;
    GstAllocator* allocator;
    GstBufferPool*    pool;
    GstVideoFormat gfmt;
    guint64 stream_id = 0;

    gboolean    work;
    std::thread* thread = nullptr;
    GstCaps* last_caps;    

    /* 关键帧+连续确认逻辑 */
    gboolean pending_fmt = FALSE;   // 是否有待确认的新分辨率
    guint32  pend_width = 0;
    guint32  pend_height = 0;
    GstCaps* pend_caps = nullptr;
    guint    pend_count = 0;  // 已连续计数
    guint    pend_confirm_num = 3; // 连续确认阈值，可配置
};
G_END_DECLS

#include "gst_aivideodecoder_prop.ixx"
#include "gst_aivideodecoder_init.ixx"

static void destroy_decoder(GstAiVideoDecoder* self)
{
    auto ai_decoder = (ai::VideoDecoder*)self->priv->decoder;
    if (ai_decoder != nullptr)
    {
        ai_decoder->stop();
        delete ai_decoder;
    }
    self->priv->decoder = nullptr;
}

static void gst_video_dec_handle_drop_frame(GstVideoDecoder* decoder);

static gboolean configure_output_format(GstAiVideoDecoder* self, GstVideoDecoder* decoder, ai::VideoOption* option, GstVideoFormat* gst_fmt);
static gboolean configure_codec_params(GstAiVideoDecoder* self, GstVideoDecoder* decoder, GstCaps* caps, ai::VideoOption* option);
static gboolean initialize_or_reset_decoder(GstAiVideoDecoder* self, GstVideoDecoder* decoder,ai::VideoOption* option);
static gboolean set_decoder_format(GstVideoDecoder *decoder, GstCaps* caps, GstAiVideoDecoder *self);

static gboolean gst_plugin_init(GstVideoDecoder *decoder)
{
    gst_video_decoder_set_packetized(decoder, TRUE);
    gst_video_decoder_set_needs_format(decoder, TRUE);

    auto self = PLUGIN_CAST(decoder);
    self->priv = static_cast<GstAiVideoDecoderPrivate *>(gst_aivideodecoder_get_instance_private(self));

    self->priv->input_state = NULL;
    self->priv->output_state = NULL;
    self->priv->info = NULL;

    self->priv->option = new ai::VideoOption();
    self->priv->decoder = NULL;
    self->priv->run_state = NULL;

    self->priv->queue = NULL;
    self->priv->drop_queue = NULL;
    self->priv->max_size = DEFAULT_MAX_BUFFER_SIZE;
    self->priv->allocator = NULL;
    self->priv->pool = NULL;
    self->priv->gfmt = GST_VIDEO_FORMAT_NV12;
    self->priv->work = FALSE;
    self->priv->thread = NULL;
    self->priv->last_caps = NULL;

    /* 初始化新的分辨率确认状态 */
    self->priv->pending_fmt = FALSE;
    self->priv->pend_caps = nullptr;
    self->priv->pend_width = self->priv->pend_height = 0;
    self->priv->pend_count = 0;
    self->priv->pend_confirm_num = 3;
    return TRUE;
}

static void gst_plugin_finalize(GObject *object)
{
    auto self = PLUGIN_CAST(object);
    GST_INFO_OBJECT(self, VLOGFMT "gst_plugin_finalize start!", VLOGPARAM);

    if (self->priv)
    {
        destroy_decoder(self);
        DELETE_OBJECT(self->priv->option);
        self->priv = NULL;
    }

    GST_INFO_OBJECT(NULL, "%s " VLOGFMT "gst_plugin_finalize finished!", GST_ELEMENT_NAME(self), VLOGPARAM);
    G_OBJECT_CLASS(parent_class)->finalize(object);
}

gboolean gst_dec_sink_query(GstVideoDecoder* decoder, GstQuery* query)
{
    auto ret = GST_VIDEO_DECODER_CLASS(parent_class)->sink_query(decoder, query);
    if (ret && GST_QUERY_TYPE(query) == GST_QUERY_CAPS)
    {
        GstCaps* result_caps = NULL;
        gst_query_parse_caps_result(query, &result_caps);
        if (nullptr == result_caps || gst_caps_is_empty(result_caps))
        {
            GstCaps* template_caps = gst_static_pad_template_get_caps(&plugin_sink_template);
            if (template_caps != nullptr)
            {
                gst_query_set_caps_result(query, template_caps);
                gst_caps_unref(template_caps);
            }
        }
    }

    return ret;
}

gboolean gst_dec_src_query(GstVideoDecoder* decoder, GstQuery* query)
{
    auto ret = GST_VIDEO_DECODER_CLASS(parent_class)->src_query(decoder, query);
    if (ret && GST_QUERY_TYPE(query) == GST_QUERY_CAPS)
    {
        GstCaps* result_caps = NULL;
        gst_query_parse_caps_result(query, &result_caps);
        if (nullptr == result_caps || gst_caps_is_empty(result_caps))
        {
            GstCaps* template_caps = gst_static_pad_template_get_caps(&plugin_src_template);
            if (template_caps != nullptr)
            {
                gst_query_set_caps_result(query, template_caps);
                gst_caps_unref(template_caps);
            }
        }
    }

    return ret;
}

static gboolean gst_video_dec_start(GstVideoDecoder* decoder)
{
    auto self = PLUGIN_CAST(decoder);
    GST_INFO_OBJECT(self, "gst_video_dec_start !");

    const gchar *element_name = GST_ELEMENT_NAME(decoder);
    const char *prefix = "decodebin";
    if (g_str_has_prefix(element_name, prefix))
        sscanf(element_name + strlen(prefix), "%ld", &self->priv->stream_id);

    if (self->priv->max_size < MIN_BUFFER_SIZE)
        self->priv->max_size = MIN_BUFFER_SIZE;

    self->priv->allocator = gst_aideviceframe_allocator_new();
    self->priv->queue = g_queue_new();
    self->priv->drop_queue = g_queue_new();
    g_mutex_init(&self->priv->lock);
    self->priv->pool = gst_aibufferpool_new(self, self->priv->allocator);
    gst_aibufferpool_config_default(self->priv->pool, 0, 100);

    self->priv->run_state = new ai::DecoderState();

    initCodecState(self->priv->run_state->gstIn_, VCODEC_LOG_DEBUG, "gstVideoDecoder receive: ");
    initCodecState(self->priv->run_state->gstOut_, VCODEC_LOG_DEBUG, "gstVideoDecoder send: ");


    ai::VideoDecoder *vdec = createVideoDecoder();
    if (!vdec)
    {
        GST_ERROR_OBJECT(self, "create Video decoder failed, please check platform is supported!" );
        goto cleanup;
    }

    vdec->setDecoderState(self->priv->run_state);
    self->priv->decoder = vdec;
    self->priv->work = TRUE;
    self->priv->thread = new std::thread(on_gst_callback_thread, decoder);
    pthread_setname_np(self->priv->thread->native_handle(), "decode_callback");

    if (!gst_buffer_pool_is_active(self->priv->pool))
        gst_buffer_pool_set_active(self->priv->pool, TRUE);

    // 强制重新协商格式
    gst_video_decoder_set_needs_format(decoder, TRUE);

    GST_INFO_OBJECT(self, VLOGFMT "gst_video_dec_start is called ok!", VLOGPARAM);
    return TRUE;

cleanup:
    if (self->priv->pool && gst_buffer_pool_is_active(self->priv->pool))
        gst_buffer_pool_set_active(self->priv->pool, FALSE);

    destroy_decoder(self);

    if (self->priv->pool)
    {
        gst_aibufferpool_free(self->priv->pool);
        self->priv->pool = NULL;
    }

    if (self->priv->queue)
    {
        g_queue_free(self->priv->queue);
        self->priv->queue = NULL;
    }

    if (self->priv->drop_queue)
    {
        g_queue_clear(self->priv->drop_queue);
        g_queue_free(self->priv->drop_queue);
        self->priv->drop_queue = NULL;
    }
    
    g_mutex_clear(&self->priv->lock);

    if (self->priv->allocator)
    {
        gst_object_unref(self->priv->allocator);
        self->priv->allocator = NULL;
    }

    GST_ERROR_OBJECT(self, "gst_video_dec_start is called failed!");
    return FALSE;
}

static gboolean gst_video_dec_stop(GstVideoDecoder* decoder)
{
    auto self = PLUGIN_CAST(decoder);
    GST_ERROR_OBJECT(self, VLOGFMT "gst_video_dec_stop !", VLOGPARAM);
    self->priv->work = FALSE;

    if (self->priv->pool && gst_buffer_pool_is_active(self->priv->pool))
        gst_buffer_pool_set_active(self->priv->pool, FALSE);

    // 先停止解码器，让内部线程有机会正常退出
    destroy_decoder(self);

    // 给解码器内部线程一些时间来正常退出
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    if (self->priv->thread != nullptr)
    {
        // 使用超时机制避免无限等待
        if (self->priv->thread->joinable())
        {
            // 尝试等待线程结束，但设置超时
            auto future = std::async(std::launch::async, [&]() {
                self->priv->thread->join();
            });

            if (future.wait_for(std::chrono::milliseconds(1000)) == std::future_status::timeout)
            {
                GST_WARNING_OBJECT(self, VLOGFMT "Thread join timeout, forcing cleanup", VLOGPARAM);
                // 线程超时，强制清理（注意：这可能导致资源泄漏，但避免死锁）
                self->priv->thread->detach();
            }
        }
        delete self->priv->thread;
        self->priv->thread = nullptr;
    }

    if (self->priv->pool)
    {
        gst_aibufferpool_free(self->priv->pool);
        self->priv->pool = NULL;
    }

    g_queue_clear(self->priv->queue);
    g_queue_free(self->priv->queue);
    self->priv->queue = NULL;

    g_queue_clear(self->priv->drop_queue);
    g_queue_free(self->priv->drop_queue);
    self->priv->drop_queue = NULL;

    g_mutex_clear(&self->priv->lock);

    if (self->priv->last_caps)
        gst_caps_replace(&self->priv->last_caps, NULL);

    if (self->priv->input_state)
    {
        gst_video_codec_state_unref(self->priv->input_state);
        self->priv->input_state = NULL;
    }
    if (self->priv->output_state)
    {
        gst_video_codec_state_unref(self->priv->output_state);
        self->priv->output_state = NULL;
    }
    if (self->priv->info)
    {
        gst_video_info_free(self->priv->info);
        self->priv->info = NULL;
    }

    if (self->priv->allocator)
    {
        gst_object_unref(self->priv->allocator);
        self->priv->allocator = NULL;
    }
    if (self->priv->pend_caps)
    {
        gst_caps_unref(self->priv->pend_caps);
        self->priv->pend_caps = nullptr;
    }

    self->priv->pending_fmt = FALSE;
    self->priv->pend_count = 0;

    DELETE_OBJECT(self->priv->option);
    DELETE_OBJECT(self->priv->run_state);
    GST_INFO_OBJECT(self, VLOGFMT "gst_video_dec_stop is called ok !", VLOGPARAM);
    return TRUE;
}

//1.codec type; 2.output format changed; 3.output resolution(w, h) changed;
static gboolean compare_caps(GstVideoDecoder* decoder,GstCaps* old_caps, GstCaps* new_caps)
{
    auto self = PLUGIN_CAST(decoder);
    if (!self)
        return FALSE;

    if (old_caps == nullptr)
    {
        GST_INFO_OBJECT(self, VLOGFMT "old caps is null", VLOGPARAM);
        return false;
    }

    const auto last_caps = gst_caps_copy(old_caps);
    const auto cur_caps = gst_caps_copy(new_caps);
    /* Simply ignore framerate for now, this could easily be evolved per CODEC if future issue are found.*/
    gst_structure_remove_field(gst_caps_get_structure(last_caps, 0), "framerate");
    gst_structure_remove_field(gst_caps_get_structure(cur_caps, 0), "framerate");
    gst_structure_remove_field(gst_caps_get_structure(last_caps, 0), "pixel-aspect-ratio");
    gst_structure_remove_field(gst_caps_get_structure(cur_caps, 0), "pixel-aspect-ratio");
    gst_structure_remove_field(gst_caps_get_structure(last_caps, 0), "profile");
    gst_structure_remove_field(gst_caps_get_structure(cur_caps, 0), "profile");


    auto is_equal = gst_caps_is_equal(last_caps, cur_caps);

    if (!is_equal)
    {
        /* 打印原始输入，便于排查 */
        char *old_caps_str  = gst_caps_to_string(last_caps);
        char *new_caps_str  = gst_caps_to_string(cur_caps);
        GST_INFO_OBJECT(self, "compare_caps --> old_caps : %s", old_caps_str);
        GST_INFO_OBJECT(self, "compare_caps --> new_caps : %s", new_caps_str);
        g_free(old_caps_str);
        g_free(new_caps_str);
    }

    gst_caps_unref(last_caps);
    gst_caps_unref(cur_caps);

    return is_equal;
}

static gboolean gst_video_dec_set_format(GstVideoDecoder* decoder, GstVideoCodecState* state)
{
    auto self = PLUGIN_CAST(decoder);
    if (!self)
        return FALSE;

    // 更新输入状态
    if (self->priv->input_state)
    {
        gst_video_codec_state_unref(self->priv->input_state);
        self->priv->input_state = nullptr;
    }
    self->priv->input_state = gst_video_codec_state_ref(state);
    g_return_val_if_fail(self->priv->input_state != nullptr, FALSE);

    // 首次初始化流程
    if (!self->priv->output_state)
    {
        if ((state->info.width <= 0) || (state->info.height <= 0))
            return FALSE;

        const gboolean caps_changed = !compare_caps(decoder, self->priv->last_caps, state->caps);
        gst_caps_replace(&self->priv->last_caps, state->caps);
        GST_INFO_OBJECT(self, "decoder init,channel %lu", self->priv->stream_id);
        return caps_changed ? set_decoder_format(decoder, state->caps, self) : TRUE;
    }

    // 分辨率候选确认与连续 N 次关键帧确认
    const int new_w = state->info.width;
    const int new_h = state->info.height;

    // 当前处于待确认阶段
    if (self->priv->pending_fmt)
    {
        // 收到与原始caps一致的caps, 判断之前pend_caps为误报，撤销待确认
        if (compare_caps(decoder, self->priv->last_caps, state->caps))
        {
            GST_INFO_OBJECT(self, VLOGFMT "Cancel pending resolution switch, input %dx%d, channel: %lu", VLOGPARAM,  new_w, new_h, self->priv->stream_id);
            GST_INFO_OBJECT(self, VLOGFMT "back to output %dx%d, channel: %lu", VLOGPARAM,  self->priv->output_state->info.width, self->priv->output_state->info.height, self->priv->stream_id);
            self->priv->pending_fmt = FALSE;
            gst_caps_replace(&self->priv->pend_caps, nullptr);
            self->priv->pend_count = 0;
            // 更新 last_caps 继续沿用原分辨率
            gst_caps_replace(&self->priv->last_caps, state->caps);
            return TRUE;
        }

        // 出现新的候选caps，重新开始计数
        if (!compare_caps(decoder, self->priv->pend_caps, state->caps))
        {
            // 出现新的帧格式
            self->priv->pend_width = new_w;
            self->priv->pend_height = new_h;
            self->priv->pend_count = 0;
            GST_WARNING_OBJECT(self, VLOGFMT "Switch candidate to %dx%d, restart confirm, channel: %lu", VLOGPARAM, new_w, new_h, self->priv->stream_id);
            // gst_caps_replace 会自动处理旧的 caps 引用计数，不需要手动 unref
             if (self->priv->pend_caps)
                 gst_caps_unref(self->priv->pend_caps);
            gst_caps_replace(&self->priv->pend_caps, state->caps);
        }
    }
    else
    {
        // 首次检测到与当前不同的caps分辨率 —— 建立候选
        if (!compare_caps(decoder, self->priv->last_caps, state->caps))
        {
            self->priv->pending_fmt = TRUE;
            // gst_caps_replace 会自动处理旧的 caps 引用计数，不需要手动 unref
             if (self->priv->pend_caps)
                 gst_caps_unref(self->priv->pend_caps);
            gst_caps_replace(&self->priv->pend_caps, state->caps);
            self->priv->pend_width = new_w;
            self->priv->pend_height = new_h;
            self->priv->pend_count = 0;
            GST_WARNING_OBJECT(self, VLOGFMT "Received new caps %dx%d, start confirm window (%u), channel: %lu" , VLOGPARAM, new_w, new_h, self->priv->pend_confirm_num, self->priv->stream_id);
        }
    }
    return TRUE;
}

static gboolean set_decoder_format(GstVideoDecoder *decoder, GstCaps* caps, GstAiVideoDecoder *self)
{
    // 配置解码选项
    std::cout << "set_decoder_format------------------" << std::endl;
    ai::VideoOption* option = self->priv->option;
    if (!option)
    {
        GST_ERROR_OBJECT(decoder, VLOGFMT "VideoOption is null", VLOGPARAM);
        return FALSE;
    }

    // 设置基本参数
    option->width_ = self->priv->input_state->info.width;
    option->height_ = self->priv->input_state->info.height;
    option->callback_ = on_ai_device_frame;
    option->userData_ = decoder;
    option->eventCallback_ = on_video_decoder_event;

    // 清理旧的视频信息
    if (self->priv->info)
    {
        gst_video_info_free(self->priv->info);
        self->priv->info = nullptr;
    }

    // 处理输出格式和分辨率
    GstVideoFormat gst_fmt = aifmt_to_gstfmt(option->pixFmt_);
    if (!configure_output_format(self, decoder, option, &gst_fmt))
        return FALSE;
    
    // 处理编解码参数
    if (!configure_codec_params(self, decoder, caps, option))
        return FALSE;

    // 确保输出分辨率合法
    if (option->oWidth_ <= 0)
        option->oWidth_ = option->width_;
    if (option->oHeight_ <= 0)
        option->oHeight_ = option->height_;

    // 寒武纪平台特定处理 - 不支持解码时缩放
#ifdef DEV_CAMBRICON
    option->oWidth_ = option->width_;
    option->oHeight_ = option->height_;
#endif
     option->channelId_ = self->priv->stream_id;
    // 更新输出格式
    self->priv->gfmt = gst_fmt;
    
    // 初始化或重置解码器
    if (!initialize_or_reset_decoder(self, decoder, option))
        return FALSE;

    // 设置输出状态
    auto output_state = gst_video_decoder_set_output_state(decoder, gst_fmt, option->oWidth_, option->oHeight_, self->priv->input_state);
    
    output_state->caps = gst_video_info_to_caps(&output_state->info);
    gst_caps_set_features(output_state->caps, 0, gst_caps_features_new(GST_CAPS_FEATURE_DEVICE_MEMORY, NULL));

    // 更新输出状态引用
    if (self->priv->output_state)
        gst_video_codec_state_unref(self->priv->output_state);
    self->priv->output_state = output_state;

    GST_INFO_OBJECT(self, "Decoder format set: INPUT width:%d height:%d, OUTPUT: width: %d height: %d, channel: %lu",option->width_, option->height_, option->oWidth_, option->oHeight_, self->priv->stream_id);
    return TRUE;
}

// 配置输出格式参数
static gboolean configure_output_format(GstAiVideoDecoder* self, GstVideoDecoder* decoder, ai::VideoOption* option, GstVideoFormat* gst_fmt)
{
    GstCaps* template_caps = gst_static_pad_template_get_caps(&plugin_src_template);
    GstCaps* allowed_caps = gst_pad_get_allowed_caps(GST_VIDEO_DECODER_SRC_PAD(decoder));
    gboolean result = TRUE;
    
    do {
        // 如果没有特定的允许格式要求，使用默认设置
        if (nullptr == template_caps || nullptr == allowed_caps || allowed_caps == template_caps)
            break;
            
        GstStructure* datastructure = gst_caps_get_structure(allowed_caps, 0);
        if (nullptr == datastructure)
            break;

        // 处理格式设置
        auto format = gst_structure_get_string(datastructure, "format");
        if (format && strlen(format) > 0) {
            if (0 == strcmp(format, "NV12")) {
                option->pixFmt_ = ai::PixelFmt::NV12;
                *gst_fmt = GST_VIDEO_FORMAT_NV12;
            } else if (0 == strcmp(format, "NV21")) {
                option->pixFmt_ = ai::PixelFmt::NV21;
                *gst_fmt = GST_VIDEO_FORMAT_NV21;
            } else if (0 == strcmp(format, "RGB")) {
                option->pixFmt_ = ai::PixelFmt::RGB24;
                *gst_fmt = GST_VIDEO_FORMAT_RGB;
            } else if (0 == strcmp(format, "BGR")) {
                option->pixFmt_ = ai::PixelFmt::BGR24;
                *gst_fmt = GST_VIDEO_FORMAT_BGR;
            } else {
                result = FALSE;
                GST_WARNING_OBJECT(decoder, VLOGFMT "Format '%s' not supported!", VLOGPARAM, format);
                break;
            }
        }

        // 处理输出分辨率
        int w = 0, h = 0;
        if (gst_structure_get_int(datastructure, "width", &w))
            option->oWidth_ = w;
        if (gst_structure_get_int(datastructure, "height", &h))
            option->oHeight_ = h;

        // 处理设备内存标记
        auto feature = gst_caps_get_features(allowed_caps, 0);
        option->hostMem_ = !gst_caps_features_contains(feature, GST_CAPS_FEATURE_DEVICE_MEMORY);
        
    } while (FALSE);

    // 释放资源
    AI_CAPS_UNREF(template_caps);
    AI_CAPS_UNREF(allowed_caps);

    // 验证像素格式是否支持
    if (result && !(*gst_fmt == GST_VIDEO_FORMAT_NV12 || *gst_fmt == GST_VIDEO_FORMAT_NV21)) {
        std::string supportFormat = AIVIDEODECODER_SUPPORT_VIDEO_FORMATS;
        if (*gst_fmt == GST_VIDEO_FORMAT_RGB) {
            if (supportFormat.find("RGB") == std::string::npos) {
                GST_WARNING_OBJECT(decoder, VLOGFMT "RGB Format not supported!", VLOGPARAM);
                result = FALSE;
            }
        } else if (*gst_fmt == GST_VIDEO_FORMAT_BGR) {
            if (supportFormat.find("BGR") == std::string::npos) {
                GST_WARNING_OBJECT(decoder, VLOGFMT "BGR Format not supported!", VLOGPARAM);
                result = FALSE;
            }
        } else {
            GST_WARNING_OBJECT(decoder, VLOGFMT "Format %d not supported!", VLOGPARAM, *gst_fmt);
            result = FALSE;
        }
    }
    
    return result;
}

// 配置编解码参数
static gboolean configure_codec_params(GstAiVideoDecoder* self, GstVideoDecoder* decoder, GstCaps* caps, ai::VideoOption* option)
{
    // 设置默认值
    option->codecType_ = ai::CodecType::H264;
    option->profile_ = ai::VideoProfile_H264_Main;
    option->level_ = ai::VideoLevel_4;
    option->fps_.num_ = 25;
    option->fps_.den_ = 1;
    
    if (!caps)
        return TRUE;
        
    int size = gst_caps_get_size(caps);
    if (size <= 0)
        return FALSE;
        
    GstStructure* structure = gst_caps_get_structure(caps, 0);
    if (nullptr == structure)
        return FALSE;

    // 处理编解码类型
    const gchar* name = gst_structure_get_name(structure);
    if (name) {
        if (0 == strncmp(name, CAPS_NAME_VIDEO_X264, strlen(CAPS_NAME_VIDEO_X264)))
            option->codecType_ = ai::CodecType::H264;
        else if (0 == strncmp(name, CAPS_NAME_VIDEO_X265, strlen(CAPS_NAME_VIDEO_X265)))
            option->codecType_ = ai::CodecType::H265;
        else {
            GST_ERROR_OBJECT(decoder, VLOGFMT "Codec type %s is not supported!", VLOGPARAM, name);
            return FALSE;
        }
    }

    // 处理Profile和Level
    const gchar* profile = gst_structure_get_string(structure, CAPS_KEY_PROFILE);
    if (profile) {
        if (ai::CodecType::H264 == option->codecType_) {
            if (!trans_video_profile(profile, &option->profile_)) {
                GST_ERROR_OBJECT(decoder, VLOGFMT "Profile %s not supported for H264!", 
                                VLOGPARAM, profile);
                return FALSE;
            }
        } else if (ai::CodecType::H265 == option->codecType_) {
            option->profile_ = ai::VideoProfile_H265_Main;
        }
    }
    
    const gchar* level = gst_structure_get_string(structure, CAPS_KEY_LEVEL);
    if (level)
        trans_video_level(level, &option->level_);
        
    // 处理帧率
    gst_structure_get_fraction(structure, CAPS_KEY_FRAMERATE, 
                              &option->fps_.num_, &option->fps_.den_);

    // 打印配置信息
    const gchar* format = gst_structure_get_string(structure, "stream-format");
    const gchar* aligment = gst_structure_get_string(structure, "alignment");
    format = format ? format : "";
    aligment = aligment ? aligment : "";
    
    GST_INFO_OBJECT(decoder, "codec:%d, profile:%d, level:%d, fps:%d/%d, format:%s, align:%s, "
                           "w:%d, h:%d, ow:%d, oh:%d, devId:%d, pool:%d channel: %lu" ,
                   (int)option->codecType_, option->profile_,
                  option->level_, option->fps_.num_, option->fps_.den_, format, aligment, 
                  option->width_, option->height_, option->oWidth_, option->oHeight_, 
                  option->deviceId_, self->priv->max_size, self->priv->stream_id);
                  
    return TRUE;
}

// 初始化或重置解码器
static gboolean initialize_or_reset_decoder(GstAiVideoDecoder* self, GstVideoDecoder* decoder, ai::VideoOption* option)
{
    ai::VideoDecoder* ai_decoder = self->priv->decoder;
    bool ret = false;
    
    // 检查分辨率是否有效
    if (option->width_ <= 0 || option->height_ <= 0)
    {
        GST_WARNING_OBJECT(decoder, "Invalid resolution: %dx%d", option->width_, option->height_);
        return TRUE; // 非致命错误，允许继续
    }

    // 创建新解码器或重置现有解码器
    if (!ai_decoder)
    {
        GST_DEBUG_OBJECT(decoder,  "[%s] Creating new decoder instance", option->dbgInfo_.c_str());
        ai_decoder = createVideoDecoder();
        if (ai_decoder)
        {
            ai_decoder->setDecoderState((ai::DecoderState*)(self->priv->run_state));
            ret = ai_decoder->start(*option);
            self->priv->decoder = ai_decoder;
        }
        else
        {
            GST_ERROR_OBJECT(decoder, "[%s] Failed to create decoder instance", option->dbgInfo_.c_str());
            return FALSE;
        }
    }
    else
    {
        ret = ai_decoder->start(*option);
        
//#ifdef DEV_CAMBRICON
        // 寒武纪平台的改进重置逻辑
        ai::VideoOption oldOption = ai_decoder->getOption();
        
        // 检查关键参数是否变化
        bool major_change = (oldOption.width_ != option->width_ || oldOption.height_ != option->height_ ||
                            oldOption.codecType_ != option->codecType_ || oldOption.pixFmt_ != option->pixFmt_);
                            
        if (major_change)
        {
            GST_INFO_OBJECT(decoder, VLOGFMT "major_change: old Option  width_ %d height_ %d codecType_ %d pixFmt_ %d", VLOGPARAM, oldOption.width_, oldOption.height_, (int)oldOption.codecType_, (int)oldOption.pixFmt_);
            GST_INFO_OBJECT(decoder, VLOGFMT "major_change: new Option  width_ %d height_ %d codecType_ %d pixFmt_ %d", VLOGPARAM, option->width_, option->height_, (int)option->codecType_, (int)option->pixFmt_);
            GST_INFO_OBJECT(decoder, VLOGFMT "Major parameter change detected, performing full reset, channel: %lu", VLOGPARAM, self->priv->stream_id);
            // 如果关键参数变化，执行完整重置
            ret = ai_decoder->reset(*option);
        }
//#else
        // 其他平台使用标准重置
//        ret = ai_decoder->reset(*option);
//#endif
    }

    // 处理结果
    if (!ret)
    {
        GST_ERROR_OBJECT(decoder, "Decoder %s failed, resolution=%dx%d", (ai_decoder ? "reset" : "initialization"), ret, option->width_, option->height_);
        destroy_decoder(self);
        return FALSE;
    }

    // 重置成功，初始化状态
    initCodecState(self->priv->run_state->gstIn_, VCODEC_LOG_DEBUG, "gstVideoDecoder receive: ");
    initCodecState(self->priv->run_state->gstOut_, VCODEC_LOG_DEBUG, "gstVideoDecoder send: ");


    return TRUE;
}

static gboolean gst_video_decoder_flush(GstVideoDecoder * decoder)
{
    auto self = PLUGIN_CAST(decoder);
    g_return_val_if_fail(self != nullptr && self->priv != nullptr && self->priv->decoder != nullptr, FALSE);
    GST_ERROR_OBJECT(self, VLOGFMT "gst_video_decoder_flush is called start !", VLOGPARAM);
    ((ai::VideoDecoder*)self->priv->decoder)->flush();
    GST_WARNING_OBJECT(decoder, VLOGFMT "gst_video_decoder_flush is called start !", VLOGPARAM);
    return TRUE;
}

static gboolean gst_video_decoder_reset(GstVideoDecoder* decoder, gboolean hard)
{
    auto self = PLUGIN_CAST(decoder);
    g_return_val_if_fail(self != nullptr && self->priv != nullptr && self->priv->decoder != nullptr && self->priv->option != nullptr, FALSE);
    GST_ERROR_OBJECT(self, "gst_video_decoder_reset is called start !");

    // 给解码器一些时间来稳定状态，避免死锁
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    if (self->priv->decoder->reset(*self->priv->option))
    {
        GST_WARNING_OBJECT(self, "Reset decoder successfully !");
        // 强制重新协商格式
        //gst_video_decoder_set_needs_format(decoder, TRUE);
    }
    else
        GST_WARNING_OBJECT(self, "Reset decoder failed, Please check !");

    GST_WARNING_OBJECT(decoder, "gst_video_decoder_reset is called end !");
    return TRUE;
}

static void on_video_decoder_event(const int eventId, void* userParam, void* param2)
{
    auto decoder = static_cast<GstVideoDecoder *>(userParam);
    auto self = PLUGIN_CAST(decoder);
    if (nullptr == decoder || nullptr == self)
        return;
    if (VCODEC_DROP_FRAME == eventId)
    {
        if (param2 != nullptr)
        {
            g_mutex_lock(&self->priv->lock);
            g_queue_push_tail(self->priv->drop_queue, param2);
            g_mutex_unlock(&self->priv->lock);
        }
        GST_DEBUG_OBJECT(decoder, VLOGFMT "on_video_decoder_event drop_queue size:%d !", VLOGPARAM, g_queue_get_length(self->priv->drop_queue));
    }
    else if (VCODEC_ERROR_EXCEPTION == eventId)
    { //need restart process
        GST_ERROR_OBJECT(decoder, VLOGFMT "AiCoreException Serious problem occured, maybe restart process is need !", VLOGPARAM);

        GstBus* bus = gst_element_get_bus(GST_ELEMENT(decoder));// GST_ELEMENT_BUS(decoder);
        if (nullptr == bus) return;

        GError* error = g_error_new(gst_stream_error_quark(), GST_STREAM_ERROR_DECODE, "%s", "AiCoreException");
        GstMessage* msg = gst_message_new_error(GST_OBJECT(decoder), error, "AiCoreException");
        gst_bus_post(bus, msg);
        gst_object_unref(bus);
    }
}

/**
 * @brief 硬件解码成功后的回调
 */
static bool on_ai_device_frame(const ai::VideoFrame& frame, void* userData)
{
    auto self = PLUGIN_CAST(static_cast<GstVideoDecoder *>(userData));
    if (nullptr == frame.data_)
    {
        GST_ERROR_OBJECT( self, "on_ai_device_frame frame.data_ is nullptr !");
        return false;
    }

    g_return_val_if_fail(self != nullptr && self->priv != nullptr && self->priv->decoder != nullptr && self->priv->option != nullptr, FALSE);

    if (nullptr == self->priv->info)
    {
        self->priv->info = gst_video_info_new();
        gst_video_info_init(self->priv->info);
        self->priv->info->finfo = gst_video_format_get_info(aifmt_to_gstfmt(self->priv->option->pixFmt_));
        self->priv->info->width = frame.data_->width;
        self->priv->info->height = frame.data_->height;
        self->priv->info->size = frame.data_->frame_size;
        self->priv->info->offset[0] = 0;
        self->priv->info->offset[1] = 0;
        self->priv->info->offset[2] = 0;
        self->priv->info->stride[0] = frame.data_->strides[0];
        self->priv->info->stride[1] = frame.data_->strides[1];
        self->priv->info->stride[2] = frame.data_->strides[2];
    }

    ai::DeviceFrame *pop_mem = nullptr;
    g_mutex_lock(&self->priv->lock);
    guint queue_len = g_queue_get_length(self->priv->queue);
    if (queue_len >= self->priv->max_size)
        pop_mem = static_cast<ai::DeviceFrame *>(g_queue_pop_head(self->priv->queue));
    g_queue_push_tail(self->priv->queue, frame.data_);
    g_mutex_unlock(&self->priv->lock);

    if (pop_mem != nullptr)
    {
        on_video_decoder_event(VCODEC_DROP_FRAME, userData, reinterpret_cast<void* >(pop_mem->frame_num));
        self->priv->decoder->releaseDeviceFrame(pop_mem);
    }
    return true;
}

static void on_gst_callback_thread(void* param)
{
    auto decoder = static_cast<GstVideoDecoder *>(param);
    auto self = PLUGIN_CAST(decoder);
    if(self == nullptr || self->priv == nullptr || self->priv->decoder == nullptr)
    {
        GST_ERROR_OBJECT(self, VLOGFMT "on_gst_callback_thread aiDecoder is NULL", VLOGPARAM);
        return;
    }

#ifdef HUAWEI_310
    devSetDevId(self->priv->option->deviceId_);
#endif

	static int err_count = 0;
    while (self->priv->work)
    {
        ai::DeviceFrame* device_frame = nullptr; // 解码输出帧
        // 取出解码输出队列中的 device_frame
        g_mutex_lock(&self->priv->lock);
        if (!g_queue_is_empty(self->priv->queue))
        {
            device_frame = static_cast<ai::DeviceFrame *>(g_queue_pop_head(self->priv->queue));
        }
        g_mutex_unlock(&self->priv->lock);

        if (!device_frame)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(5));
            continue;
        }

        const auto frame_id = device_frame->frame_num;
        GstVideoCodecFrame* out_frame = gst_video_decoder_get_frame(decoder, static_cast<int>(frame_id));
        if (!out_frame && self->priv->decoder)
        {
            if (GstVideoCodecFrame* oldest_frame = gst_video_decoder_get_oldest_frame(decoder))
            {
                GST_DEBUG_OBJECT(self, "Failed to get output frame for frameId=%ld, oldest_frame_id=%d, possible reasons: frame dropped/expired/out-of-order",frame_id, oldest_frame->system_frame_number);
                gst_video_codec_frame_unref(oldest_frame);
            }
            else
            {
                GST_DEBUG_OBJECT(self, VLOGFMT "Failed to get output frame for frameId=%ld, no frames in decoder queue",VLOGPARAM, frame_id);
            }

            self->priv->decoder->releaseDeviceFrame(device_frame);
            continue;
        }

        // 清理旧帧（只清比当前 decode_frame_number 小的）
        for (int i = 0; i < 100; ++i)
        {
            GstVideoCodecFrame* old_frame = gst_video_decoder_get_oldest_frame(decoder);
            if (!old_frame || old_frame == out_frame)
            {
                if (old_frame)
                    gst_video_codec_frame_unref(old_frame);
                break;
            }

            if (old_frame->decode_frame_number < out_frame->decode_frame_number)
            {
                gst_video_decoder_drop_frame(decoder, old_frame);
            }
            else
            {
                gst_video_codec_frame_unref(old_frame);
                break;
            }
        }

        out_frame->pts = device_frame->pts;

        // 获取输出 buffer
        GstBuffer* gst_buf = gst_aibufferpool_acquire_nowait(self->priv->pool);
        if (!gst_buf)
        {
            GST_ERROR_OBJECT(self, VLOGFMT "Failed to acquire GstBuffer from pool", VLOGPARAM);
            self->priv->decoder->releaseDeviceFrame(device_frame);
            continue;
        }

        const auto mem = reinterpret_cast<GstAiDeviceFrameMemory *>(gst_buffer_get_all_memory(gst_buf));
        if (!mem)
        {
            GST_ERROR_OBJECT(self, VLOGFMT "Failed to get GstAiDeviceFrameMemory", VLOGPARAM);
            gst_buffer_pool_release_buffer(self->priv->pool, gst_buf);
            self->priv->decoder->releaseDeviceFrame(device_frame);
            continue;
        }
        mem->buf = device_frame;
        gst_buffer_replace(&out_frame->output_buffer, gst_buf);
        gst_buffer_replace(&gst_buf, nullptr);  // 释放临时引用

        GstFlowReturn ret = gst_video_decoder_finish_frame(decoder, out_frame);
        if (ret == GST_FLOW_OK)
        {
            codecStateStepCode(self->priv->run_state->gstOut_);
            err_count = 0;
        }
        else
        {
            GST_ERROR_OBJECT(self, VLOGFMT "gst_video_decoder_finish_frame failed: ret=%d", VLOGPARAM, ret);
            if (++err_count > 10)
            {
                GST_ERROR_OBJECT(self, VLOGFMT "Too many consecutive finish_frame failures, exiting...", VLOGPARAM);
                //exit(0);  // TODO
            }

            // 当 finish_frame 失败时，frame 的所有权仍然属于我们
            // gst_video_decoder_drop_frame 会释放 frame 和其 output_buffer
            // output_buffer 的释放会触发 gst_aibufferpool_reset_buffer
            // 在 reset_buffer 中会自动调用 gst_aivideodecoder_release_frame 释放 device_frame
            // 所以这里只需要 drop frame，不要手动释放 device_frame，避免双重释放

            gst_video_decoder_drop_frame(decoder, out_frame);
            // 注意：不要在这里调用 self->priv->decoder->releaseDeviceFrame(device_frame)
            // 因为它会在 buffer pool 的 reset_buffer 中自动释放
        }
    }

     GST_ERROR_OBJECT(self, VLOGFMT "on_gst_callback_thread end", VLOGPARAM);
}

static void gst_video_dec_handle_drop_frame(GstVideoDecoder* decoder)
{
    auto self = PLUGIN_CAST(decoder);
    std::set<int64_t> frameNumList;

    g_mutex_lock(&self->priv->lock);
    while (!g_queue_is_empty(self->priv->drop_queue))
    {
        frameNumList.insert(reinterpret_cast<int64_t>(g_queue_pop_head(self->priv->drop_queue)));
    }
    g_mutex_unlock(&self->priv->lock);

    while (!frameNumList.empty())
    {
        auto pos = frameNumList.begin();
        auto index = static_cast<int64_t>(*pos);
        frameNumList.erase(pos);

        GstVideoCodecFrame* oneframe = gst_video_decoder_get_frame(decoder, index);
        if (oneframe)
            gst_video_decoder_drop_frame(decoder, oneframe);

    }
}

static GstFlowReturn gst_video_dec_handle_frame(GstVideoDecoder* decoder, GstVideoCodecFrame* frame)
{
    auto self = PLUGIN_CAST(decoder);
    if (self == nullptr || self->priv == nullptr || self->priv->decoder == nullptr)
    {
        GST_ERROR_OBJECT(self, VLOGFMT "gst_video_dec_handle_frame aiDecoder is NULL", VLOGPARAM);
        gst_video_decoder_drop_frame(decoder, frame);
        return GST_FLOW_OK;
    }

    codecStateStepCode(self->priv->run_state->gstIn_);

    ai::VideoPacket packet;
    packet.pts_ = frame->dts;
    packet.frameNum_ = frame->system_frame_number;
    auto is_key_frame = GST_VIDEO_CODEC_FRAME_IS_SYNC_POINT(frame);

    // caps待确认阶段
    if (self->priv->pending_fmt)
    {
        if (!is_key_frame)
        {
            gst_video_decoder_drop_frame(decoder, frame);
            return GST_FLOW_OK;
        }

        self->priv->pend_count++;
        if (self->priv->pend_count >= self->priv->pend_confirm_num)
        {
            GST_INFO_OBJECT(self, "Resolution confirmed after %d frames, updating to %dx%d, channel: %lu", self->priv->pend_confirm_num, self->priv->pend_width, self->priv->pend_height, self->priv->stream_id);
            if (!set_decoder_format(decoder, self->priv->pend_caps, self))
            {
                GST_ERROR_OBJECT(self, "Failed to apply new decoder format");
                gst_video_decoder_drop_frame(decoder, frame);
                return GST_FLOW_ERROR;
            }

            gst_caps_replace(&self->priv->last_caps, self->priv->pend_caps);
            if (self->priv->pend_caps)
                gst_caps_unref(self->priv->pend_caps);
            self->priv->pend_caps = nullptr;
            self->priv->pending_fmt = FALSE;
            self->priv->pend_count = 0;
        }
        else
        {
            gst_video_decoder_drop_frame(decoder, frame);
            return GST_FLOW_OK;
        }
    }

    if (!self->priv->decoder->sendPacket(packet))
        gst_video_decoder_drop_frame(decoder, frame);
    else
        gst_video_codec_frame_unref(frame);


    gst_video_dec_handle_drop_frame(decoder);

    //TODO
    //vcodecSystemMSleep(2000);

    return GST_FLOW_OK;
}

bool gst_aivideodecoder_release_frame(GstAiVideoDecoder* decoder, void* deviceframe)
{
    auto self = PLUGIN_CAST(decoder);
    if (nullptr == self || !self->priv->work)
        return false;
    g_return_val_if_fail(self != nullptr && self->priv != nullptr && self->priv->decoder != nullptr && self->priv->option != nullptr, FALSE);
    self->priv->decoder->releaseDeviceFrame(deviceframe);
    return true;
}

void gst_aivideodecoder_pool_changed(GstAiVideoDecoder* decoder, const gint alloc_num)
{
    auto self = PLUGIN_CAST(decoder);
    if (nullptr == self) return ;

    self->priv->run_state->poolSize_.store(alloc_num);
}

void gst_aivcodec_log(const int logLevel, const char* func, const char* file, uint32_t line, const char* fmt, ...)
{
    va_list var_args;

    va_start(var_args, fmt);
    gst_debug_log_valist(GST_CAT_DEFAULT, (GstDebugLevel)logLevel, file, func, line, NULL, fmt, var_args);
    va_end(var_args);
}

void gst_aivcodec_post_message(void* dec_ptr, const int level, const std::string& info, const std::string& debug)
{
    GstVideoDecoder* decoder = (GstVideoDecoder*)dec_ptr;
    GstBus* gbus = gst_element_get_bus(GST_ELEMENT(decoder));
    if (nullptr == gbus)
    {
        GST_ERROR_OBJECT(decoder, "Get bus failed, info:%s, dbg:%s", info.c_str(), debug.c_str());
        return;
    }

    GError* gerror = g_error_new(gst_stream_error_quark(), GST_STREAM_ERROR_DECODE, "%s", info.c_str());
    GstMessage* gmessage = NULL;
    if (VCODEC_LOG_WARNING == level)
        gmessage = gst_message_new_warning(GST_OBJECT(decoder), gerror, debug.c_str());
    else if (VCODEC_LOG_ERROR == level)
        gmessage = gst_message_new_error(GST_OBJECT(decoder), gerror, debug.c_str());
    else if (VCODEC_LOG_INFO == level)
        gmessage = gst_message_new_info(GST_OBJECT(decoder), gerror, debug.c_str());
    else
    {
        g_error_free(gerror);
        gerror = NULL;
    }

    gboolean ret = FALSE;
    if (gmessage) ret = gst_bus_post(gbus, gmessage);
    gst_object_unref(gbus);

    if (ret)
        GST_CAT_LEVEL_LOG(GST_CAT_DEFAULT, (GstDebugLevel)level, decoder, VLOGFMT "post message ok, error:%s, debug:%s", VLOGPARAM, info.c_str(), debug.c_str());
    else
        GST_ERROR_OBJECT(decoder, VLOGFMT "post message failed, error:%s, debug:%s", VLOGPARAM, info.c_str(), debug.c_str());
}
