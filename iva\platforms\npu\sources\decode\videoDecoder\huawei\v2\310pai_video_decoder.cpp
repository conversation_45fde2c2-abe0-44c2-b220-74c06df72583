#include <thread>
#include <chrono>
#include "310pai_video_decoder.h"
#include "vcodec_channel_manage.h"

using namespace std::chrono;

namespace ai
{
    bool Acl310PAiVideoDecoder::setDecoderContext()
    {
        decContext_ = new VCodecContext;
        decContext_->pixFmt_ = getPixelFormat(option_.pixFmt_);

        if (HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420 == decContext_->pixFmt_ || HI_PIXEL_FORMAT_YVU_SEMIPLANAR_420 == decContext_->pixFmt_)
        {
            decContext_->sw_ = ALIGN_UP16(option_.oWidth_);
            decContext_->sh_ = ALIGN_UP2(option_.oHeight_);

            decContext_->sLen_ = decContext_->sw_ * decContext_->sh_ * 3 / 2;
            decContext_->olen_ = option_.oWidth_ * option_.oHeight_ * 3 / 2;
        }
        else if (HI_PIXEL_FORMAT_RGB_888 == decContext_->pixFmt_ || HI_PIXEL_FORMAT_BGR_888 == decContext_->pixFmt_)
        {
            decContext_->sw_ = ALIGN_UP16(option_.oWidth_) * 3;
            decContext_->sh_ = option_.oHeight_;

            decContext_->sLen_ = decContext_->sw_ * decContext_->sh_;
            decContext_->olen_ = option_.oWidth_ * option_.oHeight_ * 3;
        }

        CHECK_ACL_ERR_RF(aclrtSetDevice(option_.deviceId_))
        CHECK_ACL_ERR_RF(aclrtCreateContext(&decContext_->context_, option_.deviceId_))
        CHECK_ACL_ERR_RF(hi_mpi_sys_init())

        if (!initChannel())
            return false;

        if (nullptr == dataPool_)
            dataPool_ = new VDecDataPool(option_.deviceId_, 100);

        if (nullptr == devPool_)
            devPool_  = new DeviceFramePool(decContext_->sLen_, option_.deviceId_, 200);

        decoderInit_ = true;
        return true;
    }

    void Acl310PAiVideoDecoder::destroyDecoder()
    {
        decoderInit_ = false;
        VcodecException::removeListener(this);

        if (decContext_)
        {
            if (option_.channelId_ != VCODEC_INVALID)
            {
                if (state_ == VCODEC_DECODER_START_STATE || state_ == VCODEC_DECODER_RESET_STATE)
                    sendEofPacket();


                hi_mpi_vdec_stop_recv_stream(option_.channelId_);
                clearHWDecoderFrame();
                hi_mpi_vdec_destroy_chn(option_.channelId_);
            }

            if (decContext_->context_ != nullptr)
            {
                aclrtDestroyContext(decContext_->context_);
                decContext_->context_ = nullptr;
            }
            DELETE_OBJECT(decContext_);
        }

        clearDataPool();

        state_ = VCODEC_DECODER_INIT_STATE;
    }

    bool Acl310PAiVideoDecoder::stopDecoder()
    {
        decoderInit_ = false;
        AIVCODEC_ERROR(VLOGFMT "%s stopDecoder is called !", VLOGPARAM, option_.dbgInfo_.c_str());
        VcodecException::removeListener(this);

        return true;
    }

    bool Acl310PAiVideoDecoder::clearHWDecoderFrame()
    {
        int frameCnt = 0;
        hi_video_frame_info frame;
        hi_vdec_stream stream;
        hi_vdec_supplement_info stSupplement{};
        int32_t ret;
        do
        {
            ret = hi_mpi_vdec_get_frame(option_.channelId_, &frame, &stSupplement, &stream, 0);
            if (ret != HI_ERR_VDEC_BUF_EMPTY)
            {
                hi_mpi_vdec_release_frame(option_.channelId_, &frame);
                frameCnt++;
            }
        } while (ret != HI_ERR_VDEC_BUF_EMPTY);

        AIVCODEC_ERROR("clearHWDecoderFrame channelId:%d frame size:%d!", option_.channelId_, frameCnt);
        return true;
    }

    bool Acl310PAiVideoDecoder::sendEofPacket()
    {
        if (nullptr == decContext_ || nullptr == decContext_->context_ || VCODEC_INVALID == option_.channelId_)
        {
            AIVCODEC_WARN(VLOGFMT "%s send eos failed, invalid param!", VLOGPARAM, option_.dbgInfo_.c_str());
            return false;
        }

        // 1.send EOS to decoder
        hi_vdec_pic_info& outPicInfo = decContext_->frame_;
        outPicInfo.vir_addr = 0;
        outPicInfo.buffer_size = 0;

        hi_vdec_stream& stream = decContext_->packet_;
        stream.pts = 0;
        stream.addr = nullptr;
        stream.len = 0;
        stream.end_of_frame = HI_FALSE;
        stream.end_of_stream = HI_TRUE;
        const bool bEosSend = (hi_mpi_vdec_send_stream(option_.channelId_, &stream, &outPicInfo, 100) == HI_SUCCESS);
        if (!bEosSend)
        {
            AIVCODEC_WARN(VLOGFMT "%s Send eos failed, do not need wait finished!", VLOGPARAM, option_.dbgInfo_.c_str());
            return true;
        }

        // 2.wait decoder stop
        const auto timeout = std::chrono::milliseconds(500);
        const auto startTime = std::chrono::steady_clock::now();
        while (std::chrono::steady_clock::now() - startTime < timeout)
        {
            hi_vdec_chn_status status{};
            CHECK_ACL_ERR_BK(hi_mpi_vdec_query_status(option_.channelId_, &status))

            // 检查解码器是否已处理完所有数据
            if (status.left_stream_bytes == 0 && status.left_decoded_frames == 0)
                return true;

            std::this_thread::sleep_for(10ms);
        }

        // 超时未完成，重置通道
        resetChannel();
        return true;
    }

    bool Acl310PAiVideoDecoder::initHWDecoderContext()
    {
        if (nullptr == decContext_ || nullptr == decContext_->context_)
        {
            AIVCODEC_ERROR(VLOGFMT "%s, context is null !", VLOGPARAM, option_.dbgInfo_.c_str());
            return false;
        }
        CHECK_ACL_ERR_RF(aclrtSetCurrentContext(decContext_->context_))

        return true;
    }

    bool Acl310PAiVideoDecoder::codecSupported() 
    {
        if (!(ai::CodecType::H264 == option_.codecType_ || ai::CodecType::H265 == option_.codecType_))
       {
            AIVCODEC_ERROR("codec %d not supported, [H264 H265] is valid!", option_.codecType_);
            return false;
        }

        if (!(ai::PixelFmt::NV12 == option_.pixFmt_  || ai::PixelFmt::NV21 == option_.pixFmt_
            ||ai::PixelFmt::RGB24 == option_.pixFmt_ || ai::PixelFmt::BGR24 == option_.pixFmt_))
        {
            AIVCODEC_ERROR("pixfmt %d not supported, [NV12,NV21,RGB24,BGR24] is valid!", option_.pixFmt_);
            return false;
        }

        return true;
    }

    void Acl310PAiVideoDecoder::getDecoderStatus()
    {
        if (option_.channelId_ != VCODEC_INVALID)
        {
            hi_vdec_chn_status status{};
            const hi_s32 ret = hi_mpi_vdec_query_status(option_.channelId_, &status);
            if (ret != 0)
            {
                const char *msg = aclGetRecentErrMsg();
                AIVCODEC_ERROR("hi_mpi_vdec_query_status failed chn:%d, code:0x%X(%d) msg:%s!", option_.channelId_, ret, ret, msg);
                return;
            }
            AIVCODEC_INFO("decoder chn:%d  info:[0x%X-0x%X-0x%X-0x%X-0x%X-0x%X-0x%X-0x%X-0x%X]", option_.channelId_, status.dec_err.set_pic_size_err, status.dec_err.set_protocol_num_err, status.dec_err.set_ref_num_err
                , status.dec_err.set_pic_buf_size_err, status.dec_err.format_err, status.dec_err.stream_unsupport, status.dec_err.pack_err, status.dec_err.stream_size_over
                , status.dec_err.stream_not_release);
        }
    }

    std::string Acl310PAiVideoDecoder::getRecentErrorMsg()
    {
        const char* msg = aclGetRecentErrMsg();
        return msg ? msg : "";
    }

    bool Acl310PAiVideoDecoder::decodePacketImpl(BufferDesc* bufDesc)
    {
        if(!decContext_)
        {
            AIVCODEC_ERROR(VLOGFMT "%s decodePacketImpl context is null", VLOGPARAM, option_.dbgInfo_.c_str());
            return false;
        }

        hi_vdec_stream& stream = decContext_->packet_;
        stream.addr = static_cast<hi_u8*>(bufDesc->pkt_.buf_);
        //stream.private_data = reinterpret_cast<hi_u64>(bufDesc);
        stream.len = bufDesc->pkt_.len_;  // Configure input stream size
        stream.end_of_frame = HI_TRUE;  // Configure flage of frame end
        stream.end_of_stream = HI_FALSE;  // Configure flage of stream end
        stream.need_display = HI_TRUE;
        stream.pts = bufDesc->pkt_.pts_;

        hi_vdec_pic_info& outPicInfo = decContext_->frame_;
        outPicInfo.width = option_.oWidth_;
        outPicInfo.height = option_.oHeight_;
        outPicInfo.width_stride = decContext_->sw_;
        outPicInfo.height_stride = decContext_->sh_;
        outPicInfo.pixel_format = decContext_->pixFmt_;
        outPicInfo.buffer_size = 0;
        outPicInfo.vir_addr = 0;

        if (bufDesc->frame_)
        {
            outPicInfo.buffer_size = decContext_->sLen_;
            outPicInfo.vir_addr = reinterpret_cast<uint64_t>(bufDesc->frame_->ptrs[0]);
        }

        hi_s32 ret = HI_SUCCESS;

        int retryCount = 0;
        constexpr int maxRetry = 30;
        while (retryCount <= maxRetry)
        {
            constexpr int32_t timeOut = 1000;
            ret = hi_mpi_vdec_send_stream(option_.channelId_, &stream, &outPicInfo, timeOut);
            if (ret == HI_SUCCESS)
                return true;

            if (ret == static_cast<hi_s32>(HI_ERR_VDEC_BUF_FULL))
            {
                std::this_thread::sleep_for(5ms);
                ++retryCount;
                continue;
            }
            if (ret == static_cast<hi_s32>(HI_ERR_VDEC_SYS_NOT_READY))
            {
                AIVCODEC_ERROR("hi_mpi_vdec_send_stream failed, channel not ready!");
                hi_mpi_sys_init();
                continue;
            }
            if (ret == static_cast<hi_s32>(HI_ERR_VDEC_UNEXIST))
            {
                AIVCODEC_ERROR("hi_mpi_vdec_send_stream failed, channel not exist!");
                resetChannel();
                break;
            }

            // 其他错误
            AIVCODEC_ERROR("hi_mpi_vdec_send_stream Send one frame failed channel:%d ret=0x%X(%d)!", option_.channelId_, ret, ret);
            break;
        }

        AIVCODEC_ERROR("hi_mpi_vdec_send_stream Send frame failed times over, resetChannel!");
        resetChannel();
        return false;
    }

    bool Acl310PAiVideoDecoder::handlerDecoderFrame()
    {
        hi_vdec_supplement_info stSupplement{};
        hi_video_frame_info frame;
        hi_vdec_stream stream;
        BufferDesc* bufferDesc = nullptr;
        int32_t ret = hi_mpi_vdec_get_frame(option_.channelId_, &frame, &stSupplement, &stream, 0);
        if (ret != HI_SUCCESS)
        {
            if (ret != HI_ERR_VDEC_BUF_EMPTY) {
                //AIVCODEC_ERROR(VLOGFMT "%s, onCtxThread hi_mpi_vdec_get_frame failed ret=0x%X(%d)!", VLOGPARAM, option_.dbgInfo_.c_str(), ret, ret);
            }
            std::this_thread::sleep_for(10ms);
            goto cleanup;
        }
        bufferDesc = dataPool_->findBufferDescByPts(stream.pts);
        //ret = hi_mpi_dvpp_free(stream.addr);
        //ret = hi_mpi_dvpp_free(frame.v_frame.virt_addr[0]);
        ret = hi_mpi_vdec_release_frame(option_.channelId_, &frame);
        if (HI_SUCCESS != ret)
        {
            const char *msg = aclGetRecentErrMsg();
            AIVCODEC_WARN("hi_mpi_vdec_release_frame failed, ret:%d  msg:%s, channel:%d, device:%d!",ret, nullptr == msg ? "" : msg, option_.channelId_, option_.deviceId_);
        }

        if (!bufferDesc)
        {
            AIVCODEC_WARN("buffer desc nullptr !");
            goto cleanup;
        }

        if (!decoderInit_)
        {
            devPool_->release(bufferDesc->frame_);
            bufferDesc->frame_ = nullptr;
            dataPool_->release(bufferDesc);
            return false;
        }

        switch (frame.v_frame.frame_flag)
        {
            case 0: // 解码成功
            {
                if (auto* frameInfo = bufferDesc->frame_)
                {
                    frameInfo->width      = option_.oWidth_;
                    frameInfo->height     = option_.oHeight_;
                    frameInfo->frame_size = decContext_->olen_;
                    frameInfo->device_id  = option_.deviceId_;
                    frameInfo->channel_id = option_.channelId_;
                    frameInfo->pformat    = option_.pixFmt_;
                    frameInfo->n_planes   = 1;
                    frameInfo->handle     = nullptr;
                    frameInfo->strides[0] = (decContext_->pixFmt_ == HI_PIXEL_FORMAT_RGB_888 || decContext_->pixFmt_ == HI_PIXEL_FORMAT_BGR_888)
                                                ? option_.oWidth_ * 3 : option_.oWidth_ * 3 / 2;
                    frameInfo->frame_num  = frameNum_++;
                    frameInfo->pts        = bufferDesc->pkt_.pts_;
                    frameInfo->user_data  = reinterpret_cast<void*>(bufferDesc->frameNum_);

                    if (option_.callback_(VideoFrame(frameInfo), option_.userData_))
                        bufferDesc->frame_ = nullptr;
                }

                codecStateStepCode(getDecoderState()->codecRecv_);
                break;
            }
            case 1: AIVCODEC_FIX("Decode failed, channel:%d, device:%d", option_.channelId_, option_.deviceId_); break;
            case 2: AIVCODEC_FIX("Interlaced frame skipped, channel:%d, device:%d", option_.channelId_, option_.deviceId_); break;
            case 3: AIVCODEC_FIX("Reference frame number error, channel:%d, device:%d", option_.channelId_, option_.deviceId_); break;
            case 4: AIVCODEC_FIX("Reference frame size error, channel:%d, device:%d", option_.channelId_, option_.deviceId_); break;
            default: AIVCODEC_FIX("Unknown decode result, channel:%d, device:%d", option_.channelId_, option_.deviceId_); break;
        }

cleanup:
        if (bufferDesc != nullptr)
        {
            if (bufferDesc->frame_)
            {
                devPool_->release(bufferDesc->frame_);
                bufferDesc->frame_ = nullptr;
            }
            dataPool_->release(bufferDesc);
            bufferDesc = nullptr;
        }

        return true;
    }

    int Acl310PAiVideoDecoder::getChannelId(const int devId, const int processId, const int channelCount, const int maxTimeoutUs)
    {
        int64_t startTick = vcodecGetSystemTickUs();
        int channelNum = -1;
        do
        {
            VcodecChannelLockFile file;
            channelNum = file.getChannelLocal(devId, processId, channelCount);
            if (channelNum >= 0)
                break;
        } while (vcodecGetSystemTickUs() - startTick < maxTimeoutUs);

        if (channelNum >= 0)
            return channelNum;

        AIVCODEC_ERROR(VLOGFMT "failed to get vdec channel!", VLOGPARAM);
        return VCODEC_INVALID;

    }

    bool Acl310PAiVideoDecoder::resetChannel()
    {
        if(decContext_ == nullptr)
        {
            AIVCODEC_ERROR(VLOGFMT "%s resetChannel decContext_ is null!", VLOGPARAM, option_.dbgInfo_.c_str());
            return false;
        }

        hi_vdec_chn channel = option_.channelId_;
        AIVCODEC_WARN(VLOGFMT "%s resetChannel is called, channel:%d", option_.dbgInfo_.c_str(), VLOGPARAM, channel);
        CHECK_ACL_ERR_RF(hi_mpi_vdec_stop_recv_stream(channel))
        CHECK_ACL_ERR_RF(hi_mpi_vdec_reset_chn(channel))
        CHECK_ACL_ERR_RF(hi_mpi_vdec_start_recv_stream(channel))

        clearDataPool();
        return true;
    }

    bool Acl310PAiVideoDecoder::initChannel()
    {
        const int max_times = option_.oneChannelCount_ * 2;
        for (int i = 0; i < max_times; ++i)
        {
            if (VCODEC_INVALID == option_.channelId_)
            {
                option_.channelId_ = getChannelId(option_.deviceId_, option_.processId_, option_.oneChannelCount_, 5 * 1000 * 1000);
                if (VCODEC_INVALID == option_.channelId_)
                {
                    AIVCODEC_WARN( "getChannelId failed one time, dev:%d", option_.deviceId_);
                    continue;
                }
            }
            decContext_->chAttr_.type = (option_.codecType_ == ai::CodecType::H265) ? HI_PT_H265 : HI_PT_H264;
            decContext_->chAttr_.mode = HI_VDEC_SEND_MODE_FRAME;
            decContext_->chAttr_.pic_width = option_.width_;
            decContext_->chAttr_.pic_height = option_.height_;
            decContext_->chAttr_.stream_buf_size = option_.width_ * option_.height_ * 3 / 2;
            decContext_->chAttr_.frame_buf_cnt = 0; // TODO ACL310P_REF_FRAME_NUM + ACL310P_DISPLAY_FRAME_NUM + 1;
            hi_pic_buf_attr bufAttr{static_cast<hi_u32>(option_.width_), static_cast<hi_u32>(option_.height_),0, HI_DATA_BIT_WIDTH_8,decContext_->pixFmt_,HI_COMPRESS_MODE_NONE};

            decContext_->chAttr_.frame_buf_size = hi_vdec_get_pic_buf_size(decContext_->chAttr_.type, &bufAttr);
            decContext_->chAttr_.video_attr.ref_frame_num = ACL310P_REF_FRAME_NUM;
            decContext_->chAttr_.video_attr.temporal_mvp_en = HI_TRUE;
            decContext_->chAttr_.video_attr.tmv_buf_size = hi_vdec_get_tmv_buf_size(decContext_->chAttr_.type, option_.width_, option_.height_);

            int32_t ret = hi_mpi_vdec_create_chn(option_.channelId_, &decContext_->chAttr_);
            if (HI_SUCCESS != ret)
            {
                const char *msg = aclGetRecentErrMsg();
                AIVCODEC_ERROR("hi_mpi_vdec_create_chn failed, ret%d: msg: channel:%d, device:%d!",ret, nullptr == msg ? "" : msg, option_.channelId_, option_.deviceId_);
                goto cleanup;
            }

            ret = hi_mpi_vdec_get_chn_param(option_.channelId_, &decContext_->chParam_);
            if (HI_SUCCESS != ret)
            {
                const char *msg = aclGetRecentErrMsg();
                AIVCODEC_ERROR("hi_mpi_vdec_get_chn_param failed, ret%d: msg: channel:%d, device:%d!",ret, nullptr == msg ? "" : msg, option_.channelId_, option_.deviceId_);
                goto cleanup;
            }

            decContext_->chParam_.video_param.dec_mode = HI_VIDEO_DEC_MODE_IPB;
            decContext_->chParam_.video_param.compress_mode = HI_COMPRESS_MODE_HFBC;
            decContext_->chParam_.video_param.video_format = HI_VIDEO_FORMAT_TILE_64x16;
            decContext_->chParam_.video_param.out_order = HI_VIDEO_OUT_ORDER_DISPLAY;
            decContext_->chParam_.display_frame_num = ACL310P_DISPLAY_FRAME_NUM;

            ret = hi_mpi_vdec_set_chn_param(option_.channelId_, &decContext_->chParam_);
            if (HI_SUCCESS != ret)
            {
                const char *msg = aclGetRecentErrMsg();
                AIVCODEC_ERROR("hi_mpi_vdec_set_chn_param failed, ret%d: msg: channel:%d, device:%d!",ret, nullptr == msg ? "" : msg, option_.channelId_, option_.deviceId_);
                goto cleanup;
            }

            ret = hi_mpi_vdec_start_recv_stream(option_.channelId_);
            if (HI_SUCCESS != ret)
            {
                const char *msg = aclGetRecentErrMsg();
                AIVCODEC_ERROR("hi_mpi_vdec_start_recv_stream failed, ret%d: msg: channel:%d, device:%d!",ret, nullptr == msg ? "" : msg, option_.channelId_, option_.deviceId_);
                goto cleanup;
            }

            return true;
        cleanup:
            hi_mpi_vdec_stop_recv_stream(option_.channelId_);
            hi_mpi_vdec_destroy_chn(option_.channelId_);
            option_.channelId_ = VCODEC_INVALID;
        }

        AIVCODEC_ERROR("initChannel failed, dev:%d", option_.deviceId_);
        return false;

    }

    hi_pixel_format Acl310PAiVideoDecoder::getPixelFormat(const ai::PixelFmt format)
    {
        switch (format)
        {
            case ai::PixelFmt::NV21:
                return HI_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
            case ai::PixelFmt::RGB24:
                return HI_PIXEL_FORMAT_RGB_888;
            case ai::PixelFmt::BGR24:
                return HI_PIXEL_FORMAT_BGR_888;
            case ai::PixelFmt::NV12:
            default:
                return HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420;
        }
    }
}
