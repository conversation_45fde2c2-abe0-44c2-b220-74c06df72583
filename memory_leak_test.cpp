#include <iostream>
#include <memory>
#include <chrono>
#include <thread>

// 模拟测试内存泄漏修复效果
// 这个测试程序用于验证修复后的内存管理是否正确

class MockDeviceFrame {
public:
    void* ptrs[3] = {nullptr, nullptr, nullptr};
    void* handle = nullptr;
    uint64_t frame_num = 0;
    
    ~MockDeviceFrame() {
        if (handle == nullptr) {
            // 模拟设备内存释放
            std::cout << "DeviceFrame destructor: releasing device memory" << std::endl;
            for (int i = 0; i < 3; i++) {
                if (ptrs[i]) {
                    std::cout << "  Freeing ptr[" << i << "]" << std::endl;
                    // 在实际代码中这里会调用 devFree(ptrs[i])
                    ptrs[i] = nullptr;
                }
            }
        } else {
            std::cout << "DeviceFrame destructor: handle is not null, skipping device memory release" << std::endl;
        }
    }
};

class MockDeviceFramePool {
private:
    int device_id_;
    
public:
    MockDeviceFramePool(int device_id) : device_id_(device_id) {}
    
    MockDeviceFrame* allocbuf() {
        auto frame = std::make_unique<MockDeviceFrame>();
        
        // 关键修复：设置handle为非nullptr，防止DeviceFrame析构函数释放设备内存
        frame->handle = this;
        
        // 模拟分配设备内存
        frame->ptrs[0] = malloc(1024);  // 模拟设备内存
        std::cout << "DeviceFramePool::allocbuf: allocated device memory, handle set to pool" << std::endl;
        
        return frame.release();
    }
    
    void freebuf(MockDeviceFrame* frame) {
        if (!frame) return;
        
        std::cout << "DeviceFramePool::freebuf: releasing device memory explicitly" << std::endl;
        
        // 显式释放设备内存
        for (int i = 0; i < 3; i++) {
            if (frame->ptrs[i]) {
                std::cout << "  Freeing ptr[" << i << "] in pool" << std::endl;
                free(frame->ptrs[i]);  // 在实际代码中这里会调用 DeviceFramePool::freeDeviceBuf
                frame->ptrs[i] = nullptr;
            }
        }
        
        delete frame;
        std::cout << "DeviceFramePool::freebuf: frame deleted" << std::endl;
    }
};

struct MockGstAiDeviceFrameMemory {
    MockDeviceFrame* buf;
};

class MockGstAiDeviceFrameAllocator {
public:
    static void free_memory(MockGstAiDeviceFrameMemory* memory) {
        std::cout << "GstAiDeviceFrameAllocator::free: starting" << std::endl;
        
        if (memory->buf) {
            std::cout << "GstAiDeviceFrameAllocator::free: deleting DeviceFrame" << std::endl;
            // 修复后的代码：直接删除DeviceFrame对象
            // DeviceFrame的析构函数会检查handle，如果handle不为nullptr则不会释放设备内存
            delete memory->buf;
            memory->buf = nullptr;
        }
        
        delete memory;
        std::cout << "GstAiDeviceFrameAllocator::free: completed" << std::endl;
    }
};

void test_memory_management() {
    std::cout << "\n=== Testing Memory Management Fix ===" << std::endl;
    
    // 创建内存池
    MockDeviceFramePool pool(0);
    
    // 从内存池分配DeviceFrame
    MockDeviceFrame* frame = pool.allocbuf();
    
    // 模拟GStreamer内存管理
    MockGstAiDeviceFrameMemory* gst_mem = new MockGstAiDeviceFrameMemory;
    gst_mem->buf = frame;
    
    std::cout << "\n--- Simulating GStreamer memory release ---" << std::endl;
    // 模拟GStreamer释放内存（修复后的行为）
    MockGstAiDeviceFrameAllocator::free_memory(gst_mem);
    
    std::cout << "\n--- Test completed ---" << std::endl;
}

void test_old_behavior() {
    std::cout << "\n=== Testing Old Behavior (with leak) ===" << std::endl;
    
    // 模拟旧的行为：handle为nullptr
    auto frame = std::make_unique<MockDeviceFrame>();
    frame->ptrs[0] = malloc(1024);
    frame->handle = nullptr;  // 旧行为：handle为nullptr
    
    std::cout << "Creating frame with handle = nullptr" << std::endl;
    
    // 模拟GStreamer内存管理（旧的有问题的行为）
    MockGstAiDeviceFrameMemory* gst_mem = new MockGstAiDeviceFrameMemory;
    gst_mem->buf = frame.release();
    
    std::cout << "\n--- Simulating old GStreamer memory release (commented out delete) ---" << std::endl;
    // 旧代码中被注释掉的部分，导致内存泄漏
    // delete gst_mem->buf;  // 这行被注释掉了！
    delete gst_mem;
    
    std::cout << "Memory leaked! DeviceFrame was not deleted." << std::endl;
}

int main() {
    std::cout << "Memory Leak Fix Verification Test" << std::endl;
    std::cout << "=================================" << std::endl;
    
    // 测试修复后的行为
    test_memory_management();
    
    // 测试旧的有问题的行为
    test_old_behavior();
    
    std::cout << "\n=== Summary ===" << std::endl;
    std::cout << "1. Fixed version: DeviceFrame handle is set to pool, preventing double-free" << std::endl;
    std::cout << "2. Fixed version: GstAiDeviceFrameAllocator properly deletes DeviceFrame" << std::endl;
    std::cout << "3. Old version: DeviceFrame was not deleted, causing memory leak" << std::endl;
    
    return 0;
}
